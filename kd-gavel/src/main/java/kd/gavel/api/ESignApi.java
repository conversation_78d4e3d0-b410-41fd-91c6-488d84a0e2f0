package kd.gavel.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import kd.common.context.RedisCommonKeyEnum;
import kd.common.enums.RetEnum;
import kd.common.tool.*;
import kd.entity.*;
import kd.gavel.service.*;
import kd.gavel.support.SignConfigInfo;
import kd.gavel.support.SignFlowStatusEnum;
import kd.gavel.support.SignInfo;
import kd.gavel.support.eSign.*;
import kd.gavel.util.HTTPHelper;
import kd.main.common.AliOssBucketPrefix;
import kd.main.common.ClaimCaseObjectStatusEnum;
import kd.main.common.ExRetEnum;
import kd.main.common.Tool;
import kd.main.util.HttpToolV3;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.*;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static kd.gavel.util.ESignTool.*;

/**
 * @Description: 对接e签宝接口
 * @Author: Andrea
 * @Date: 2024/9/17 14:16
 */
@RestController("/api/eSignApi")
@RequestMapping("/api/eSignApi")
public class ESignApi {
    private final static Logger logger = LoggerFactory.getLogger(ESignApi.class);

    @Autowired
    private SignatureTaskLogService signatureTaskLogService;

    @Autowired
    private SignatureTaskService signatureTaskService;

    @Autowired
    private SignatureTemplateService signaturaTemplateService;

    @Autowired
    private ChannelService channelService;

    /**
     * 发起填充任务
     * @param json
     * @return
     */
    @RequestMapping(value = "createByDocTemplate")
    @ResponseBody
    public String createByDocTemplate(@RequestBody String json) {
        logger.info(">>>>>>>>>方法/api/eSignApi/createByDocTemplate，开始填充信息，json：{}", json);
        Map<String, Object> resMap = new HashMap<>();

        SignInfo signInfo= JSON.parseObject(json,SignInfo.class);

        Channel channel=channelService.findByEntCode(signInfo.getEntCode());

        try{
            String domain= RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "DOMAIN");
            String url= RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "FILL_INFO_URL");
            String postUrl=domain+url;

            JSONObject reqBodyObj = new JSONObject();
            reqBodyObj.put("docTemplateId",signInfo.geteTemplateId());
            reqBodyObj.put("fileName",signInfo.getFileName());
            reqBodyObj.put("components",signInfo.getComponents());
            reqBodyObj.put("requiredCheck",signInfo.getRequiredCheck());

            String reqBodyData = reqBodyObj.toString();
            // 对请求Body体内的数据计算ContentMD5
            String contentMD5 = doContentMD5(reqBodyData);

            String method = "POST";
            String accept = "*/*";
            String contentType = "application/json";
            String date = "";
            String headers = "";
            String appId=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_ID");
            String appKey=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_KEY");

            StringBuffer sb = new StringBuffer();
            sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                    .append(contentType).append("\n").append(date).append("\n");
            if ("".equals(headers)) {
                sb.append(headers).append(url);
            } else {
                sb.append(headers).append("\n").append(url);
            }

            // 构建参与请求签名计算的明文
            String plaintext = sb.toString();
            // 计算请求签名值
            String reqSignature = doSignatureBase64(plaintext, appKey);

            long timeStamp = new Date().getTime();

            // 构建请求头
            LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
            header.put("X-Tsign-Open-App-Id", appId);
            header.put("X-Tsign-Open-Auth-Mode", "Signature");
            header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(timeStamp));
            header.put("Accept", accept);
            header.put("Content-Type", contentType);
            header.put("X-Tsign-Open-Ca-Signature", reqSignature);
            header.put("Content-MD5", contentMD5);

            logger.info(">>>>>>>>>>>>>>>>>>请求头：{}", header);
            logger.info(">>>>>>>>>>>>>>>>>>信息填充接口请求：{}", reqBodyData);
            String result = HTTPHelper.sendPOST(postUrl, reqBodyData, header, "UTF-8");
            logger.info(">>>>>>>>>>>>>>>>>>信息填充接口返回：{}", result);
            JSONObject resObj= JSON.parseObject(result);
            Map<String,String> callBackMap=new HashMap<>();
            if(resObj==null || !"0".equals(resObj.getString("code"))){
                SignatureTaskLog signatureTaskLog =new SignatureTaskLog();
                signatureTaskLog.setChannelId(channel.getId());
                signatureTaskLog.setTaskId(signInfo.getSingnatureTaskId());
                signatureTaskLog.setId(Tool.uuid());
                signatureTaskLog.setReqData(reqBodyData);
                signatureTaskLog.setResData((resObj == null ? "null" : result));
                signatureTaskLog.setDescription("信息填充任务失败");
                signatureTaskLog.setCreateTime(new Date());
                signatureTaskLogService.insertSelective(signatureTaskLog);

                SignatureTask signatureTask1 =signatureTaskService.selectByPrimaryKey(signInfo.getSingnatureTaskId());
//                singnatureTask.setFileId(resObj.getJSONObject("data").getString("fileId"));
                signatureTask1.setStatus(-1);
                signatureTaskService.updateByPrimaryKeySelective(signatureTask1);

                SignatureTask signatureTask=signatureTaskService.selectByPrimaryKey(signInfo.getSingnatureTaskId());

                callBackMap.put("msg",resObj.getString("message"));

                Map<String,Object> returnMap=new HashMap<>();
                returnMap.put("code","2");
                returnMap.put("callBackUrl",signatureTask.getCallback());
                returnMap.put("result",JsonTool.genByFastJson(callBackMap));
                returnMap.put("signFileId",signatureTask.getId());

                returnInfo(JsonTool.genByFastJson(returnMap));

                resMap.put("ret", "-1");
                resMap.put("msg", "信息填充任务失败");
                return JsonTool.genByFastJson(resMap);
            }else {
                SignatureTaskLog signatureTaskLog =new SignatureTaskLog();
                signatureTaskLog.setChannelId(channel.getId());
                signatureTaskLog.setTaskId(signInfo.getSingnatureTaskId());
                signatureTaskLog.setId(Tool.uuid());
                signatureTaskLog.setReqData(reqBodyData);
                signatureTaskLog.setResData(result);
                signatureTaskLog.setDescription("信息填充任务成功");
                signatureTaskLog.setCreateTime(new Date());
                signatureTaskLogService.insertSelective(signatureTaskLog);

                SignatureTask signatureTask =signatureTaskService.selectByPrimaryKey(signInfo.getSingnatureTaskId());
                signatureTask.setFileId(resObj.getJSONObject("data").getString("fileId"));
                signatureTask.setStatus(1);
                signatureTaskService.updateByPrimaryKeySelective(signatureTask);

//                String taskId=Tool.uuid();
//                String objId = AliOssBucketPrefix.genFullObject(AliOssBucketPrefix.KD_E_SIGN, taskId, Tool.uuid());
//                SignFileTask signFileTask=new SignFileTask();
//                signFileTask.setId(taskId);
//                signFileTask.setObjId(objId);
//                signFileTask.setOuterUrl(resObj.getJSONObject("data").getString("fileDownloadUrl"));
//                signFileTask.setStatus(0);
//                signFileTask.setCreateTime(new Date());
//                signFileTask.setFormatterType(0);
//                signFileTask.setCreator("-1");
//                signFileTaskService.insertSelective(signFileTask);

//                String res1=upload(taskId);
//                logger.info(res1);

                callBackMap.put("msg",resObj.getString("message"));
                callBackMap.put("url",resObj.getJSONObject("data").getString("fileDownloadUrl"));

                Map<String,Object> returnMap=new HashMap<>();
                returnMap.put("code","1");
                returnMap.put("callBackUrl",signatureTask.getCallback());
                returnMap.put("result",JsonTool.genByFastJson(callBackMap));
                returnMap.put("signFileId",signatureTask.getId());

                returnInfo(JsonTool.genByFastJson(returnMap));

                String res=createByFile(json);
                logger.info(res);
                JSONObject resObject=JSONObject.parseObject(res);
                if("-1".equals(resObject.getString("ret"))){
                    return res;
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(">>>>>>>>>请求/api/eSignApi/createByDocTemplate，系统异常，请联系管理员！");
            SignatureTaskLog signatureTaskLog =new SignatureTaskLog();
            signatureTaskLog.setChannelId(channel.getId());
            signatureTaskLog.setTaskId(signInfo.getSingnatureTaskId());
            signatureTaskLog.setId(Tool.uuid());
            signatureTaskLog.setReqData(json);
//            signatureTaskLog.setResData((resObj == null ? "null" : result));
            signatureTaskLog.setDescription("信息填充_系统异常");
            signatureTaskLog.setCreateTime(new Date());
            signatureTaskLogService.insertSelective(signatureTaskLog);
            return JsonBizTool.genJson(RetEnum.FAIL);
        }
        return JsonBizTool.genJson(RetEnum.SUCCESS);
    }

    /**
     * 发起签署
     * @param json
     * @return
     */
    @RequestMapping(value = "createByFile")
    @ResponseBody
    public String createByFile(@RequestBody String json){
        logger.info(">>>>>>>>>方法/api/eSignApi/createByFile，发起签署，json：{}", json);
        Map<String, Object> resMap = new HashMap<>();

        SignInfo signInfo= JSON.parseObject(json,SignInfo.class);

        Channel channel=channelService.findByEntCode(signInfo.getEntCode());
        try{
            String domain= RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "DOMAIN");
            String url= RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "SEND_FILE");
            String postUrl=domain+url;

            LocalDate currentDate = LocalDate.now();

            SignatureTask signatureTask =signatureTaskService.selectByPrimaryKey(signInfo.getSingnatureTaskId());

            SignatureTemplate signatureTemplate=signaturaTemplateService.selectByPrimaryKey(signatureTask.getTemplateId());

            //todo:获取发起人id
            String psnId=null;
            if(StringUtils.isNotBlank(signInfo.getPsnMobile())){
                logger.info(">>>>>>>>>>>>>>>>>>手机号：{}", signInfo.getPsnMobile());
                String psnIdMobile=psnMobileJson(signInfo.getPsnMobile());
                JSONObject jsonObject = JSON.parseObject(psnIdMobile);
                String data = jsonObject.getString("data");

                JSONObject jsonObject2 = JSON.parseObject(data);
                psnId = jsonObject2.getString("psnId");
            }else{
                logger.info(">>>>>>>>>>>>>>>>>>手机号为空：{}", signInfo.getPsnMobile());
                psnId="e2fbe1fc91f7453c9961561590de6e14";
            }


            SignConfigInfo signConfigInfo=JSON.parseObject(signatureTemplate.getConfigJson(),SignConfigInfo.class);
            logger.info(">>>>>>>>>>>>>>>>>>手机号 psnId：{}", psnId);
            if(StringUtils.isNotEmpty(psnId)){
                logger.info(">>>>>>>发起人信息修改 signConfigInfo.getSignFlowInitiator()：{}，signConfigInfo.getSignFlowInitiator().getPsnInitiator(){}", signConfigInfo.getSignFlowInitiator(),signConfigInfo.getSignFlowInitiator().getPsnInitiator());
                signConfigInfo.getSignFlowInitiator().getOrgInitiator().getTransactor().setPsnId(psnId);
            }

            List<Doc> docList=new ArrayList<>();
            Doc doc=new Doc();
            doc.setFileId(signatureTask.getFileId());
            // todo:文件后缀类型，待定，有word
            doc.setFileName(signInfo.getFileName()+".pdf");
            doc.setOrder(1);
            docList.add(doc);
            signConfigInfo.setDocs(docList);

            signConfigInfo.getSignFlowConfig().setSignFlowTitle(signInfo.getFileTitle());
            signConfigInfo.getSignFlowConfig().setSignFlowExpireTime(DateUtils.parse(currentDate.plusDays(30).toString()).getTime());
            signConfigInfo.getSignFlowConfig().setNotifyUrl( RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "CALL_BACK_URL"));

            List<Signer> signers=signConfigInfo.getSigners();
            List<Person> personList=signInfo.getPersonInfo();
            if(signers.size()!=personList.size()){
                resMap.put("ret", "-1");
                resMap.put("msg", "签署方信息与模板不匹配");
                return JsonTool.genByFastJson(resMap);
            }

            for(int i=0; i<signers.size(); i++) {
                String seqNo=Integer.toString(i+1);
                List<Person> person=personList.stream()
                        .filter(Person -> seqNo.equals(Person.getSeqNo()))
                        .collect(Collectors.toList());

                PsnSignerInfo psnSignerInfo=new PsnSignerInfo();
                psnSignerInfo.setPsnAccount(person.get(0).getPersonPhone());
                PsnInfo psnInfo=new PsnInfo();
                psnInfo.setPsnName(person.get(0).getPersonName());
                psnInfo.setPsnIDCardNum(person.get(0).getPersonIdNumber());
                psnInfo.setPsnIDCardType(person.get(0).getPersonIdType());
                psnSignerInfo.setPsnInfo(psnInfo);
                signConfigInfo.getSigners().get(i).setPsnSignerInfo(psnSignerInfo);

                signConfigInfo.getSigners().get(i).getSignFields().get(0).setFileId(signatureTask.getFileId());
            }


            String reqBodyData = JSON.toJSONString(signConfigInfo);
            // 对请求Body体内的数据计算ContentMD5
            String contentMD5 = doContentMD5(reqBodyData);

            String method = "POST";
            String accept = "*/*";
            String contentType = "application/json";
            String date = "";
            String headers = "";
            String appId=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_ID");
            String appKey=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_KEY");

            StringBuffer sb = new StringBuffer();
            sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                    .append(contentType).append("\n").append(date).append("\n");
            if ("".equals(headers)) {
                sb.append(headers).append(url);
            } else {
                sb.append(headers).append("\n").append(url);
            }

            // 构建参与请求签名计算的明文
            String plaintext = sb.toString();
            // 计算请求签名值
            String reqSignature = doSignatureBase64(plaintext, appKey);

            long timeStamp = new Date().getTime();

            // 构建请求头
            LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
            header.put("X-Tsign-Open-App-Id", appId);
            header.put("X-Tsign-Open-Auth-Mode", "Signature");
            header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(timeStamp));
            header.put("Accept", accept);
            header.put("Content-Type", contentType);
            header.put("X-Tsign-Open-Ca-Signature", reqSignature);
            header.put("Content-MD5", contentMD5);

            logger.info(">>>>>>>>>>>>>>>>>>请求头：{}", header);
            logger.info(">>>>>>>>>>>>>>>>>>发起签署接口请求：{}", reqBodyData);
            String result = HTTPHelper.sendPOST(postUrl, reqBodyData, header, "UTF-8");
            logger.info(">>>>>>>>>>>>>>>>>>发起签署接口返回：{}", result);
            JSONObject resObj= JSON.parseObject(result);
            Map<String,String> callBackMap=new HashMap<>();
            if(resObj==null || !"0".equals(resObj.getString("code"))){
                SignatureTaskLog signatureTaskLog =new SignatureTaskLog();
                signatureTaskLog.setChannelId(channel.getId());
                signatureTaskLog.setTaskId(signInfo.getSingnatureTaskId());
                signatureTaskLog.setId(Tool.uuid());
                signatureTaskLog.setReqData(reqBodyData);
                signatureTaskLog.setResData((resObj == null ? "null" : result));
                signatureTaskLog.setDescription("发起签署失败");
                signatureTaskLog.setCreateTime(new Date());
                signatureTaskLogService.insertSelective(signatureTaskLog);

                SignatureTask signatureTask1 =signatureTaskService.selectByPrimaryKey(signInfo.getSingnatureTaskId());
//                singnatureTask.setFileId(resObj.getJSONObject("data").getString("fileId"));
                signatureTask1.setStatus(-2);
                signatureTaskService.updateByPrimaryKeySelective(signatureTask1);

                callBackMap.put("msg",resObj.getString("message"));

                Map<String,Object> returnMap=new HashMap<>();
                returnMap.put("code","4");
                returnMap.put("callBackUrl",signatureTask.getCallback());
                returnMap.put("result",JsonTool.genByFastJson(callBackMap));
                returnMap.put("signFileId",signatureTask.getId());

                returnInfo(JsonTool.genByFastJson(returnMap));

                resMap.put("ret", "-1");
                resMap.put("msg", "发起签署失败");
                return JsonTool.genByFastJson(resMap);
            }else {
                SignatureTaskLog signatureTaskLog =new SignatureTaskLog();
                signatureTaskLog.setChannelId(channel.getId());
                signatureTaskLog.setTaskId(signInfo.getSingnatureTaskId());
                signatureTaskLog.setId(Tool.uuid());
                signatureTaskLog.setReqData(reqBodyData);
                signatureTaskLog.setResData(result);
                signatureTaskLog.setDescription("发起签署成功");
                signatureTaskLog.setCreateTime(new Date());
                signatureTaskLogService.insertSelective(signatureTaskLog);

                SignatureTask signatureTask1 =signatureTaskService.selectByPrimaryKey(signInfo.getSingnatureTaskId());
//                singnatureTask.setFileId(resObj.getJSONObject("data").getString("fileId"));
                signatureTask1.setStatus(2);
                signatureTask1.setSignFlowId(resObj.getJSONObject("data").getString("signFlowId"));
                signatureTaskService.updateByPrimaryKeySelective(signatureTask1);

                callBackMap.put("msg",resObj.getString("message"));

                Map<String,Object> returnMap=new HashMap<>();
                returnMap.put("code","3");
                returnMap.put("callBackUrl",signatureTask.getCallback());
                returnMap.put("result",JsonTool.genByFastJson(callBackMap));
                returnMap.put("signFileId",signatureTask.getId());

                returnInfo(JsonTool.genByFastJson(returnMap));
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(">>>>>>>>>请求/api/eSignApi/createByFile，系统异常，请联系管理员！");
            SignatureTaskLog signatureTaskLog =new SignatureTaskLog();
            signatureTaskLog.setChannelId(channel.getId());
            signatureTaskLog.setTaskId(signInfo.getSingnatureTaskId());
            signatureTaskLog.setId(Tool.uuid());
            signatureTaskLog.setReqData(json);
//            signatureTaskLog.setResData(result);
            signatureTaskLog.setDescription("发起签署_系统异常");
            signatureTaskLog.setCreateTime(new Date());
            signatureTaskLogService.insertSelective(signatureTaskLog);
            return JsonBizTool.genJson(RetEnum.FAIL);
        }

        return JsonBizTool.genJson(RetEnum.SUCCESS);
    }

    /**
     * e签宝回传
     * @param json
     * @return
     */
    @RequestMapping(value = "callBack")
    @ResponseBody
    public String callBack(@RequestBody String json){
        logger.info(">>>>>>>>>>>>>>>>>>签署回调：{}", json);

        JSONObject reqBodyObj = JSON.parseObject(json);
        try {
            String action=reqBodyObj.getString("action");
            Map<String,Object> returnMap=new HashMap<>();
            if("SIGN_FLOW_COMPLETE".equals(action)){
                String signFlowId=reqBodyObj.getString("signFlowId");
                SignatureTask signatureTask=signatureTaskService.findBySignFlowId(signFlowId);
                SignatureTemplate signatureTemplate=signaturaTemplateService.selectByPrimaryKey(signatureTask.getTemplateId());
                Channel channel=channelService.selectByPrimaryKey(signatureTemplate.getChannelId());
                SignatureTaskLog signatureTaskLog =new SignatureTaskLog();
                signatureTaskLog.setChannelId(signatureTemplate.getChannelId());
                signatureTaskLog.setTaskId(signatureTask.getId());
                signatureTaskLog.setId(Tool.uuid());
    //            signatureTaskLog.setReqData();
                signatureTaskLog.setResData(json);
                signatureTaskLog.setDescription(SignFlowStatusEnum.codeToName(reqBodyObj.getString("signFlowStatus")));
                signatureTaskLog.setCreateTime(new Date());
                signatureTaskLogService.insertSelective(signatureTaskLog);

                int psnCount=signatureTask.getSuccessQuantity()+1;
                signatureTask.setSuccessQuantity(psnCount);
                if(psnCount==signatureTask.getPersonQuantity()){
                    signatureTask.setStatus(3);
                }
                signatureTaskService.updateByPrimaryKeySelective(signatureTask);

                int count=channel.getSurplusSignatureQuota()-1;
                channel.setSurplusSignatureQuota(count);
                channelService.updateByPrimaryKeySelective(channel);

//                String taskId=Tool.uuid();
//                String objId = AliOssBucketPrefix.genFullObject(AliOssBucketPrefix.KD_E_SIGN, taskId, Tool.uuid());
//                SignFileTask signFileTask=new SignFileTask();
//                signFileTask.setId(taskId);
//                signFileTask.setObjId(objId);
//                signFileTask.setStatus(0);
//                signFileTask.setCreateTime(new Date());
//                signFileTask.setFormatterType(0);
//                signFileTask.setCreator("-1");
//                signFileTaskService.insertSelective(signFileTask);

//                reqBodyObj.put("taskId",taskId);

//                String res =fileDownload(JsonTool.genByFastJson(reqBodyObj));

                returnMap.put("code",SignFlowStatusEnum.codeToStatus(reqBodyObj.getString("signFlowStatus")));
                returnMap.put("callBackUrl",signatureTask.getCallback());
                returnMap.put("signFileId",signatureTask.getId());
                returnMap.put("result","");
                returnInfo(JsonTool.genByFastJson(returnMap));

            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(">>>>>>>>>请求/api/eSignApi/callBack，系统异常，请联系管理员！");
            return JsonBizTool.genJson(RetEnum.FAIL);
        }

        return JsonBizTool.genJson(RetEnum.SUCCESS);
    }

    @RequestMapping(value = "findOrgId")
    @ResponseBody
    public String findOrgId(String param) {
        try {
            String host= RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "DOMAIN");
            String getUrl= RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "FIND_ORG_ID");

            String encodedParam = URLEncoder.encode(param, "UTF-8");

            String getALLUrl=host+getUrl+encodedParam;

            HashMap<String,Object> reqBodyObj = new HashMap<>();

            String reqBodyData = reqBodyObj.toString();
            // 对请求Body体内的数据计算ContentMD5
            String contentMD5 = doContentMD5(reqBodyData);

            String method = "GET";
            String accept = "*/*";
            String contentType = "application/json";
            String url = getUrl+param;
            String date = "";
            String headers = "";
            String appId=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_ID");
            String appKey=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_KEY");

            StringBuffer sb = new StringBuffer();
            sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                    .append(contentType).append("\n").append(date).append("\n");
            if ("".equals(headers)) {
                sb.append(headers).append(url);
            } else {
                sb.append(headers).append("\n").append(url);
            }

            // 构建参与请求签名计算的明文
            String plaintext = sb.toString();
            // 计算请求签名值
            String reqSignature = doSignatureBase64(plaintext, appKey);

            long timeStamp = new Date().getTime();

            // 构建请求头
            LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
            header.put("X-Tsign-Open-App-Id", appId);
            header.put("X-Tsign-Open-Auth-Mode", "Signature");
            header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(timeStamp));
            header.put("Accept", accept);
            header.put("Content-Type", contentType);
            header.put("X-Tsign-Open-Ca-Signature", reqSignature);
            header.put("Content-MD5", contentMD5);

            String result = HTTPHelper.sendGet(getALLUrl, reqBodyObj, header, "UTF-8");
            logger.info(">>>>>>>>>>>>>>>>>>查询接口返回：{}", result);
            JSONObject resObj= JSON.parseObject(result);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(">>>>>>>>>请求/api/eSignApi/findOrgId，系统异常，请联系管理员！");
            return  JsonBizTool.genJson(RetEnum.FAIL);
        }
    }

    @RequestMapping(value = "findPsnId")
    @ResponseBody
    public String findPsnId(String param) {
        try {
            String host= RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "DOMAIN");
            String getUrl= RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "FIND_PSN_ID");

            String getALLUrl=host+getUrl+param;

            HashMap<String,Object> reqBodyObj = new HashMap<>();

            String reqBodyData = reqBodyObj.toString();
            // 对请求Body体内的数据计算ContentMD5
            String contentMD5 = doContentMD5(reqBodyData);

            String method = "GET";
            String accept = "*/*";
            String contentType = "application/json";
            String url = getUrl+param;
            String date = "";
            String headers = "";
            String appId=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_ID");
            String appKey=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_KEY");

            StringBuffer sb = new StringBuffer();
            sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                    .append(contentType).append("\n").append(date).append("\n");
            if ("".equals(headers)) {
                sb.append(headers).append(url);
            } else {
                sb.append(headers).append("\n").append(url);
            }

            // 构建参与请求签名计算的明文
            String plaintext = sb.toString();
            // 计算请求签名值
            String reqSignature = doSignatureBase64(plaintext, appKey);

            long timeStamp = new Date().getTime();

            // 构建请求头
            LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
            header.put("X-Tsign-Open-App-Id", appId);
            header.put("X-Tsign-Open-Auth-Mode", "Signature");
            header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(timeStamp));
            header.put("Accept", accept);
            header.put("Content-Type", contentType);
            header.put("X-Tsign-Open-Ca-Signature", reqSignature);
            header.put("Content-MD5", contentMD5);

            String result = HTTPHelper.sendGet(getALLUrl, reqBodyObj, header, "UTF-8");
            logger.info(">>>>>>>>>>>>>>>>>>查询接口返回：{}", result);
            JSONObject resObj = JSON.parseObject(result);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(">>>>>>>>>请求/api/eSignApi/findPsnId，系统异常，请联系管理员！");
            return  JsonBizTool.genJson(RetEnum.FAIL);
        }
    }

    /**
     * 回传系统
     * @param json
     */
    @RequestMapping(value = "returnInfo")
    @ResponseBody
    public void returnInfo(@RequestBody String json){
        logger.info(">>>>>>>>>方法/api/eSignApi/returnInfo，信息回传：{}", json);
        URL url = null;
        try {
            JSONObject reqJson=JSONObject.parseObject(json);
            String callBackUrl=reqJson.getString("callBackUrl");
            int index = callBackUrl.indexOf("?");
            String reqUrl=callBackUrl.substring(0, index);
            url = new URL(callBackUrl);

            // 获取查询字符串
            String query = url.getQuery();

            // 存储查询参数的Map
            Map<String, String> queryParams = new HashMap<>();

            // 如果查询字符串不为空，解析参数
            if (query != null && !query.isEmpty()) {
                // 将查询字符串分割成参数对
                String[] pairs = query.split("&");

                for (String pair : pairs) {
                    // 分割键和值
                    int idx = pair.indexOf("=");
                    String key = pair.substring(0, idx);
                    String value = pair.substring(idx + 1);

                    // 将键值对添加到Map中
                    queryParams.put(key, value);
                }
            }
            queryParams.put("result",reqJson.getString("result"));
            queryParams.put("code",reqJson.getString("code"));
            if(StringUtils.isNotBlank(reqJson.getString("signFileId"))){
                queryParams.put("signFileId",reqJson.getString("signFileId"));
            }

            String res = HttpToolV3.callApiByPostStr(true,reqUrl,JsonTool.genByFastJson(queryParams));

            JSONObject resObj = JSONObject.parseObject(res);

            logger.info(JsonTool.genByFastJson(queryParams));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(">>>>>>>>>请求/api/eSignApi/returnInfo，系统异常，请联系管理员！");
        }
    }

    /**
     * 查询填充完成文件
     * @param json
     * @return
     */
    @RequestMapping("findFillFile")
    @ResponseBody
    public String findFillFile(@RequestBody String json){
        logger.info(">>>>>>>>>>>>>>>>>>下载填充后文件：{}", json);
        try {

            JSONObject reqObj = JSON.parseObject(json);

            String taskId=reqObj.getString("taskId");

            SignatureTask signatureTask=signatureTaskService.selectByPrimaryKey(taskId);

            String host= RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "DOMAIN");
            String getUrl="/v3/files/"+signatureTask.getFileId();
            HashMap<String,Object> reqBodyObj = new HashMap<>();

            String reqBodyData = reqBodyObj.toString();
            // 对请求Body体内的数据计算ContentMD5
            String contentMD5 = doContentMD5(reqBodyData);

            String method = "GET";
            String accept = "*/*";
            String contentType = "application/json";
            String url =getUrl;
            String date = "";
            String headers = "";
            String appId=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_ID");
            String appKey=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_KEY");

            StringBuffer sb = new StringBuffer();
            sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                    .append(contentType).append("\n").append(date).append("\n");
            if ("".equals(headers)) {
                sb.append(headers).append(url);
            } else {
                sb.append(headers).append("\n").append(url);
            }

            // 构建参与请求签名计算的明文
            String plaintext = sb.toString();
            // 计算请求签名值
            String reqSignature = doSignatureBase64(plaintext, appKey);

            long timeStamp = new Date().getTime();

            // 构建请求头
            LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
            header.put("X-Tsign-Open-App-Id", appId);
            header.put("X-Tsign-Open-Auth-Mode", "Signature");
            header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(timeStamp));
            header.put("Accept", accept);
            header.put("Content-Type", contentType);
            header.put("X-Tsign-Open-Ca-Signature", reqSignature);
            header.put("Content-MD5", contentMD5);

            String result = HTTPHelper.sendGet(host+getUrl, reqBodyObj, header, "UTF-8");
            logger.info(">>>>>>>>>>>>>>>>>>下载填充文件返回：{}", result);
            JSONObject resObj= JSON.parseObject(result);
            Map<String, Object> resMap = new HashMap<>();
            if(!"0".equals(resObj.getString("code"))){
                resMap.put("ret", "-1");
                resMap.put("msg", resObj.getString("message"));
            }else{
                JSONObject data=JSON.parseObject(resObj.getString("data"));
                resMap.put("ret", "0");
                resMap.put("url", data.getString("fileDownloadUrl"));
            }
            logger.error(">>>>>>>>>/api/eSignApi/findFillFile，返回:"+JsonTool.genByFastJson(resMap));
            return  JsonTool.genByFastJson(resMap);
        }catch (Exception e){
            e.printStackTrace();
            logger.error(">>>>>>>>>请求/api/eSignApi/findFillFile，系统异常，请联系管理员！");
            return JsonBizTool.genJson(RetEnum.FAIL);
        }


    }

    /**
     * 查询签署完成文件
     * @param json
     * @return
     */
    @RequestMapping(value = "findSignFile")
    @ResponseBody
    public String findSignFile(@RequestBody String json) {
        logger.info(">>>>>>>>>>>>>>>>>>下载签署文件：{}", json);

        try {
            JSONObject reqObj = JSON.parseObject(json);
            String taskId=reqObj.getString("taskId");

            SignatureTask signatureTask=signatureTaskService.selectByPrimaryKey(taskId);
            String host= RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "DOMAIN");
            String getUrl="/v3/sign-flow/"+signatureTask.getSignFlowId()+"/file-download-url?signFlowId="+signatureTask.getSignFlowId();

            HashMap<String,Object> reqBodyObj = new HashMap<>();

            String reqBodyData = reqBodyObj.toString();
            // 对请求Body体内的数据计算ContentMD5
            String contentMD5 = doContentMD5(reqBodyData);

            String method = "GET";
            String accept = "*/*";
            String contentType = "application/json";
            String url =getUrl;
            String date = "";
            String headers = "";
            String appId=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_ID");
            String appKey=RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_KEY");

            StringBuffer sb = new StringBuffer();
            sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                    .append(contentType).append("\n").append(date).append("\n");
            if ("".equals(headers)) {
                sb.append(headers).append(url);
            } else {
                sb.append(headers).append("\n").append(url);
            }

            // 构建参与请求签名计算的明文
            String plaintext = sb.toString();
            // 计算请求签名值
            String reqSignature = doSignatureBase64(plaintext, appKey);

            long timeStamp = new Date().getTime();

            // 构建请求头
            LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
            header.put("X-Tsign-Open-App-Id", appId);
            header.put("X-Tsign-Open-Auth-Mode", "Signature");
            header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(timeStamp));
            header.put("Accept", accept);
            header.put("Content-Type", contentType);
            header.put("X-Tsign-Open-Ca-Signature", reqSignature);
            header.put("Content-MD5", contentMD5);

            String result = HTTPHelper.sendGet(host+getUrl, reqBodyObj, header, "UTF-8");
            logger.info(">>>>>>>>>>>>>>>>>>下载签署文件返回：{}", result);
            JSONObject resObj= JSON.parseObject(result);
            Map<String, Object> resMap = new HashMap<>();
            if(!"0".equals(resObj.getString("code"))){
                resMap.put("ret", "-1");
                resMap.put("msg", "文件信息不存在!");
            }else{
                JSONObject data=JSON.parseObject(resObj.getString("data"));
                JSONObject files= (JSONObject) JSON.parseArray(data.getString("files")).get(0);
                resMap.put("ret", "0");
                resMap.put("url", files.getString("downloadUrl"));
            }
            logger.error(">>>>>>>>>/api/eSignApi/findSignFile，返回:"+JsonTool.genByFastJson(resMap));
            return  JsonTool.genByFastJson(resMap);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(">>>>>>>>>请求/api/eSignApi/findSignFile，系统异常，请联系管理员！");
            return JsonBizTool.genJson(RetEnum.FAIL);
        }

    }

    public  String psnMobileJson(String psnMobile){
        String gavelFindPsnIdURL = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "GAVEL_FIND_PSN_ID");
        if(StringUtils.isBlank(gavelFindPsnIdURL)){
            gavelFindPsnIdURL="http://gavel.kedongnet.com/api/eSignApi/findPsnId?param=";
        }
        String urlString = gavelFindPsnIdURL+psnMobile; // 替换为你的API链接
        String json=null;
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            InputStream inputStream;
            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                inputStream = connection.getInputStream();
            } else {
                inputStream = connection.getErrorStream();
            }

            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder stringBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }

            inputStream.close();
            connection.disconnect();

            // 这里的stringBuilder.toString()就是从API获取的JSON字符串
            System.out.println(stringBuilder.toString());
            json=stringBuilder.toString();
            return json;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取签署链接
     * @param json
     * @return
     */
    @RequestMapping(value = "getSignLink")
    @ResponseBody
    public String getSignLink(@RequestBody String json) {
        logger.info(">>>>>>>>>方法/api/eSignApi/getSignLink，获取签署链接，json：{}", json);
        JSONObject jsonObject = JSONObject.parseObject(json);

        String signatureTaskId = jsonObject.getString("signatureTaskId");

        SignatureTask signatureTask = signatureTaskService.selectByPrimaryKey(signatureTaskId);
        String signFlowId = signatureTask.getSignFlowId();

        Map<String, Object> resMap = new HashMap<>();

        // 检查 signFlowId 是否为空
        if (StringUtils.isBlank(signFlowId)) {
            resMap.put("ret", "-1");
            resMap.put("msg", "signFlowId 不能为空");
            return JsonTool.genByFastJson(resMap);
        }


        // 获取签署人信息   适配多个签署人
        List<Person> personList = JSONArray.parseArray(signatureTask.getPersonInfo(), Person.class);

        List<String> resultList = new ArrayList<>();

        for (Person person : personList) {
            Map<String, Object> pushMap = new HashMap<>();
            Map<String, Object> operator = new HashMap<>();
            String personPhone = person.getPersonPhone();
            operator.put("psnAccount", personPhone);
            pushMap.put("operator", operator);
            pushMap.put("needLogin", true); // 是否需要登录打开链接

            String link = getESignLink(signFlowId, pushMap, json);
            JSONObject linkObj = JSONObject.parseObject(link);
            linkObj.put("personName", person.getPersonName());

            resultList.add(JsonTool.genByFastJson(linkObj));
        }

        return JsonTool.genByFastJson(resultList);

    }

    public String getESignLink(String signFlowId,Map<String, Object> pushMap,String json){
        try {
            Map<String, Object> resMap = new HashMap<>();

            String domain = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "DOMAIN");
            String urlTemplate = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "GET_SIGN_LINK_URL");
            String url = String.format(urlTemplate, signFlowId);
            String postUrl = domain + url;

            logger.info(">>>>>>>>>>>>>>发起请求地址：" + postUrl);

            String reqBodyData = JsonBizTool.genJson(pushMap);
            // 对请求Body体内的数据计算ContentMD5
            String contentMD5 = doContentMD5(reqBodyData);

            String method = "POST";
            String accept = "*/*";
            String contentType = "application/json";
            String date = "";
            String headers = "";
            String appId = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_ID");
            String appKey = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_KEY");

            StringBuffer sb = new StringBuffer();
            sb.append(method).append("\n")
                    .append(accept).append("\n")
                    .append(contentMD5).append("\n")
                    .append(contentType).append("\n")
                    .append(date).append("\n");
            if ("".equals(headers)) {
                sb.append(headers).append(url);
            } else {
                sb.append(headers).append("\n").append(url);
            }
            // 构建参与请求签名计算的明文
            String plaintext = sb.toString();
            // 计算请求签名值
            String reqSignature = doSignatureBase64(plaintext, appKey);

            // 构建请求头
            LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
            header.put("X-Tsign-Open-App-Id", appId);
            header.put("X-Tsign-Open-Auth-Mode", "Signature");
            header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(new Date().getTime()));
            header.put("Accept", accept);
            header.put("Content-Type", contentType);
            header.put("X-Tsign-Open-Ca-Signature", reqSignature);
            header.put("Content-MD5", contentMD5);

            logger.info(">>>>>>>>>>>>>>>>>>请求头：{}", header);
            logger.info(">>>>>>>>>>>>>>>>>>获取签署链接接口请求：{}", reqBodyData);
            String result = HTTPHelper.sendPOST(postUrl, reqBodyData, header, "UTF-8");
            logger.info(">>>>>>>>>>>>>>>>>>获取签署链接接口返回：{}", result);

            JSONObject resObj = JSON.parseObject(result);
            resMap.put("data", resObj);
            if (resObj == null || !"0".equals(resObj.getString("code"))) {
                resMap.put("ret", "-1");
                resMap.put("msg", "获取签署链接失败");
                return JsonTool.genByFastJson(resMap);
            } else {
                // 将签署链接添加到响应结果 Map 中
                resMap.put("ret", "0");
                resMap.put("msg", "获取签署链接成功");
                // 返回成功响应结果的 JSON 字符串
                return JsonTool.genByFastJson(resMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(">>>>>>>>>请求/api/eSignApi/getSignLink，系统异常，请联系管理员！");
            SignatureTaskLog signatureTaskLog = new SignatureTaskLog();
            signatureTaskLog.setId(Tool.uuid());
            signatureTaskLog.setReqData(json);
            signatureTaskLog.setDescription("获取签署链接异常");
            signatureTaskLog.setCreateTime(new Date());
            signatureTaskLogService.insertSelective(signatureTaskLog);
            return JsonBizTool.genJson(RetEnum.FAIL);
        }

    }

    /**
     * 协议撤回
     * @param json
     * @return
     */
    @RequestMapping(value = "agreementWithdrawn")
    @ResponseBody
    public String agreementWithdrawnApi(@RequestBody String json){
        logger.info(">>>>>>>>>方法/api/signTemplateApi/agreementWithdrawn，开始协议撤回，json：{}", json);
        try {
            Map<String, Object> pushMap = new HashMap<>();
            Map<String, Object> resMap = new HashMap<>();

            JSONObject jsonObject = JSONObject.parseObject(json);
            String signatureTaskId = jsonObject.getString("signatureTaskId");
            String reason = jsonObject.getString("reason");
            String claimCaseObjectId = jsonObject.getString("claimCaseObjectId");

            SignatureTask signatureTask = signatureTaskService.selectByPrimaryKey(signatureTaskId);
            String signFlowId = signatureTask.getSignFlowId();
            // 检查 signFlowId 是否为空
            if (StringUtils.isBlank(signFlowId)) {
                resMap.put("ret", "-1");
                resMap.put("msg", "signFlowId 不能为空");
                return JsonTool.genByFastJson(resMap);
            }
            pushMap.put("signFlowId",  signFlowId);
            pushMap.put("revokeReason", reason);

            String domain = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "DOMAIN");
            String urlTemplate = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "AGREEMENT_WITHDRAWN_URL");
            String url = String.format(urlTemplate, signFlowId);
            String postUrl = domain + url;

            logger.info(">>>>>>>>>>>>>>协议撤回 发起请求地址：" + postUrl);


            String reqBodyData = JsonBizTool.genJson(pushMap);
            // 对请求Body体内的数据计算ContentMD5
            String contentMD5 = doContentMD5(reqBodyData);

            String method = "POST";
            String accept = "*/*";
            String contentType = "application/json";
            String date = "";
            String headers = "";
            String appId = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_ID");
            String appKey = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ESIGN", "APP_KEY");

            StringBuffer sb = new StringBuffer();
            sb.append(method).append("\n")
                    .append(accept).append("\n")
                    .append(contentMD5).append("\n")
                    .append(contentType).append("\n")
                    .append(date).append("\n");
            if ("".equals(headers)) {
                sb.append(headers).append(url);
            } else {
                sb.append(headers).append("\n").append(url);
            }
            // 构建参与请求签名计算的明文
            String plaintext = sb.toString();
            // 计算请求签名值
            String reqSignature = doSignatureBase64(plaintext, appKey);

            // 构建请求头
            LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
            header.put("X-Tsign-Open-App-Id", appId);
            header.put("X-Tsign-Open-Auth-Mode", "Signature");
            header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(new Date().getTime()));
            header.put("Accept", accept);
            header.put("Content-Type", contentType);
            header.put("X-Tsign-Open-Ca-Signature", reqSignature);
            header.put("Content-MD5", contentMD5);

            logger.info(">>>>>>>>>>>>>>>>>>请求头：{}", header);
            logger.info(">>>>>>>>>>>>>>>>>>获取协议撤回接口请求：{}", reqBodyData);
            String result = HTTPHelper.sendPOST(postUrl, reqBodyData, header, "UTF-8");
            logger.info(">>>>>>>>>>>>>>>>>>获取协议撤回接口返回：{}", result);

            return result;

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(">>>>>>>>>请求/api/eSignApi/agreementWithdrawnApi，系统异常，请联系管理员！");
            SignatureTaskLog signatureTaskLog = new SignatureTaskLog();
            signatureTaskLog.setId(Tool.uuid());
            signatureTaskLog.setReqData(json);
            signatureTaskLog.setDescription("协议撤回异常");
            signatureTaskLog.setCreateTime(new Date());
            signatureTaskLogService.insertSelective(signatureTaskLog);
            return JsonBizTool.genJson(RetEnum.FAIL);
        }
    }
}
