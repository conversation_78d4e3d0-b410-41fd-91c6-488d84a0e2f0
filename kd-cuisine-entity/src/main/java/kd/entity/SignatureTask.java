package kd.entity;

import java.util.Date;

/**
 * 签署任务表
 * 
 * @version 1.0 2024-09-14
 */
public class SignatureTask {
    /**
     * uuid
     */
    private String id;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 请求json
     */
    private String reqJson;

    /**
     * e签宝返回id
     */
    private String fileId;

    /**
     * 必填字段校验（默认true）
     */
    private String requiredCheck;

    /**
     * 状态   1:填充生成成功    2:填充生成失败   3:发起签署成功   4:发起签署失败   5:签署完成   6:签署已撤销   7:签署已过期   8:签署已拒签
     */
    private Integer status;

    /**
     * 回调地址
     */
    private String callback;

    /**
     * 签署人信息
     */
    private String personInfo;

    /**
     * 签署人数
     */
    private Integer personQuantity;

    /**
     * 完成人数
     */
    private Integer successQuantity;

    /**
     * 签署id
     */
    private String signFlowId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * uuid
     * @return 
     */
    public String getId() {
        return id;
    }

    /**
     * uuid
     * @param id
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 模板id
     * @return 
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     * 模板id
     * @param templateId
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId == null ? null : templateId.trim();
    }

    /**
     * 请求json
     * @return
     */
    public String getReqJson() {
        return reqJson;
    }

    /**
     * 请求json
     * @param reqJson
     */
    public void setReqJson(String reqJson) {
        this.reqJson = reqJson == null ? null : reqJson.trim();
    }

    /**
     * e签宝返回id
     * @return 
     */
    public String getFileId() {
        return fileId;
    }

    /**
     * e签宝返回id
     * @param fileId
     */
    public void setFileId(String fileId) {
        this.fileId = fileId == null ? null : fileId.trim();
    }

    /**
     * 必填字段校验（默认true）
     * @return 
     */
    public String getRequiredCheck() {
        return requiredCheck;
    }

    /**
     * 必填字段校验（默认true）
     * @param requiredCheck
     */
    public void setRequiredCheck(String requiredCheck) {
        this.requiredCheck = requiredCheck == null ? null : requiredCheck.trim();
    }

    /**
     * 状态
     * @return 
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 状态
     * @param status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 回调地址
     * @return 
     */
    public String getCallback() {
        return callback;
    }

    /**
     * 回调地址
     * @param callback
     */
    public void setCallback(String callback) {
        this.callback = callback == null ? null : callback.trim();
    }

    /**
     * 签署人信息
     * @return
     */
    public String getPersonInfo() {
        return personInfo;
    }

    /**
     * 签署人信息
     * @param personInfo
     */
    public void setPersonInfo(String personInfo) {
        this.personInfo = personInfo == null ? null : personInfo.trim();
    }

    /**
     * 签署人数
     * @return 
     */
    public Integer getPersonQuantity() {
        return personQuantity;
    }

    /**
     * 签署人数
     * @param personQuantity
     */
    public void setPersonQuantity(Integer personQuantity) {
        this.personQuantity = personQuantity;
    }

    /**
     * 完成人数
     * @return 
     */
    public Integer getSuccessQuantity() {
        return successQuantity;
    }

    /**
     * 完成人数
     * @param successQuantity
     */
    public void setSuccessQuantity(Integer successQuantity) {
        this.successQuantity = successQuantity;
    }

    /**
     * 签署id
     * @return 
     */
    public String getSignFlowId() {
        return signFlowId;
    }

    /**
     * 签署id
     * @param signFlowId
     */
    public void setSignFlowId(String signFlowId) {
        this.signFlowId = signFlowId == null ? null : signFlowId.trim();
    }

    /**
     * 创建时间
     * @return 
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}