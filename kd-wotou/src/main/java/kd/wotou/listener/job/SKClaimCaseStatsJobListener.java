package kd.wotou.listener.job;


import com.rabbitmq.client.Channel;
import kd.main.common.QueueName;
import kd.wotou.api.SKClaimCaseStatsApi;
import kd.wotou.event.SpringContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;

@Component
@RabbitListener(queues = QueueName.KD_SK_CLAIM_CASE_STATS)
public class SKClaimCaseStatsJobListener {

    private static final Logger  logger = LoggerFactory.getLogger(SKClaimCaseStatsJobListener.class);


    @RabbitHandler
    public void pushListener(String data, Message msg, Channel channel) throws IOException {
        logger.info(">>>>>>>>>队列{} 开始执行{}", QueueName.KD_SK_CLAIM_CASE_STATS, LocalDateTime.now());
        try {
            String repStr = SpringContextHolder.getBean(SKClaimCaseStatsApi.class).SKClaimCaseStats();
            logger.info(">>>>>>>>>执行结果{}", repStr);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            logger.info(">>>>>>>>>队列{} 结束执行{}", QueueName.KD_SK_CLAIM_CASE_STATS, LocalDateTime.now());
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
        }
    }
}
