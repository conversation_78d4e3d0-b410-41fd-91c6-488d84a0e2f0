package kd.wotou.listener;

import com.rabbitmq.client.Channel;
import kd.common.context.RedisCommonKeyEnum;
import kd.common.tool.EmailToolV3;
import kd.common.tool.RedisTool3;
import kd.main.common.QueueName;
import kd.main.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Date;

@Component
@RabbitListener(queues = QueueName.KD_SK_CLAIM_CASE_STATS_EMAIL)
public class EmailListener {
    private static final Logger logger = LoggerFactory.getLogger(EmailListener.class.getName());

    @RabbitHandler
    public void sendEmail(String content, Message msg, Channel channel) throws IOException {
        logger.info(">>>>>>>>>>>>>>>>>>>>>>开始发送邮件,content:{}", content);
        try {
            if (StringUtils.isBlank(content)){
                logger.info("邮件发送内容为空");
            }
            String subject="饿了么人保案件统计" ;
            String emails = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "PICC_CASE_ELE", "EMAILS_SK_CLAIM_CASE_STATS");
            if (StringUtils.isBlank(emails)){
                logger.info("Redis中未配置收件人");
            } else {
                EmailToolV3.sendHtmlEmail4Eai(subject,content,emails,"kd",null);
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
        }
    }
}
