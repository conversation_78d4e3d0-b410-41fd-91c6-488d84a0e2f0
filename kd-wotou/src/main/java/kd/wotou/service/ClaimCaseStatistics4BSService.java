package kd.wotou.service;

import kd.wotou.data.ClaimCaseStatistics4BS;
import kd.wotou.vo.data.ClaimCaseStatistics4BSDataVo;

import java.util.Date;
import java.util.List;
import java.util.Map;


public interface ClaimCaseStatistics4BSService {

    /**
     * 根据ID查询案件统计
     * @param id
     * @return
     */
    ClaimCaseStatistics4BS selectByPrimaryKey(String id);

    /**
     * 更新或插入案件统计
     * @param claimCaseStatistics
     * @param isUpdate
     */
    void insertOrUpdateData(ClaimCaseStatistics4BS claimCaseStatistics, boolean isUpdate);


    /**
     * 根据insCodes进行滚动查询案件统计列表
     * @param size
     * @param insCodes
     * @return
     */
    ClaimCaseStatistics4BSDataVo findAllListByScroll4Statistics(int size, String[] insCodes);

    /**
     * 根据insCodes、案件状态、时间进行滚动查询案件统计列表
      * @param size
     * @param insCodes
     * @param status
     * @param endDate
     * @return
     */
    ClaimCaseStatistics4BSDataVo findAllListByScroll4Statistics(int size, String[] insCodes,String status, Date endDate);

    /**
     * 根据insCodes、出险时间范围、案件状态进行滚动查询案件统计列表
     * @param size
     * @param insCodes
     * @param status
     * @param startDate
     * @param endDate
     * @return
     */
    ClaimCaseStatistics4BSDataVo findAllListByScroll4Statistics(int size, String[] insCodes,String status, Date startDate, Date endDate);

    /**
     * 根据insCodes和出险时间范围进行滚动查询案件统计列表
     * @param size
     * @param insCodes
     * @param startDate
     * @param endDate
     * @return
     */
    ClaimCaseStatistics4BSDataVo findAllListByScroll4Statistics(int size,  String[] insCodes, Date startDate, Date endDate);

    /**
     * 根据滚动ID进行滚动查询
     * @param scrollId
     * @return
     */
    ClaimCaseStatistics4BSDataVo findAllListByScrollId4Statistics(String scrollId);

    /**
     * 案件统计查询
     * @param paramMap
     * @return
     */
    List<ClaimCaseStatistics4BS> selectAllByInsCode(Map<String, Object> paramMap);

    /**
     * 根据更新时间范围查询未决案件统计
     * @param size
     * @param beforeDate
     * @return
     */
    ClaimCaseStatistics4BSDataVo findAllNotFinishCaseInfo(int size, Date beforeDate);

    /**
     * 根据id列表查询案件统计
     * @param idList
     * @return
     */
    List<ClaimCaseStatistics4BS> findAllByIdList(List<String> idList);


}
