package kd.wotou.api;


import kd.common.context.RedisCommonKeyEnum;
import kd.common.tool.JsonBizTool;
import kd.common.tool.RedisTool3;
import kd.main.common.ExRetEnum;
import kd.main.common.QueueName;
import kd.main.util.DateUtils;
import kd.wotou.data.ClaimCaseStatistics4BS;
import kd.wotou.service.ClaimCaseStatistics4BSService;
import kd.wotou.vo.data.ClaimCaseStatistics4BSDataVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * @author: zack
 * @date: 2025/7/30
 * @description: 饿了么人保案件统计
 *
 */
@Controller
@RequestMapping(value = "/api/SKClaimCaseStatsApi")
public class SKClaimCaseStatsApi {

    private static final Logger  logger = LoggerFactory.getLogger(SKClaimCaseStatsApi.class);

    @Autowired
    private ClaimCaseStatistics4BSService claimCaseStatistics4BSService;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @RequestMapping(value = "SKClaimCaseStats")
    @ResponseBody
    public String SKClaimCaseStats(){

        try {
            logger.info("开始任务饿了么人保案件统计");

            //初始时间
            String initialDate = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "PICC_CASE_ELE_STATS", "CLAIM_CASE_STATS_START_DATE");
            Date startDate = DateUtils.parse(initialDate + " 00:00:00", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS);

            String dayDate = DateUtils.format(DateUtils.addDays(new Date(), -1), DateUtils.FORMAT_DATE_YYYY_MM_DD);

            //昨天起止日期
            Date dayStartDate = DateUtils.parse(dayDate + " 00:00:00", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS);
            Date dayEndDate = DateUtils.parse(dayDate + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS);

            if (dayEndDate.getTime() < startDate.getTime()){
                logger.info("饿了么人保案件统计任务失败，统计时间小于起始时间");
                return JsonBizTool.genJson(ExRetEnum.FAIL);
            }

            String insCode = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "PICC_CASE_ELE_STATS", "CLAIM_CASE_STATS_INSCODE");
            String[] insCodes = insCode.split(",");
            int size = 1000;

            //1.报案：前一天三库新增的案件数
            //截止昨天全量案件统计
            ClaimCaseStatistics4BSDataVo claimCaseStatistics4BSDayDataVo = claimCaseStatistics4BSService.findAllListByScroll4Statistics(size, insCodes,"-1",startDate, dayEndDate);
            List<ClaimCaseStatistics4BS> claimCaseStatistics4BSDayList = new ArrayList<>();
            scrollTraverse(claimCaseStatistics4BSDayDataVo,claimCaseStatistics4BSDayList);

            //2.零结（关闭）：前一天三库关闭的案件数
            String status = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "PICC_CASE_ELE_STATS", "CLAIM_CASE_STATS_STATUS");
            if (StringUtils.isBlank(status)){
                status = "关闭";
            }
            //到昨天的关闭案件全量数
            ClaimCaseStatistics4BSDataVo claimCaseStatisticsClose4BSDayDataVo = claimCaseStatistics4BSService.findAllListByScroll4Statistics(size, insCodes,status,startDate, dayEndDate);
            List<ClaimCaseStatistics4BS> claimCaseStatisticsClose4BSDayList = new ArrayList<>();
            scrollTraverse(claimCaseStatisticsClose4BSDayDataVo,claimCaseStatisticsClose4BSDayList);

            //上次统计新增的案件数
            String oldDayClaimCase = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "PICC_CASE_ELE_STATS", "DAY_CLAIM_CASE");
            //上次统计关闭案件数
            String oldDayCloseClaimCase = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "PICC_CASE_ELE_STATS", "DAY_CLOSE_CLAIM_CASE");

            if (StringUtils.isAnyBlank(oldDayClaimCase,oldDayCloseClaimCase)){
                return JsonBizTool.genJson(ExRetEnum.FAIL);
            }

            int newDayClaimCase = claimCaseStatistics4BSDayList.size() - Integer.parseInt(oldDayClaimCase);
            int newDayCloseClaimCase = claimCaseStatisticsClose4BSDayList.size() - Integer.parseInt(oldDayCloseClaimCase);

            //3.当年报案：当年三库新增的案件数
            ClaimCaseStatistics4BSDataVo claimCaseStatistics4BSYearDataVo = claimCaseStatistics4BSService.findAllListByScroll4Statistics(size, insCodes,"-1",  startDate, dayEndDate);
            List<ClaimCaseStatistics4BS> claimCaseStatisticsClose4BSYearList = new ArrayList<>();
            scrollTraverse(claimCaseStatistics4BSYearDataVo,claimCaseStatisticsClose4BSYearList);

            int newYearClaimCase = claimCaseStatisticsClose4BSYearList.size();

            //4.当年结案：当年三库结案的案件数
            ClaimCaseStatistics4BSDataVo claimCaseStatistics4BSYearCloseDataVo = claimCaseStatistics4BSService.findAllListByScroll4Statistics(size, insCodes,status,  startDate, dayEndDate);
            List<ClaimCaseStatistics4BS> claimCaseStatisticsClose4BSYearCloseList = new ArrayList<>();
            scrollTraverse(claimCaseStatistics4BSYearCloseDataVo,claimCaseStatisticsClose4BSYearCloseList);

            int newYearCloseClaimCase = claimCaseStatisticsClose4BSYearCloseList.size();


            String date = DateUtils.format(DateUtils.addDays(new Date(), -1), DateUtils.FORMAT_DATE_YYYY_MM_DD_CHINA);
            String content = "  日期："+date+
                    "\n  报案："+newDayClaimCase+
                    "\n  零结（关闭）："+newDayCloseClaimCase+
                    "\n  当年报案："+newYearClaimCase+
                    "\n  当年零结（关闭）："+newYearCloseClaimCase;

            amqpTemplate.convertAndSend(QueueName.KD_SK_CLAIM_CASE_STATS_EMAIL, content);

            //存放这次统计的报案数和零结数
            RedisTool3.hset(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "PICC_CASE_ELE_STATS", "DAY_CLAIM_CASE", claimCaseStatistics4BSDayList.size() + "");
            RedisTool3.hset(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "PICC_CASE_ELE_STATS", "DAY_CLOSE_CLAIM_CASE", claimCaseStatisticsClose4BSDayList.size() + "");

        } catch (Exception e) {
            logger.error(">>>>>>>>> 饿了么人保案件统计异常{}", e);
            e.printStackTrace();

            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }



    //滚动遍历
    public void scrollTraverse(ClaimCaseStatistics4BSDataVo claimCaseStatistics4BSDataVo, List<ClaimCaseStatistics4BS> claimCaseStatistics4BSList){
        if (CollectionUtils.isNotEmpty(claimCaseStatistics4BSDataVo.getClaimCaseStatistics4BSList())) {
            while (CollectionUtils.isNotEmpty(claimCaseStatistics4BSDataVo.getClaimCaseStatistics4BSList())) {
                for (ClaimCaseStatistics4BS claimCaseStatistics4BS : claimCaseStatistics4BSDataVo.getClaimCaseStatistics4BSList()) {
                    claimCaseStatistics4BSList.add(claimCaseStatistics4BS);
                }
                claimCaseStatistics4BSDataVo = claimCaseStatistics4BSService.findAllListByScrollId4Statistics(claimCaseStatistics4BSDataVo.getScrollId());
            }
        }
    }



}
