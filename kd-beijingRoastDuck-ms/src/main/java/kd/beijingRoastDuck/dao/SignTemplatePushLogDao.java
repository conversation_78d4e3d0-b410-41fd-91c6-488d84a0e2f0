package kd.beijingRoastDuck.dao;

import kd.entity.SignTemplatePushLog;
import org.springframework.stereotype.Repository;

/**
 * @author: hoomik
 * @description: 签署任务推送
 * @date: 2024/11/07 14:30
 * @param 
 * @return
 */
@Repository
public interface SignTemplatePushLogDao {

    /**
     * @author: hoomik
     * @description: 添加签署任务推送log
     * @date: 2024/12/10 14:31
     * @param signTemplatePushLog
     * @return 
     */
    int insertSelective(SignTemplatePushLog signTemplatePushLog);

    /**
     * @author: hoomik
     * @description: 修改签署任务推送log
     * @date: 2024/12/10 14:32
     * @param signTemplatePushLog
     * @return 
     */
    int updateByPrimaryKeySelective(SignTemplatePushLog signTemplatePushLog);

    /**
     * @author: zack
     * @description: 根据案件id修改签署任务推送log 将状态不为-1的改为-1
     * @date: 2025/08/04 11:14
     * @param signTemplatePushLog
     * @return
     */
    int updateByClaimCaseObjectId(SignTemplatePushLog signTemplatePushLog);

}
