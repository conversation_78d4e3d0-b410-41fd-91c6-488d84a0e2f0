package kd.beijingRoastDuck.service;

import kd.beijingRoastDuck.vo.SignTemplatePushVo;
import kd.entity.SignTemplatePushLog;

import java.util.List;
import java.util.Map;

/**
 * @author: hoomik
 * @description: 签署推送日志
 * @date: 2024/11/07 14:32
 * @param
 * @return
 */
public interface SignTemplatePushLogService {

    /**
     * @author: hoomik
     * @description: 添加签署推送日志
     * @date: 2024/11/07 14:32
     * @param
     * @return
     */
    int insertSelective(SignTemplatePushLog signTemplatePushLog);

    /**
     * @author: hoomik
     * @description: 查询签署推送日志
     * @date: 2024/11/07 14:32
     * @param
     * @return
     */
    List<SignTemplatePushVo> findByParam(Map<String, Object> paramMap);

    /**
     * @author: hoomik
     * @description: 查询单个签署推送日志
     * @date: 2024/11/07 14:32
     * @param
     * @return
     */
    SignTemplatePushLog selectByPrimaryKey(String id);

    /**
     * @author: hoomik
     * @description: 修改签署推送日志
     * @date: 2024/11/07 14:32
     * @param
     * @return
     */
    int updateByPrimaryKeySelective(SignTemplatePushLog signTemplatePushLog);

    /**
     * @author: hoomik
     * @description: 根据案件对象id和status(不等于) 查询签署推送日志
     * @param claimCaseObjectId
     * @return
     */
    List<SignTemplatePushLog> findByClaimCaseObjectIdAndNoStatus(String claimCaseObjectId,String status);

}
