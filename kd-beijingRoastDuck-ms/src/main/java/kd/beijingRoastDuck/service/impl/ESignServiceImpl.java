package kd.beijingRoastDuck.service.impl;

import kd.beijingRoastDuck.dao.ClaimCaseObjectDao;
import kd.beijingRoastDuck.dao.SignTemplatePushLogDao;
import kd.beijingRoastDuck.service.ESignService;
import kd.entity.ClaimCaseObject;
import kd.entity.SignTemplatePushLog;
import kd.main.common.ClaimCaseObjectStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@Transactional
public class ESignServiceImpl implements ESignService {

    @Autowired
    private ClaimCaseObjectDao  claimCaseObjectDao;

    @Autowired
    private SignTemplatePushLogDao signTemplatePushLogDao;

    /**
     * 协议撤回
     * @param claimCaseObjectId
     * @return
     */
    @Override
    public int agreementWithdrawnData(String claimCaseObjectId) {

        int i = 0;

        //修改签字状态为签字待发起
        ClaimCaseObject updateClaimCaseObject = new ClaimCaseObject();
        updateClaimCaseObject.setId(claimCaseObjectId);
        updateClaimCaseObject.setStatus(ClaimCaseObjectStatusEnum.BAX38.getCode());
        updateClaimCaseObject.setModifyTime(new Date());
        i += claimCaseObjectDao.updateByPrimaryKeySelective(updateClaimCaseObject);

        //签字推送日志表 将状态不为-1状态改为 -1(关闭)
        SignTemplatePushLog updateSignTemplatePushLog = new SignTemplatePushLog();
        updateSignTemplatePushLog.setClaimCaseObjectId(claimCaseObjectId);
        updateSignTemplatePushLog.setStatus(-1);
        updateSignTemplatePushLog.setModifyTime(new Date());
        updateSignTemplatePushLog.setModifier("-1");

        i += signTemplatePushLogDao.updateByClaimCaseObjectId(updateSignTemplatePushLog);


        return i;
    }
}
