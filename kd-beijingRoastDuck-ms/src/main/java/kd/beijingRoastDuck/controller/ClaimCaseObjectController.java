package kd.beijingRoastDuck.controller;

import com.alibaba.fastjson.*;
import com.github.pagehelper.PageInfo;
import kd.beijingRoastDuck.event.SpringContextHolder;
import kd.beijingRoastDuck.req.ClaimCaseObjectLog4CarReq;
import kd.beijingRoastDuck.service.*;
import kd.beijingRoastDuck.support.AssessmentDataSup;
import kd.beijingRoastDuck.support.AssessmentDate4CarSup;
import kd.beijingRoastDuck.support.ClaimCaseObjectSup;
import kd.beijingRoastDuck.util.ShiroSessionUtil;
import kd.beijingRoastDuck.util.Tool;
import kd.beijingRoastDuck.vo.*;
import kd.common.context.RedisCommonKeyEnum;
import kd.common.pager.PageParam;
import kd.common.tool.*;
import kd.entity.*;
import kd.main.common.*;
import kd.main.elm.ClaimCaseLossAdjustVo;
import kd.main.support.signTask.PersonInfo;
import kd.main.support.signTask.SignPushTaskReq;
import kd.main.util.ConvertUpMoneyTool;
import kd.main.util.SignCenterTool;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

@Controller
@RequestMapping(value = "/claimCaseObjectController")
public class ClaimCaseObjectController {

    private final static Logger logger = LoggerFactory.getLogger(ClaimCaseObjectController.class.getName());

    @Autowired
    private AssessmentBusinessService assessmentBusinessService;

    @Autowired
    private ClaimCaseObjectService claimCaseObjectService;

    @Autowired
    private ClaimCaseObjectAssessmentService claimCaseObjectAssessmentService;

    @Autowired
    private ClaimCaseObjectPaymentService claimCaseObjectPaymentService;

    @Autowired
    private ClaimCaseService claimCaseService;

    @Autowired
    private ClaimCaseAttachService claimCaseAttachService;

    @Autowired
    private PolicyPersonService policyPersonService;

    @Autowired
    private ProductService productService;

    @Autowired
    private BankInfoService bankInfoService;

    @Autowired
    private ManagerService managerService;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private ClaimCaseObjectLogService claimCaseObjectLogService;

    @Autowired
    private SignTemplateConfigService signTemplateConfigService;

    @Autowired
    private SignTemplateService signTemplateService;

    @Autowired
    private ClaimCaseLogService claimCaseLogService;

    @Autowired
    private SignTemplatePushLogService signTemplatePushLogService;

    @Autowired
    private FloatProductService floatProductService;

    @Autowired
    private InsuranceNuclearAuditTaskService insuranceNuclearAuditTaskService;

    @Autowired
    private InsuranceClaimAssessmentService insuranceClaimAssessmentService;


    @RequestMapping(value = {"claimCaseObjectList", ""})
    public String claimCaseObjectList(ClaimCaseObjectVo claimCaseObjectVo, HttpServletRequest request, Model model) {
        PageParam pp = Tool.genPageParam(request);
        Map<String, Object> paramMap = new HashMap<>();
        if (claimCaseObjectVo.getComeFrom() == null) {
            return "error/404";
        }
        paramMap.put("comeFrom", claimCaseObjectVo.getComeFrom());
        paramMap.put("userId", ShiroSessionUtil.getLoginSession().getId());

        if (claimCaseObjectVo.getStatus() != null) {
            paramMap.put("status", claimCaseObjectVo.getStatus());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getClaimCaseNo())) {
            paramMap.put("claimCaseNo", claimCaseObjectVo.getClaimCaseNo());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getInsuranceCaseNo())) {
            paramMap.put("insuranceCaseNo", claimCaseObjectVo.getInsuranceCaseNo());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatName())) {
            paramMap.put("treatName", claimCaseObjectVo.getTreatName());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatIdNum())) {
            paramMap.put("treatIdNum", claimCaseObjectVo.getTreatIdNum());
        }
        if (claimCaseObjectVo.getType() != null) {
            paramMap.put("type", claimCaseObjectVo.getType());
        }
        if (claimCaseObjectVo.getCategory() != null) {
            paramMap.put("category", claimCaseObjectVo.getCategory());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getInsCode())) {
            paramMap.put("insCode", claimCaseObjectVo.getInsCode());
        }
//        if(StringUtils.isNotBlank(claimCaseObjectVo.getLabel())) {
//            paramMap.put("label", claimCaseObjectVo.getLabel());
//        }

        PageInfo<ClaimCaseObjectReq> page = claimCaseObjectService.claimCaseObjectList(paramMap, pp);
        model.addAttribute("page", page);
        model.addAttribute("claimCaseObjectVo", claimCaseObjectVo);

        Map<String, Object> statusMap = ClaimCaseObjectStatusEnum.getStatusMap();
        model.addAttribute("statusMap", statusMap);

        Map<String, Object> labelShowMap = ClaimCaseLabelEnum.getClaimCaseLabelMap();
        model.addAttribute("labelShowMap", labelShowMap);

        // 出险类型
        Map<String, ApplyTypeNewEnum> typeCategoryMap = ApplyTypeNewEnum.getAppyTypeNewMap();
        model.addAttribute("typeCategoryMap", typeCategoryMap);

        //展示InsCode选择
        Map<String, String> insCodeMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "POLICY_REPUSH_INS_CODE");
        model.addAttribute("insCodeMap", insCodeMap);

        return "claimCaseObject/claimCaseObjectList";
    }

    @RequestMapping(value = {"objectPretreatmentList", ""})
    public String objectPretreatmentList(ClaimCaseObjectVo claimCaseObjectVo, HttpServletRequest request, Model model) {
        PageParam pp = Tool.genPageParam(request);
        Map<String, Object> paramMap = new HashMap<>();
        if (claimCaseObjectVo.getComeFrom() == null) {
            return "error/404";
        }
        paramMap.put("comeFrom", claimCaseObjectVo.getComeFrom());
        paramMap.put("userId", ShiroSessionUtil.getLoginSession().getId());

        if (claimCaseObjectVo.getStatus() != null) {
            paramMap.put("status", claimCaseObjectVo.getStatus());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getClaimCaseNo())) {
            paramMap.put("claimCaseNo", claimCaseObjectVo.getClaimCaseNo());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatName())) {
            paramMap.put("treatName", claimCaseObjectVo.getTreatName());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatIdNum())) {
            paramMap.put("treatIdNum", claimCaseObjectVo.getTreatIdNum());
        }
        if (claimCaseObjectVo.getType() != null) {
            paramMap.put("type", claimCaseObjectVo.getType());
        }
        if (claimCaseObjectVo.getCategory() != null) {
            paramMap.put("category", claimCaseObjectVo.getCategory());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getInsCode())) {
            paramMap.put("insCode", claimCaseObjectVo.getInsCode());
        }

        PageInfo<ClaimCaseObjectReq> page = claimCaseObjectService.claimCaseObjectList(paramMap, pp);
        model.addAttribute("page", page);
        model.addAttribute("claimCaseObjectVo", claimCaseObjectVo);

        Map<String, Object> statusMap = ClaimCaseObjectStatusEnum.getStatusMap();
        model.addAttribute("statusMap", statusMap);

        Map<String, Object> labelShowMap = ClaimCaseLabelEnum.getClaimCaseLabelMap();
        model.addAttribute("labelShowMap", labelShowMap);

        // 出险类型
        Map<String, ApplyTypeNewEnum> typeCategoryMap = ApplyTypeNewEnum.getAppyTypeNewMap();
        model.addAttribute("typeCategoryMap", typeCategoryMap);

        return "claimCaseObject/objectPretreatmentList";
    }

    @RequestMapping(value = {"claimCaseObjectList4BS", ""})
    public String claimCaseObjectList4BS(ClaimCaseObjectVo claimCaseObjectVo, HttpServletRequest request, Model model) {
        PageParam pp = Tool.genPageParam(request);
        Map<String, Object> paramMap = new HashMap<>();
        if (claimCaseObjectVo.getComeFrom() == null) {
            return "error/404";
        }
        paramMap.put("comeFrom", claimCaseObjectVo.getComeFrom());
        paramMap.put("userId", ShiroSessionUtil.getLoginSession().getId());

        if (claimCaseObjectVo.getStatus() != null) {
            paramMap.put("status", claimCaseObjectVo.getStatus());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getClaimCaseNo())) {
            paramMap.put("claimCaseNo", claimCaseObjectVo.getClaimCaseNo());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatName())) {
            paramMap.put("treatName", claimCaseObjectVo.getTreatName());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatIdNum())) {
            paramMap.put("treatIdNum", claimCaseObjectVo.getTreatIdNum());
        }
        if (claimCaseObjectVo.getType() != null) {
            paramMap.put("type", claimCaseObjectVo.getType());
        }
        if (claimCaseObjectVo.getCategory() != null) {
            paramMap.put("category", claimCaseObjectVo.getCategory());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getInsCode())) {
            paramMap.put("insCode", claimCaseObjectVo.getInsCode());
        }

        PageInfo<ClaimCaseObjectReq> page = claimCaseObjectService.claimCaseObjectList4BS(paramMap, pp);
        model.addAttribute("page", page);
        model.addAttribute("claimCaseObjectVo", claimCaseObjectVo);

        Map<String, Object> statusMap = ClaimCaseObjectStatusEnum.getStatusMap();
        model.addAttribute("statusMap", statusMap);

        Set<String> managerIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (ClaimCaseObjectReq claimCaseObject : page.getList()) {
                if (StringUtils.isNotBlank(claimCaseObject.getAuditer())) {
                    managerIdList.add(claimCaseObject.getAuditer());
                }
            }
            if (CollectionUtils.isNotEmpty(managerIdList)) {
                List<Manager> managerList = managerService.findByManagerIdList(new ArrayList<>(managerIdList));
                Map<String, String> managerMap = managerList.stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
                model.addAttribute("managerMap", managerMap);
            }
        }


        // 出险类型
        Map<String, ApplyTypeNewEnum> typeCategoryMap = ApplyTypeNewEnum.getAppyTypeNewMap();
        model.addAttribute("typeCategoryMap", typeCategoryMap);

        //展示InsCode选择狂
        Map<String, String> insCodeMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "POLICY_REPUSH_INS_CODE");
        model.addAttribute("insCodeMap", insCodeMap);


        return "claimCaseObject/claimCaseObjectList4BS";
    }

    /**
     * 估损复核（保司，存在保司案件号版本）
     * @param claimCaseObjectVo
     * @param request
     * @param model
     * @return
     */
    @RequestMapping(value = {"claimCaseObjectList4BSV2", ""})
    public String claimCaseObjectList4BSV2(ClaimCaseObjectVo claimCaseObjectVo, HttpServletRequest request, Model model) {
        PageParam pp = Tool.genPageParam(request);
        Map<String, Object> paramMap = new HashMap<>();
        if (claimCaseObjectVo.getComeFrom() == null) {
            return "error/404";
        }
        paramMap.put("comeFrom", claimCaseObjectVo.getComeFrom());
        paramMap.put("userId", ShiroSessionUtil.getLoginSession().getId());

        if (claimCaseObjectVo.getStatus() != null) {
            paramMap.put("status", claimCaseObjectVo.getStatus());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getInsuranceCaseNo())) {
            paramMap.put("insuranceCaseNo", claimCaseObjectVo.getInsuranceCaseNo());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatName())) {
            paramMap.put("treatName", claimCaseObjectVo.getTreatName());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatIdNum())) {
            paramMap.put("treatIdNum", claimCaseObjectVo.getTreatIdNum());
        }
        if (claimCaseObjectVo.getType() != null) {
            paramMap.put("type", claimCaseObjectVo.getType());
        }
        if (claimCaseObjectVo.getCategory() != null) {
            paramMap.put("category", claimCaseObjectVo.getCategory());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getInsCode())) {
            paramMap.put("insCode", claimCaseObjectVo.getInsCode());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getStopFlag())) {
            paramMap.put("stopFlag", claimCaseObjectVo.getStopFlag());
        }
        PageInfo<ClaimCaseObjectReq> page = claimCaseObjectService.claimCaseObjectList4BSV2(paramMap, pp);
        model.addAttribute("page", page);
        model.addAttribute("claimCaseObjectVo", claimCaseObjectVo);

        Map<String, Object> statusMap = ClaimCaseObjectStatusEnum.getStatusMap();
        model.addAttribute("statusMap", statusMap);

        Set<String> managerIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (ClaimCaseObjectReq claimCaseObject : page.getList()) {
                if (StringUtils.isNotBlank(claimCaseObject.getAuditer())) {
                    managerIdList.add(claimCaseObject.getAuditer());
                }
            }
            if (CollectionUtils.isNotEmpty(managerIdList)) {
                List<Manager> managerList = managerService.findByManagerIdList(new ArrayList<>(managerIdList));
                Map<String, String> managerMap = managerList.stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
                model.addAttribute("managerMap", managerMap);
            }
        }


        // 出险类型
        Map<String, ApplyTypeNewEnum> typeCategoryMap = ApplyTypeNewEnum.getAppyTypeNewMap();
        model.addAttribute("typeCategoryMap", typeCategoryMap);

        //展示InsCode选择狂
        Map<String, String> insCodeMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "POLICY_REPUSH_INS_CODE");
        model.addAttribute("insCodeMap", insCodeMap);

        //放弃任务InsCode配置
        Map<String, String> stopTaskMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "TASK_STOP_INS_CODE");
        model.addAttribute("stopTaskMap", stopTaskMap);
        return "claimCaseObject/claimCaseObjectList4BSV2";
    }

    @RequestMapping(value = {"claimCaseSignatureList", ""})
    public String claimCaseSignatureList(ClaimCaseObjectVo claimCaseObjectVo, HttpServletRequest request, Model model) {
        PageParam pp = Tool.genPageParam(request);
        Map<String, Object> paramMap = new HashMap<>();

        paramMap.put("userId", ShiroSessionUtil.getLoginSession().getId());

        if (claimCaseObjectVo.getStatus() != null) {
            paramMap.put("status", claimCaseObjectVo.getStatus());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getClaimCaseNo())) {
            paramMap.put("claimCaseNo", claimCaseObjectVo.getClaimCaseNo());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatName())) {
            paramMap.put("treatName", claimCaseObjectVo.getTreatName());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatIdNum())) {
            paramMap.put("treatIdNum", claimCaseObjectVo.getTreatIdNum());
        }
        if (claimCaseObjectVo.getType() != null) {
            paramMap.put("type", claimCaseObjectVo.getType());
        }
        if (claimCaseObjectVo.getCategory() != null) {
            paramMap.put("category", claimCaseObjectVo.getCategory());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getInsCode())) {
            paramMap.put("insCode", claimCaseObjectVo.getInsCode());
        }

        if (StringUtils.isNotBlank(claimCaseObjectVo.getAuditer())){
            paramMap.put("auditer", claimCaseObjectVo.getAuditer());
        }


        PageInfo<ClaimCaseObjectReq> page = claimCaseObjectService.claimCaseSignatureList(paramMap, pp);
        model.addAttribute("page", page);
        model.addAttribute("claimCaseObjectVo", claimCaseObjectVo);

        Map<String, Object> statusMap = ClaimCaseObjectStatusEnum.getStatusMap();
        model.addAttribute("statusMap", statusMap);

        Set<String> managerIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (ClaimCaseObjectReq claimCaseObject : page.getList()) {
                if (StringUtils.isNotBlank(claimCaseObject.getAuditer())) {
                    managerIdList.add(claimCaseObject.getAuditer());
                }
            }
            if (CollectionUtils.isNotEmpty(managerIdList)) {
                List<Manager> managerList = managerService.findByManagerIdList(new ArrayList<>(managerIdList));
                Map<String, String> managerMap = managerList.stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
                model.addAttribute("managerMap", managerMap);
            }
        }


        // 出险类型
        Map<String, ApplyTypeNewEnum> typeCategoryMap = ApplyTypeNewEnum.getAppyTypeNewMap();
        model.addAttribute("typeCategoryMap", typeCategoryMap);

        //展示InsCode选择框
        Map<String, String> insCodeMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "POLICY_REPUSH_INS_CODE");
        model.addAttribute("insCodeMap", insCodeMap);


        Map<String, String> managerSelectMap = managerService.findAllManager(new HashMap<>()).stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
        model.addAttribute("managerSelectMap", managerSelectMap);
        return "claimCaseObject/claimCaseSignatureList";
    }

    /**
     * 我的任务/内部审核 - 开始审核
     * 检查三者车损车牌号、车辆识别代码是否存在重复,如果重复返回存在的案件号
     * 检查三者人伤姓名、身份证号是否存在重复,如果重复返回存在的案件号
     * @return ClaimCaseNos
     */
    @RequestMapping(value = {"taskFieldCheck", ""})
    @ResponseBody
    public String taskFieldCheck(@RequestParam Map<String, String> params){

        if (params == null || StringUtils.isBlank(params.get("params"))) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }

        Map<String,String> map = new HashMap<>();

        try {
            map = JSONObject.parseObject(params.get("params"), Map.class);
        }catch (JSONException e){
            return JsonBizTool.genJson(ExRetEnum.ERROR_PUSH_REQ_TYPE_NOT_ALLOW);
        }

        if (map == null) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }

        String claimCaseNo = map.get("claimCaseNo");
        if (StringUtils.isBlank(claimCaseNo)){
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }

        Set<String> carNumberClaimCaseNo = null;
        Set<String> carEncodingClaimCaseNo = null;
        //去除姓名重复判断值
        Set<String> treatIdNumClaimCaseNo = null;

        if (StringUtils.isNotEmpty(map.get("carNumber"))){
            carNumberClaimCaseNo = claimCaseObjectService.findByUnique(Collections.singletonMap("carNumber", map.get("carNumber"))).stream().filter(item -> item.getIsCaseClosed() != 1).map(ClaimCaseObject::getClaimCaseNo).collect(Collectors.toSet());
        }

        if (StringUtils.isNotEmpty(map.get("carEncoding"))){
            carEncodingClaimCaseNo = claimCaseObjectService.findByUnique(Collections.singletonMap("carEncoding", map.get("carEncoding"))).stream().filter(item -> item.getIsCaseClosed() != 1).map(ClaimCaseObject::getClaimCaseNo).collect(Collectors.toSet());
        }

        //去除姓名重复判断 获取值

        if (StringUtils.isNotEmpty(map.get("treatIdNum"))){
            treatIdNumClaimCaseNo = claimCaseObjectService.findByUnique(Collections.singletonMap("treatIdNum", map.get("treatIdNum"))).stream().filter(item -> item.getIsCaseClosed() != 1).map(ClaimCaseObject::getClaimCaseNo).collect(Collectors.toSet());
        }

        Map<String, Object> resultMap = new HashMap<>();

        if (carNumberClaimCaseNo != null) {
            // 如果是当前案件的案件号不会提示重复
            carNumberClaimCaseNo.remove(claimCaseNo);
            resultMap.put("carNumberClaimCaseNo", carNumberClaimCaseNo);
        }

        if (carEncodingClaimCaseNo != null) {
            // 如果是当前案件的案件号不会提示重复
            carEncodingClaimCaseNo.remove(claimCaseNo);
            resultMap.put("carEncodingClaimCaseNo", carEncodingClaimCaseNo);
        }
        //去除姓名重复判断 判断是否是当前案件


        if (treatIdNumClaimCaseNo != null) {
            // 如果是当前案件的案件号不会提示重复
            treatIdNumClaimCaseNo.remove(claimCaseNo);
            resultMap.put("treatIdNumClaimCaseNo", treatIdNumClaimCaseNo);
        }

        return JsonBizTool.genJson(resultMap);
    }

    @RequestMapping(value = {"startTask", ""})
    public String startTask(String id, Integer comeFrom,String checkCondition, Model model) {

        String userId = ShiroSessionUtil.getLoginSession().getId();
        String realName = ShiroSessionUtil.getLoginSession().getRealName();
        String position = ShiroSessionUtil.getLoginRoleSession().get(0).getName();

        if (StringUtils.isBlank(id) || comeFrom == null) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }
        if (claimCaseObject.getIsCaseClosed() == 1) {
            model.addAttribute("exception", "该估损单案件已关闭");
            return "error/500";
        }

        if (comeFrom == 1) {
            if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            }
        }

        // 内部获取审核任务
        if (comeFrom == 2) {
            if (StringUtils.isNotBlank(claimCaseObject.getCheckAuditer()) && !claimCaseObject.getCheckAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getCheckAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX22.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX32.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getCheckAuditer())) {
                    claimCaseObject.setCheckAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        }

        // 保司获取审核任务
        if (comeFrom == 3) {
            if (StringUtils.isNotBlank(claimCaseObject.getInsAuditer()) && !claimCaseObject.getInsAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getInsAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX35.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getInsAuditer())) {
                    claimCaseObject.setInsAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        }

        // 车损对象 && 理算流程 && 新案件流程
        if (claimCaseObject.getCategory() == 3 && claimCaseObject.getStatus().startsWith("BAX3") && "1".equals(claimCaseObject.getIsNewCase())) {
            // 使用新车损流程进行处理
            return carStartTask(id, comeFrom, checkCondition, model);
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);
        if (StringUtils.isNotBlank(claimCase.getPolicyPersonId())) {
            PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());
            model.addAttribute("policyPerson", policyPerson);
          /*  String redisPlanCode = policyPerson.getPlanId() + "_" + policyPerson.getPayPremium().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
            String planCode = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ELE_CCIC_PLAN_CODE", redisPlanCode);*/
            Product product = productService.selectByEleProductCode(policyPerson.getPlanId());
            model.addAttribute("product", product);
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseId", claimCase.getId());
        String skipAttach = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "not_search_file_content_type");
        if (StringUtils.isNotBlank(skipAttach)) {
            List<String> skipAttachList = Arrays.asList(skipAttach.split(","));
            paramMap.put("skipAttach", skipAttachList);
        }
        List<ClaimCaseAttach> claimAttachList = claimCaseAttachService.findAttachByParam(paramMap);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(claimAttachList)) {
            claimAttachList.stream().forEach(claimCaseAttach -> {
                claimCaseAttach.setFileObjectId(AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, claimCaseAttach.getFileObjectId(), 3600 * 1000 * 3));
                // 历史数据排在最后面
                if (claimCaseAttach.getAttachIndex() == null) {
                    claimCaseAttach.setAttachIndex(99999);
                }
            });
            Map<String, List<ClaimCaseAttach>> claimAttachMap = claimAttachList.stream().collect(groupingBy(
                    ClaimCaseAttach::getContentType,
                    collectingAndThen(
                            toList(),
                            list -> list.stream().sorted(Comparator.comparing(ClaimCaseAttach::getAttachIndex)).collect(toList())
                    )
            ));
            model.addAttribute("claimAttachMap", claimAttachMap);

        }
        Map<String, Object> imgInfoMap = CaseImgInfoEnum.getImgInfoMap();
        model.addAttribute("imgInfoMaps", imgInfoMap);
        model.addAttribute("claimCaseObject", claimCaseObject);

        if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
            if (bankInfo != null) {
                model.addAttribute("bankInfo", bankInfo);
            }
        }

        if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("readonly", true);
        }

        if (!ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("verifyReadonly", true);
        }

        model.addAttribute("comeFrom", comeFrom);
        model.addAttribute("checkCondition", checkCondition);

        List<ClaimCaseObjectAssessment> objectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(objectAssessmentList)){
            objectAssessmentList.stream().forEach(claimCaseObjectAssessment -> {
                if(claimCaseObjectAssessment.getAssessmentIndex() == null){
                    //历史数据排在最后面
                    claimCaseObjectAssessment.setAssessmentIndex(99999);
                }
            });
            //排序
            objectAssessmentList.sort(Comparator.comparingInt(ClaimCaseObjectAssessment::getType)
                    .thenComparingInt(ClaimCaseObjectAssessment::getCategory)
                    .thenComparingInt(ClaimCaseObjectAssessment::getAssessmentIndex));

            model.addAttribute("objectAssessmentList", objectAssessmentList);
        }

        List<ClaimCaseObjectPayment> objectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
        model.addAttribute("objectPaymentList", JsonTool.genByFastJson(objectPaymentList));

        Map<String, BankInfo> bankInfoMap = new HashMap<>();

        for (ClaimCaseObjectPayment objectPayment : objectPaymentList) {
            if (StringUtils.isNotBlank(objectPayment.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(objectPayment.getBankInfoId());
                if (bankInfo != null) {
                    bankInfoMap.put(objectPayment.getBankInfoId(), bankInfo);
                }
            }
        }
        model.addAttribute("bankInfoMap", JsonTool.genByFastJson(bankInfoMap));

        //预估金额
        List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.findByParam(paramMap);
        if(CollectionUtils.isNotEmpty(claimCaseObjectList)){
            BigDecimal objectAppraisalAmount = BigDecimal.ZERO;
            for (ClaimCaseObject Object : claimCaseObjectList) {
                objectAppraisalAmount = objectAppraisalAmount.add(Object.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : Object.getEstimatedApprovedMoney());
            }
            model.addAttribute("objectAppraisalAmount",objectAppraisalAmount);
        }

        paramMap.clear();
        paramMap.put("baseUserId", claimCase.getBaseUserId());
        List<ClaimCase> claimCaseList = claimCaseService.findByTreatParam(paramMap);
        model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(claimCaseList) ? claimCaseList.size() : 0);

        if (comeFrom == 1) {
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
            claimCaseObjectLog.setPosition(position);
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(claimCaseObject.getStatus().startsWith("BAX2") ? "估损开始任务" : "理算开始任务");
            claimCaseObjectLog.setCreator(realName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogService.insertSelective(claimCaseObjectLog);
        }

        Map<String, String> isValidityMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "VALIDITY_CERTIFICATE_INSCODE");
        if (isValidityMap.containsKey(claimCase.getInsCode())) {
            model.addAttribute("isValidity", true);
        }
        Map<String, Object> enumMapByParentCode = new HashMap<>();
        if (claimCaseObject.getCategory() == 1) {
            enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("1");
            model.addAttribute("enumMapByParentCode", enumMapByParentCode);
            return "claimCaseObject/objectGatherDetail";
        }


        enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("2");
        model.addAttribute("enumMapByParentCode", enumMapByParentCode);
        return "claimCaseObject/objectGatherGoodsDetail";
    }

    /**
     * 三者车损跳转定损页面
     * @param id
     * @param model
     * @return
     */
    @RequestMapping("startMeasureLossAssessment4Car")
    public String startMeasureLossAssessment4Car(String id, Integer comeFrom,String checkCondition, Model model){
        String userId = ShiroSessionUtil.getLoginSession().getId();
        String realName = ShiroSessionUtil.getLoginSession().getRealName();
        String position = ShiroSessionUtil.getLoginRoleSession().get(0).getName();

        if (StringUtils.isBlank(id)) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }
        if (claimCaseObject.getIsCaseClosed() == 1) {
            model.addAttribute("exception", "该估损单案件已关闭");
            return "error/500";
        }

        if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus())
                && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus())
                && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())
                && !ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus())
                && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus())
                && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);

        model.addAttribute("claimCaseObject", claimCaseObject);

        model.addAttribute("caseId", claimCase.getId());
        model.addAttribute("isShowUploadBotton","1");//是否显示上传影像 1：是；0：否
        model.addAttribute("isShowDeleteDir","1");//是否显示删除的文件夹 1：是；0：否
        model.addAttribute("isShowDeleteButton","1");//是否显示影像删除按钮 1：是；0：否

        if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
            if (bankInfo != null) {
                model.addAttribute("bankInfo", bankInfo);
            }
        }

        //更新为新案件
        ClaimCaseObject caseObject = new ClaimCaseObject();
        caseObject.setId(id);
        caseObject.setIsNewCase("1");
        claimCaseObjectService.updateByPrimaryKeySelective(caseObject);

        ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
        claimCaseObjectLog.setId(Tool.uuid());
        claimCaseObjectLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
        claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
        claimCaseObjectLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
        claimCaseObjectLog.setPosition(position);
        claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
        claimCaseObjectLog.setDescription("估损开始任务");
        claimCaseObjectLog.setCreator(realName + "-" + userId);
        claimCaseObjectLog.setCreateTime(new Date());

        claimCaseObjectLogService.insertSelective(claimCaseObjectLog);

        if(StringUtils.isNotBlank(claimCase.getLabel())){
            if(claimCase.getLabel().contains( ClaimCaseLabelEnum.msgToCode("车物先行赔付"))){
                model.addAttribute("claimFirst", "1");
            }
            if(claimCase.getLabel().contains( ClaimCaseLabelEnum.msgToCode("车物先行"))){
                model.addAttribute("claimFirst", "1");
            }

            if(claimCase.getLabel().contains( ClaimCaseLabelEnum.msgToCode("诉讼"))){
                model.addAttribute("litigation", "1");
            }
            if(claimCase.getLabel().contains( ClaimCaseLabelEnum.msgToCode("代位追偿"))){
                model.addAttribute("subrogation", "1");
            }
        }

        model.addAttribute("comeFrom", comeFrom);
        model.addAttribute("checkCondition", checkCondition);

        Map<String, Object> logMap = new HashMap<>();
        logMap.put("claimCaseObjectId", claimCaseObject.getId());

        List<InsuranceClaimAssessmentVo> insuranceClaimAssessmentList = insuranceClaimAssessmentService.selectByParam(logMap);
        model.addAttribute("costList", insuranceClaimAssessmentList);

        Map<String, String> carAssessmentTypeMap = CarAssessmentType.getCarAssessmentTypeMap();
        model.addAttribute("carAssessmentTypeMap", carAssessmentTypeMap);

        return "carDamage/lossAssessmentDetail";
    }

    /**
     * 车损保司审核-运营侧
     * @param id
     * @param comeFrom
     * @param checkCondition
     * @param model
     * @return
     */
    @RequestMapping(value = {"startTaskCar", ""})
    public String startTaskCar(String id, Integer comeFrom,String checkCondition, Model model) {

        String userId = ShiroSessionUtil.getLoginSession().getId();
        String realName = ShiroSessionUtil.getLoginSession().getRealName();
        String position = ShiroSessionUtil.getLoginRoleSession().get(0).getName();

        if (StringUtils.isBlank(id) || comeFrom == null) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }
        if (claimCaseObject.getIsCaseClosed() == 1) {
            model.addAttribute("exception", "该估损单案件已关闭");
            return "error/500";
        }

        if (comeFrom == 1) {
            if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            }
        }

        // 内部获取审核任务
        if (comeFrom == 2) {
            if (StringUtils.isNotBlank(claimCaseObject.getCheckAuditer()) && !claimCaseObject.getCheckAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getCheckAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX22.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX32.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getCheckAuditer())) {
                    claimCaseObject.setCheckAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        }

        // 保司获取审核任务
        if (comeFrom == 3) {
            if (StringUtils.isNotBlank(claimCaseObject.getInsAuditer()) && !claimCaseObject.getInsAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getInsAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX35.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getInsAuditer())) {
                    claimCaseObject.setInsAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);

        model.addAttribute("claimCaseObject", claimCaseObject);
        model.addAttribute("caseId",claimCase.getId());//案件id 必须要
        model.addAttribute("isShowUploadBotton","0");//是否显示上传影像 1：是；0：否
        model.addAttribute("isShowDeleteDir","1");//是否显示删除的文件夹 1：是；0：否 (保司复审运营侧要显示删除文件夹)
        model.addAttribute("isShowDeleteButton","0");//是否显示影像删除按钮 1：是；0：否


        // 查询核损费用合计数据 (t_insurance_claim_assessment)
        /*List<InsuranceClaimAssessmentVo> costList = insuranceNuclearAuditTaskService.findCostDataByLossNo(claimCaseObject.getId());
        model.addAttribute("costList", costList);*/


        model.addAttribute("comeFrom", comeFrom);
        model.addAttribute("checkCondition", checkCondition);
        model.addAttribute("assessmentType", CarAssessmentType.NUCLEAR_DAMAGE.getMsg());

        if (comeFrom == 1) {
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
            claimCaseObjectLog.setPosition(position);
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(claimCaseObject.getStatus().startsWith("BAX2") ? "估损开始任务" : "理算开始任务");
            claimCaseObjectLog.setCreator(realName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogService.insertSelective(claimCaseObjectLog);
        }


        return "insuranceNuclearAudit/taskDetail";
    }

    /**
     * 车损保司审核-保司侧
     * @param id
     * @param comeFrom
     * @param checkCondition
     * @param model
     * @return
     */
    @RequestMapping(value = {"startTaskCarV2", ""})
    public String startTaskCarV2(String id, Integer comeFrom,String checkCondition, Model model) {

        String userId = ShiroSessionUtil.getLoginSession().getId();
        String realName = ShiroSessionUtil.getLoginSession().getRealName();
        String position = ShiroSessionUtil.getLoginRoleSession().get(0).getName();

        if (StringUtils.isBlank(id) || comeFrom == null) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }
        if (claimCaseObject.getIsCaseClosed() == 1) {
            model.addAttribute("exception", "该估损单案件已关闭");
            return "error/500";
        }

        if (comeFrom == 1) {
            if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            }
        }

        // 内部获取审核任务
        if (comeFrom == 2) {
            if (StringUtils.isNotBlank(claimCaseObject.getCheckAuditer()) && !claimCaseObject.getCheckAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getCheckAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX22.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX32.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getCheckAuditer())) {
                    claimCaseObject.setCheckAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        }

        // 保司获取审核任务
        if (comeFrom == 3) {
            if (StringUtils.isNotBlank(claimCaseObject.getInsAuditer()) && !claimCaseObject.getInsAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getInsAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX35.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getInsAuditer())) {
                    claimCaseObject.setInsAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);

        model.addAttribute("claimCaseObject", claimCaseObject);
        model.addAttribute("caseId",claimCase.getId());//案件id 必须要
        model.addAttribute("isShowUploadBotton","0");//是否显示上传影像 1：是；0：否
        model.addAttribute("isShowDeleteDir","0");//是否显示删除的文件夹 1：是；0：否
        model.addAttribute("isShowDeleteButton","0");//是否显示影像删除按钮 1：是；0：否


        // 查询核损费用合计数据 (t_insurance_claim_assessment)
        /*List<InsuranceClaimAssessmentVo> costList = insuranceNuclearAuditTaskService.findCostDataByLossNo(claimCaseObject.getId());
        model.addAttribute("costList", costList);*/


        model.addAttribute("comeFrom", comeFrom);
        model.addAttribute("checkCondition", checkCondition);
        model.addAttribute("assessmentType", CarAssessmentType.NUCLEAR_DAMAGE.getMsg());

        if (comeFrom == 1) {
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
            claimCaseObjectLog.setPosition(position);
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(claimCaseObject.getStatus().startsWith("BAX2") ? "估损开始任务" : "理算开始任务");
            claimCaseObjectLog.setCreator(realName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogService.insertSelective(claimCaseObjectLog);
        }


        return "insuranceNuclearAudit/taskDetailV2";
    }

    /**
     * 车损理算初审
     * @param id
     * @param comeFrom
     * @param checkCondition
     * @param model
     * @return
     */
    @RequestMapping(value = {"startTaskAccountingCar", ""})
    public String startTaskAccountingCar(String id, Integer comeFrom,String checkCondition, Model model) {

        String userId = ShiroSessionUtil.getLoginSession().getId();
        String realName = ShiroSessionUtil.getLoginSession().getRealName();
        String position = ShiroSessionUtil.getLoginRoleSession().get(0).getName();

        if (StringUtils.isBlank(id) || comeFrom == null) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }
        if (claimCaseObject.getIsCaseClosed() == 1) {
            model.addAttribute("exception", "该估损单案件已关闭");
            return "error/500";
        }

        if (comeFrom == 1) {
            if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            }
        }

        // 内部获取审核任务
        if (comeFrom == 2) {
            if (StringUtils.isNotBlank(claimCaseObject.getCheckAuditer()) && !claimCaseObject.getCheckAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getCheckAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX22.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX32.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getCheckAuditer())) {
                    claimCaseObject.setCheckAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        }

        // 保司获取审核任务
        if (comeFrom == 3) {
            if (StringUtils.isNotBlank(claimCaseObject.getInsAuditer()) && !claimCaseObject.getInsAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getInsAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX35.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getInsAuditer())) {
                    claimCaseObject.setInsAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);
        if (StringUtils.isNotBlank(claimCase.getPolicyPersonId())) {
            PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());
            model.addAttribute("policyPerson", policyPerson);
          /*  String redisPlanCode = policyPerson.getPlanId() + "_" + policyPerson.getPayPremium().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
            String planCode = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ELE_CCIC_PLAN_CODE", redisPlanCode);*/
            Product product = productService.selectByEleProductCode(policyPerson.getPlanId());
            model.addAttribute("product", product);
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseId", claimCase.getId());
        String skipAttach = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "not_search_file_content_type");
        if (StringUtils.isNotBlank(skipAttach)) {
            List<String> skipAttachList = Arrays.asList(skipAttach.split(","));
            paramMap.put("skipAttach", skipAttachList);
        }
        List<ClaimCaseAttach> claimAttachList = claimCaseAttachService.findAttachByParam(paramMap);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(claimAttachList)) {
            claimAttachList.stream().forEach(claimCaseAttach -> {
                claimCaseAttach.setFileObjectId(AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, claimCaseAttach.getFileObjectId(), 3600 * 1000 * 3));
                // 历史数据排在最后面
                if (claimCaseAttach.getAttachIndex() == null) {
                    claimCaseAttach.setAttachIndex(99999);
                }
            });
            Map<String, List<ClaimCaseAttach>> claimAttachMap = claimAttachList.stream().collect(groupingBy(
                    ClaimCaseAttach::getContentType,
                    collectingAndThen(
                            toList(),
                            list -> list.stream().sorted(Comparator.comparing(ClaimCaseAttach::getAttachIndex)).collect(toList())
                    )
            ));
            model.addAttribute("claimAttachMap", claimAttachMap);

        }
        Map<String, Object> imgInfoMap = CaseImgInfoEnum.getImgInfoMap();
        model.addAttribute("imgInfoMaps", imgInfoMap);
        model.addAttribute("claimCaseObject", claimCaseObject);

        if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
            if (bankInfo != null) {
                model.addAttribute("bankInfo", bankInfo);
            }
        }

        if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("readonly", true);
        }

        if (!ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("verifyReadonly", true);
        }

        model.addAttribute("comeFrom", comeFrom);
        model.addAttribute("checkCondition", checkCondition);

        List<ClaimCaseObjectAssessment> objectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(objectAssessmentList)){
            objectAssessmentList.stream().forEach(claimCaseObjectAssessment -> {
                if(claimCaseObjectAssessment.getAssessmentIndex() == null){
                    //历史数据排在最后面
                    claimCaseObjectAssessment.setAssessmentIndex(99999);
                }
            });
            //排序
            objectAssessmentList.sort(Comparator.comparingInt(ClaimCaseObjectAssessment::getType)
                    .thenComparingInt(ClaimCaseObjectAssessment::getCategory)
                    .thenComparingInt(ClaimCaseObjectAssessment::getAssessmentIndex));

            model.addAttribute("objectAssessmentList", objectAssessmentList);
        }

        List<ClaimCaseObjectPayment> objectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
        model.addAttribute("objectPaymentList", JsonTool.genByFastJson(objectPaymentList));

        Map<String, BankInfo> bankInfoMap = new HashMap<>();

        for (ClaimCaseObjectPayment objectPayment : objectPaymentList) {
            if (StringUtils.isNotBlank(objectPayment.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(objectPayment.getBankInfoId());
                if (bankInfo != null) {
                    bankInfoMap.put(objectPayment.getBankInfoId(), bankInfo);
                }
            }
        }
        model.addAttribute("bankInfoMap", JsonTool.genByFastJson(bankInfoMap));


        if (comeFrom == 1) {
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
            claimCaseObjectLog.setPosition(position);
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(claimCaseObject.getStatus().startsWith("BAX2") ? "估损开始任务" : "理算开始任务");
            claimCaseObjectLog.setCreator(realName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogService.insertSelective(claimCaseObjectLog);
        }

        // 理算初审车损
        // 查询车损日志参数
        Map<String, Object> logParamMap = new HashMap<>();
        logParamMap.put("claimCaseObjectId", claimCaseObject.getId()); //不能为空
        logParamMap.put("position", ShiroSessionUtil.getLoginRoleSession().get(0).getName());
        // 查询车损日志
        List<ClaimCaseObjectLog4CarVo> claimCaseObjectLog4CarVoList = claimCaseObjectLogService.findlog4Car(logParamMap);
        // 查询历史案件
        if (StringUtils.isNotBlank(claimCase.getBaseUserId())) {
            paramMap.clear();
            paramMap.put("baseUserId", claimCase.getBaseUserId());
            List<ClaimCase> historyCase = claimCaseService.findByTreatParam(paramMap);
            model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(historyCase) ? historyCase.size() : 0);
            model.addAttribute("historyCaseList", historyCase);
        }
        // 查询损失金额
        List<InsuranceClaimAssessmentVo> costList;
        Map<String, Object> costParamMap = new HashMap<>();
        costParamMap.put("claimCaseObjectId", claimCaseObject.getId());
        costList = insuranceClaimAssessmentService.selectByParam (costParamMap);
        costList.forEach(insuranceClaimAssessmentVo -> insuranceClaimAssessmentVo.setAssessmentType(CarAssessmentType.nameToMsg(insuranceClaimAssessmentVo.getAssessmentType())));
        model.addAttribute("costList", costList);
        model.addAttribute("logList", claimCaseObjectLog4CarVoList);
        model.addAttribute("paymentList", objectPaymentList);
        model.addAttribute("caseId", claimCase.getId());//案件id 必须要
        model.addAttribute("isShowUploadBotton", "0");//是否显示上传影像 1：是；0：否
        model.addAttribute("isShowDeleteDir", "0");//是否显示删除的文件夹 1：是；0：否
        model.addAttribute("isShowDeleteButton", "0");//是否显示影像删除按钮 1：是；0：否

        return "accountingAuditTask/accountingTaskDetail";
    }

    /**
     * 页面跳转->提交确认页面
     */
    @RequestMapping(value = "submitConfirm")
    public String submitConfirm(String reportNo, String insCode, String checkCondition, String claimCaseObjectId, Model model) {
        logger.info(">>>>>>>>>>理算初审通过: 报案号：{}", reportNo);
        if (reportNo != null && !reportNo.trim().isEmpty()) {
            ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);
            try {
                // 设置提交确认页面的字段信息
                Map<String, String> submitInfo = new HashMap<>();
                submitInfo.put("reportNo", reportNo);
                submitInfo.put("taskType", "理算初审");
                submitInfo.put("taskStatus", "已提交");
                if (ClaimCaseObjectStatusEnum.BAX35.getCode().equals(claimCaseObject.getStatus())) {
                    submitInfo.put("acceptDepartment", "理算复核");
                } else {
                    submitInfo.put("acceptDepartment", "/");
                }
                submitInfo.put("insCode", insCode);
                submitInfo.put("checkCondition", checkCondition);
                model.addAttribute("submitInfo", submitInfo);

            } catch (Exception e) {
                e.printStackTrace();
                model.addAttribute("error", "系统异常：" + e.getMessage());
            }
        } else {
            model.addAttribute("error", "报案号不能为空");
        }
        return "accountingAuditTask/submitConfirm";
    }

    /**
     * 车损理算开始任务
     * @param id
     * @param comeFrom
     * @param checkCondition
     * @param model
     * @return
     */
    @RequestMapping(value = {"carStartTask", ""})
    public String carStartTask(String id, Integer comeFrom,String checkCondition, Model model) {

        String userId = ShiroSessionUtil.getLoginSession().getId();
        String realName = ShiroSessionUtil.getLoginSession().getRealName();
        String position = ShiroSessionUtil.getLoginRoleSession().get(0).getName();

        if (StringUtils.isBlank(id) || comeFrom == null) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        if (comeFrom != 1) {
            model.addAttribute("exception", "流程异常，请联系管理员");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }
        if (claimCaseObject.getCategory() != 3) {
            model.addAttribute("exception", "流程异常，请联系管理员");
            return "error/500";
        }
        if (claimCaseObject.getIsCaseClosed() == 1) {
            model.addAttribute("exception", "该估损单案件已关闭");
            return "error/500";
        }
        model.addAttribute("claimCaseObject", claimCaseObject);

        // 理算允许开始任务的状态
        List<String> statusList = new ArrayList<>();
        statusList.add(ClaimCaseObjectStatusEnum.BAX31.getCode());
        statusList.add(ClaimCaseObjectStatusEnum.BAX34.getCode());
        statusList.add(ClaimCaseObjectStatusEnum.BAX37.getCode());

        if (!statusList.contains(claimCaseObject.getStatus())) {
            model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            return "error/500";
        }

        // 非新案件流程直接抛出异常
        if (!"1".equals(claimCaseObject.getIsNewCase())) {
            model.addAttribute("exception", "流程异常，请联系管理员");
            return "error/500";
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseId", claimCase.getId());
        String skipAttach = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "not_search_file_content_type");
        if (StringUtils.isNotBlank(skipAttach)) {
            List<String> skipAttachList = Arrays.asList(skipAttach.split(","));
            paramMap.put("skipAttach", skipAttachList);
        }
        List<ClaimCaseAttach> claimAttachList = claimCaseAttachService.findAttachByParam(paramMap);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(claimAttachList)) {
            claimAttachList.stream().forEach(claimCaseAttach -> {
                claimCaseAttach.setFileObjectId(AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, claimCaseAttach.getFileObjectId(), 3600 * 1000 * 3));
                // 历史数据排在最后面
                if (claimCaseAttach.getAttachIndex() == null) {
                    claimCaseAttach.setAttachIndex(99999);
                }
            });
            Map<String, List<ClaimCaseAttach>> claimAttachMap = claimAttachList.stream().collect(groupingBy(
                    ClaimCaseAttach::getContentType,
                    collectingAndThen(
                            toList(),
                            list -> list.stream().sorted(Comparator.comparing(ClaimCaseAttach::getAttachIndex)).collect(toList())
                    )
            ));
            model.addAttribute("claimAttachMap", claimAttachMap);
        }
        Map<String, Object> imgInfoMap = CaseImgInfoEnum.getImgInfoMap();
        model.addAttribute("imgInfoMaps", imgInfoMap);

        // 历史案件量
        paramMap.clear();
        paramMap.put("baseUserId", claimCase.getBaseUserId());
        List<ClaimCase> claimCaseList = claimCaseService.findByTreatParam(paramMap);
        model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(claimCaseList) ? claimCaseList.size() : 0);


        if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
            if (bankInfo != null) {
                model.addAttribute("bankInfo", bankInfo);
            }
        }

        model.addAttribute("comeFrom", comeFrom);
        model.addAttribute("checkCondition", checkCondition);

        // 流程环节类型
        Map<String, String> assessmentTypeMap = CarAssessmentType.getCarAssessmentTypeMap();
        model.addAttribute("assessmentTypeMap", assessmentTypeMap);

        // 查询车损明细
        List<InsuranceClaimAssessmentVo> insuranceClaimAssessmentList = insuranceClaimAssessmentService.findByObjectId(claimCaseObject.getId());
        // 根据环节进行分组
        Map<String, InsuranceClaimAssessmentVo> insuranceClaimAssessmentMap = insuranceClaimAssessmentList.stream().collect(toMap(x -> x.getAssessmentType(), x -> x));
        model.addAttribute("insuranceClaimAssessmentMap", insuranceClaimAssessmentMap);

        List<ClaimCaseObjectPayment> objectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
        if (CollectionUtils.isNotEmpty(objectPaymentList)) {
            objectPaymentList.stream().forEach(payment -> {
                if (payment.getIdNumStartDate() != null) {
                    if (payment.getIdNumEndDate() != null) {
                        // 计算年份差
                        int endYear = payment.getIdNumEndDate().getYear();
                        int startYear = payment.getIdNumStartDate().getYear();
                        payment.setRemark(String.valueOf(endYear - startYear));
                    } else {
                        // 证件长期有效
                        payment.setRemark("-1");
                    }
                }
            });
        }
        model.addAttribute("objectPaymentList", objectPaymentList);

        Map<String, BankInfo> bankInfoMap = new HashMap<>();

        for (ClaimCaseObjectPayment objectPayment : objectPaymentList) {
            if (StringUtils.isNotBlank(objectPayment.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(objectPayment.getBankInfoId());
                if (bankInfo != null) {
                    bankInfoMap.put(objectPayment.getBankInfoId(), bankInfo);
                }
            }
        }
        model.addAttribute("bankInfoMap", JsonTool.genByFastJson(bankInfoMap));


        ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
        claimCaseObjectLog.setId(Tool.uuid());
        claimCaseObjectLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
        claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
        claimCaseObjectLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
        claimCaseObjectLog.setPosition(position);
        claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
        claimCaseObjectLog.setDescription("理算开始任务");
        claimCaseObjectLog.setCreator(realName + "-" + userId);
        claimCaseObjectLog.setCreateTime(new Date());
        claimCaseObjectLogService.insertSelective(claimCaseObjectLog);

        Map<String, String> isValidityMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "VALIDITY_CERTIFICATE_INSCODE");
        if (isValidityMap.containsKey(claimCase.getInsCode())) {
            model.addAttribute("isValidity", true);
        }

        model.addAttribute("caseId", claimCase.getId());     //案件id 必须要
        model.addAttribute("isShowUploadBotton","1");//是否显示上传影像 1：是；0：否
        model.addAttribute("isShowDeleteDir","1");//是否显示删除的文件夹 1：是；0：否
        model.addAttribute("isShowDeleteButton","1");//是否显示影像删除按钮 1：是；0：否

        return "objectCarProcess/settlementTaskDetail";
    }

    /**
     * 车损理算提交
     * @param json
     * @param request
     * @return
     */
    @RequestMapping(value = {"carVerifyObjectSubmit", ""})
    @ResponseBody
    public String carVerifyObjectSubmit(@RequestBody String json, HttpServletRequest request) {

        logger.info(">>>>>>>>>>调用carVerifyObjectSubmit出险对象信息处理接口开始，json：{}", json);
        try {
            ClaimCaseObjectReq claimCaseObjectReq = JSON.parseObject(json, new TypeReference<ClaimCaseObjectReq>() {});

            String userId = ShiroSessionUtil.getLoginSession().getId();
            String userName = ShiroSessionUtil.getLoginSession().getRealName(); // 审核人员真实姓名
            String roleName= ShiroSessionUtil.getLoginRoleSession().get(0).getName();// 岗位角色名称

            List<ClaimCaseObject> updateClaimCaseObjectList = new ArrayList<>();
            List<InsuranceClaimAssessment> addInsuranceClaimassessmentList = new ArrayList<>();
            List<InsuranceClaimAssessment> updInsuranceClaimassessmentList = new ArrayList<>();
            List<ClaimCaseObjectAssessment> updateClaimCaseObjectAssessmentList = new ArrayList<>();
            List<ClaimCaseObjectPayment> addClaimCaseObjectPaymentList = new ArrayList<>();
            List<ClaimCaseObjectPayment> updateClaimCaseObjectPaymentList = new ArrayList<>();
            List<ClaimCaseObjectPayment> deleteClaimCaseObjectPaymentList = new ArrayList<>();
            List<ClaimCaseObjectLog4CarReq> insertClaimCaseObjectLog4CarList = new ArrayList<>();
            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();

            // 获取object页面填写数据
            ClaimCaseObject newObject = new ClaimCaseObject();
            BeanUtils.copyProperties(claimCaseObjectReq, newObject);

            // 原估损对象数据
            ClaimCaseObject oldObject = claimCaseObjectService.selectByPrimaryKey(newObject.getId());
            if (oldObject == null) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "赔付对象不存在，objectId：" + newObject.getId());
                }});
            }

            // 传输的状态
            String status = newObject.getStatus();

            if (StringUtils.isAnyBlank(newObject.getId(), status)) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "估损id与状态不能为空");
                }});
            }

            if (ClaimCaseObjectStatusEnum.BAX31.getCode().equals(status) || ClaimCaseObjectStatusEnum.BAX32.getCode().equals(status)) {
                if (!ClaimCaseObjectStatusEnum.BAX31.getCode().equals(oldObject.getStatus())
                        && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(oldObject.getStatus())
                        && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(oldObject.getStatus()))
                {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX35.getCode().equals(status) && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(oldObject.getStatus())) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "当前任务状态已流转，请更换任务！");
                }});
            }

            // 重复幂等校验
            List<ClaimCaseObject> existList = claimCaseObjectService.findByUnique(new HashMap<String, Object>() {{
                this.put("claimCaseId", oldObject.getClaimCaseId());
                this.put("type", oldObject.getType());
                this.put("category", oldObject.getCategory());
                this.put("name", oldObject.getName());
            }});


            if (CollectionUtils.isNotEmpty(existList)) {
                if (existList.size() != 1 || !existList.get(0).getId().equals(newObject.getId())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "赔付对象已存在");
                    }});
                }
            }

            // 理算明细项
            InsuranceClaimAssessment insuranceClaimAssessment = claimCaseObjectReq.getInsuranceClaimAssessment();
            insuranceClaimAssessment.setAssessmentType(CarAssessmentType.msgToName(insuranceClaimAssessment.getAssessmentType()));

            // 最新领款信息列表
            List<ClaimCaseObjectPayment> newClaimCaseObjectPaymentList = claimCaseObjectReq.getClaimCaseObjectPaymentList();
            // 原领款信息列表
            List<ClaimCaseObjectPayment> oldClaimCaseObjectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(newObject.getId());

            // 老表明细项
            List<ClaimCaseObjectAssessment> claimCaseObjectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(newObject.getId());
            if (CollectionUtils.isNotEmpty(claimCaseObjectAssessmentList)) {
                // 根据类型分组
                Map<String, List<ClaimCaseObjectAssessment>> objectAssessmentMap = claimCaseObjectAssessmentList.stream().collect(groupingBy(ClaimCaseObjectAssessment::getCode));
                Map<String, BigDecimal> updAssessment = new HashMap<>();
                for (String key : objectAssessmentMap.keySet()) {
                    ClaimCaseObjectAssessment claimCaseObjectAssessment = objectAssessmentMap.get(key).get(0);
                    if (EstimateInventoryEnum.维修配件.getCode().equals(claimCaseObjectAssessment.getCode())) {
                        updAssessment.put(claimCaseObjectAssessment.getId(), insuranceClaimAssessment.getTotalPartsAmount());       // 维修配件合计
                    }
                    if (EstimateInventoryEnum.维修人工.getCode().equals(claimCaseObjectAssessment.getCode())) {
                        updAssessment.put(claimCaseObjectAssessment.getId(), insuranceClaimAssessment.getTotalLaborAmount());       // 维修人工合计
                    }
                }
                claimCaseObjectAssessmentList.stream().forEach(assessment -> {
                    if (updAssessment.containsKey(assessment.getId())) {
                        assessment.setVerifyAmount(updAssessment.get(assessment.getId()));
                    } else {
                        assessment.setVerifyAmount(BigDecimal.ZERO);
                    }
                });
                claimCaseObjectReq.setClaimCaseObjectAssessmentList(claimCaseObjectAssessmentList);
                updateClaimCaseObjectAssessmentList.addAll(claimCaseObjectAssessmentList);
            }


            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(newClaimCaseObjectPaymentList)) {
                // 新增领款信息列表
                addClaimCaseObjectPaymentList = newClaimCaseObjectPaymentList.stream().filter(payment -> StringUtils.isBlank(payment.getId())).collect(Collectors.toList());

                // 更新领款信息列表
                updateClaimCaseObjectPaymentList = newClaimCaseObjectPaymentList.stream().filter(payment -> StringUtils.isNotBlank(payment.getId())).collect(Collectors.toList());

                // 更新列表id
                List<String> updateIdList = updateClaimCaseObjectPaymentList.stream().map(ClaimCaseObjectPayment::getId).collect(Collectors.toList());

                // 删除领款信息列表
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oldClaimCaseObjectPaymentList)) {
                    deleteClaimCaseObjectPaymentList = oldClaimCaseObjectPaymentList.stream().filter(payment -> !updateIdList.contains(payment.getId())).collect(Collectors.toList());
                }
            } else {
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oldClaimCaseObjectPaymentList)) {
                    deleteClaimCaseObjectPaymentList = oldClaimCaseObjectPaymentList;
                }
            }




            for (ClaimCaseObjectPayment claimCaseObjectPayment : addClaimCaseObjectPaymentList) {
                claimCaseObjectPayment.setId(Tool.uuid());
                claimCaseObjectPayment.setClaimCaseObjectId(oldObject.getId());
                claimCaseObjectPayment.setClaimCaseId(oldObject.getClaimCaseId());
                claimCaseObjectPayment.setClaimCaseNo(oldObject.getClaimCaseNo());
                claimCaseObjectPayment.setCreateTime(new Date());
                claimCaseObjectPayment.setStatus("2");
            }

            // 打印结果描述日志
            String description = SpringContextHolder.getBean(ClaimCaseObjectV2Controller.class).getResultDescription(claimCaseObjectReq);
            if ("-1".equals(description)) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "系统异常,描述转换失败:");
                }});
            }

            if (oldObject.getInsCode().equals("HX") && StringUtils.isNotBlank(newObject.getTreatIdNum()) && StringUtils.isBlank(newObject.getTreatIdType())){
                newObject.setTreatIdType("身份证");
            }
            // 更新估损信息
            updateClaimCaseObjectList.add(newObject);

            // 更新/新增理算明细项
            if (StringUtils.isNotBlank(insuranceClaimAssessment.getId())) {
                // 更新
                insuranceClaimAssessment.setUpdateTime(LocalDateTime.now());
                updInsuranceClaimassessmentList.add(insuranceClaimAssessment);
            } else {
                // 新增
                insuranceClaimAssessment.setId(Tool.uuid());
                insuranceClaimAssessment.setCreateTime(LocalDateTime.now());
                addInsuranceClaimassessmentList.add(insuranceClaimAssessment);
            }

            // 赔付对象日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(oldObject.getId());
            claimCaseObjectLog.setClaimCaseId(oldObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseNo(oldObject.getClaimCaseNo());
            claimCaseObjectLog.setStatus(newObject.getStatus());
            claimCaseObjectLog.setDescription(description);
            claimCaseObjectLog.setCreator(userName +"-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);

            BigDecimal verifyMoney = BigDecimal.ZERO;     // 理算总金额
            if (CollectionUtils.isNotEmpty(addClaimCaseObjectPaymentList)) {
                BigDecimal bigDecimal = addClaimCaseObjectPaymentList.stream().map(ClaimCaseObjectPayment::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_DOWN);
                verifyMoney = verifyMoney.add(bigDecimal);
            }
            if (CollectionUtils.isNotEmpty(updateClaimCaseObjectPaymentList)) {
                BigDecimal bigDecimal = updateClaimCaseObjectPaymentList.stream().map(ClaimCaseObjectPayment::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_DOWN);
                verifyMoney = verifyMoney.add(bigDecimal);
            }

            // 车损流转日志
            ClaimCaseObjectLog4CarReq claimCaseObjectLog4CarReq = new ClaimCaseObjectLog4CarReq();
            claimCaseObjectLog4CarReq.setClaimCaseObjectId(oldObject.getId());
            claimCaseObjectLog4CarReq.setMoney(verifyMoney.toPlainString());
            if (ClaimCaseObjectStatusEnum.BAX31.getCode().equals(newObject.getStatus())) {
                claimCaseObjectLog4CarReq.setOperatingMode(ClaimCaseObjectLogStatus4CarEnum.理算保存.getCode());
            }else if(ClaimCaseObjectStatusEnum.BAX32.getCode().equals(newObject.getStatus()) || ClaimCaseObjectStatusEnum.BAX35.getCode().equals(newObject.getStatus())){
                claimCaseObjectLog4CarReq.setOperatingMode(ClaimCaseObjectLogStatus4CarEnum.理算提审.getCode());
            }
            claimCaseObjectLog4CarReq.setDealOpinion(claimCaseObjectReq.getLogRemark());
            claimCaseObjectLog4CarReq.setCreator(userId);
            insertClaimCaseObjectLog4CarList.add(claimCaseObjectLog4CarReq);

            // 案件日志
            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(oldObject.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(oldObject.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(roleName);
            claimCaseLog.setType(ClaimCaseLogTypeEnum.客服操作估损清单.getCode());
            claimCaseLog.setStatus(newObject.getStatus());
            claimCaseLog.setDescription(description);
            claimCaseLog.setReqData(JsonTool.genByFastJson(newObject));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());
            insertClaimCaseLogList.add(claimCaseLog);



            // 入库sub
            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectList(updateClaimCaseObjectList);
            assessmentDataSup.setUpdateClaimCaseObjectAssessmentList(updateClaimCaseObjectAssessmentList);
            assessmentDataSup.setInsertInsuranceClaimAssessmentList(addInsuranceClaimassessmentList);
            assessmentDataSup.setUpdateInsuranceClaimAssessmentList(updInsuranceClaimassessmentList);
            assessmentDataSup.setInsertClaimCaseObjectPaymentList(addClaimCaseObjectPaymentList);
            assessmentDataSup.setUpdateClaimCaseObjectPaymentList(updateClaimCaseObjectPaymentList);
            assessmentDataSup.setDeleteClaimCaseObjectPaymentList(deleteClaimCaseObjectPaymentList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);
            assessmentDataSup.setInsertClaimCaseObjectLog4CarList(insertClaimCaseObjectLog4CarList);

            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);



            // "理算初审进行中"状态 && 保司为富邦，是，则根据估损金额判断是否需要修改状态
            if (ClaimCaseObjectStatusEnum.BAX32.getCode().equals(newObject.getStatus()) && "FB".equals(oldObject.getInsCode())) {
                String money = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA", oldObject.getInsCode() + "_ESTIMATEDLOSS_MONEY");     // 理算金额界限
                // 如果理算金额 <= 估损总金额，则直接进入理算保司审核
                if (StringUtils.isNotBlank(money) && new BigDecimal(money).compareTo(oldObject.getLossAssessmentSum()) <= 0) {
                    // 更新claimCaseObject的modifyTime
                    ClaimCaseObject claimCaseObjectUpdate = new ClaimCaseObject();
                    claimCaseObjectUpdate.setId(newObject.getId());
                    claimCaseObjectUpdate.setStatus(ClaimCaseObjectStatusEnum.BAX35.getCode());
                    claimCaseObjectUpdate.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObjectUpdate);

                    claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX35.getCode());
                }

            }
            Map<String, Object> resMap = new HashMap<>();
            resMap.put("ret", "0");
            resMap.put("msg", "成功");
            resMap.put("status", claimCaseObjectReq.getStatus());       // 用于调整页面进入流程显示
            return JsonBizTool.genJson(resMap);

        } catch (Exception e) {
            logger.error("系统异常！出险对象信息入库失败");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }
    }

    /**
     * 三者车损理算提交后跳转确认页面
     * @param reportNo
     * @param insCode
     * @param status
     * @param model
     * @return
     */
    @RequestMapping(value = "carVerifySubmitConfirmPage")
    public String carVerifySubmitConfirmPage(String reportNo, String insCode, String status, Model model) {
        logger.info(">>>>>>>>>>carVerifySubmitConfirmPage 理算处理提交: 报案号：{}", reportNo);
        if (reportNo != null && !reportNo.trim().isEmpty()) {
            try {
                // 设置提交确认页面的字段信息
                Map<String, String> submitInfo = new HashMap<>();
                submitInfo.put("reportNo", reportNo);
                submitInfo.put("taskType", "理算处理");
                submitInfo.put("taskStatus", "已提交");
                if (ClaimCaseObjectStatusEnum.BAX35.getCode().equals(status)) {
                    submitInfo.put("acceptDepartment", "理算复核");
                }else {
                    submitInfo.put("acceptDepartment", "理算初审");
                }
                submitInfo.put("insCode", insCode);

                model.addAttribute("submitInfo", submitInfo);

            } catch (Exception e) {
                e.printStackTrace();
                model.addAttribute("error", "系统异常：" + e.getMessage());
            }
        } else {
            model.addAttribute("error", "报案号不能为空");
        }

        return "carDamage/submitPage";
    }

    /**
     * 保司开始审核
     * @param id
     * @param comeFrom
     * @param checkCondition
     * @param model
     * @return
     */
    @RequestMapping(value = {"startTask4BS", ""})
    public String startTask4BS(String id, Integer comeFrom, String checkCondition, Model model) {

        String userId = ShiroSessionUtil.getLoginSession().getId();
        String realName = ShiroSessionUtil.getLoginSession().getRealName();
        String position = ShiroSessionUtil.getLoginRoleSession().get(0).getName();

        if (StringUtils.isBlank(id) || comeFrom == null) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }
        if (claimCaseObject.getIsCaseClosed() == 1) {
            model.addAttribute("exception", "该估损单案件已关闭");
            return "error/500";
        }

        // 保司获取审核任务
        if (comeFrom == 3) {
            if (StringUtils.isNotBlank(claimCaseObject.getInsAuditer()) && !claimCaseObject.getInsAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getInsAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX35.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getInsAuditer())) {
                    claimCaseObject.setInsAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        } else {
            return "error/403";
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);
        if (StringUtils.isNotBlank(claimCase.getPolicyPersonId())) {
            PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());
            model.addAttribute("policyPerson", policyPerson);
            Product product = productService.selectByEleProductCode(policyPerson.getPlanId());
            model.addAttribute("product", product);
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseId", claimCase.getId());
        String skipAttach = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "not_search_file_content_type");
        if (StringUtils.isNotBlank(skipAttach)) {
            List<String> skipAttachList = Arrays.asList(skipAttach.split(","));
            paramMap.put("skipAttach", skipAttachList);
        }
        List<ClaimCaseAttach> claimAttachList = claimCaseAttachService.findAttachByParam(paramMap);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(claimAttachList)) {
            claimAttachList.stream().forEach(claimCaseAttach -> {
                claimCaseAttach.setFileObjectId(AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, claimCaseAttach.getFileObjectId(), 3600 * 1000 * 3));
                // 历史数据排在最后面
                if (claimCaseAttach.getAttachIndex() == null) {
                    claimCaseAttach.setAttachIndex(99999);
                }
            });
            Map<String, List<ClaimCaseAttach>> claimAttachMap = claimAttachList.stream().collect(groupingBy(
                    ClaimCaseAttach::getContentType,
                    collectingAndThen(
                            toList(),
                            list -> list.stream().sorted(Comparator.comparing(ClaimCaseAttach::getAttachIndex)).collect(toList())
                    )
            ));
            model.addAttribute("claimAttachMap", claimAttachMap);

        }
        Map<String, Object> imgInfoMap = CaseImgInfoEnum.getImgInfoMap();
        model.addAttribute("imgInfoMaps", imgInfoMap);
        model.addAttribute("claimAttachList", claimAttachList);
        model.addAttribute("claimCaseObject", claimCaseObject);

        if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
            if (bankInfo != null) {
                model.addAttribute("bankInfo", bankInfo);
            }
        }

        if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("readonly", true);
        }

        if (!ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("verifyReadonly", true);
        }

        model.addAttribute("comeFrom", comeFrom);
        model.addAttribute("checkCondition", checkCondition);

        List<ClaimCaseObjectAssessment> objectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(objectAssessmentList)){

            objectAssessmentList.stream().forEach(claimCaseObjectAssessment -> {
                if(claimCaseObjectAssessment.getAssessmentIndex() == null){
                    //历史数据排在最后面
                    claimCaseObjectAssessment.setAssessmentIndex(99999);
                }
            });
            //排序
            objectAssessmentList.sort(Comparator.comparingInt(ClaimCaseObjectAssessment::getType)
                    .thenComparingInt(ClaimCaseObjectAssessment::getCategory)
                    .thenComparingInt(ClaimCaseObjectAssessment::getAssessmentIndex));

            model.addAttribute("objectAssessmentList", objectAssessmentList);
        }


        List<ClaimCaseObjectPayment> objectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
        model.addAttribute("objectPaymentList", JsonTool.genByFastJson(objectPaymentList));

        Map<String, BankInfo> bankInfoMap = new HashMap<>();

        for (ClaimCaseObjectPayment objectPayment : objectPaymentList) {
            if (StringUtils.isNotBlank(objectPayment.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(objectPayment.getBankInfoId());
                if (bankInfo != null) {
                    bankInfoMap.put(objectPayment.getBankInfoId(), bankInfo);
                }
            }
        }
        model.addAttribute("bankInfoMap", JsonTool.genByFastJson(bankInfoMap));

        // 预估金额
        List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.findByParam(paramMap);
        if(CollectionUtils.isNotEmpty(claimCaseObjectList)){
            BigDecimal objectAppraisalAmount = BigDecimal.ZERO;
            for (ClaimCaseObject Object : claimCaseObjectList) {
                objectAppraisalAmount = objectAppraisalAmount.add(Object.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : Object.getEstimatedApprovedMoney());
            }
            model.addAttribute("objectAppraisalAmount",objectAppraisalAmount);
        }

        paramMap.clear();
        paramMap.put("baseUserId", claimCase.getBaseUserId());
        List<ClaimCase> claimCaseList = claimCaseService.findByTreatParam(paramMap);
        model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(claimCaseList) ? claimCaseList.size() : 0);


        Map<String, String> isValidityMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "VALIDITY_CERTIFICATE_INSCODE");
        if (isValidityMap.containsKey(claimCase.getInsCode())) {
            model.addAttribute("isValidity", true);
        }
        Map<String, Object> enumMapByParentCode = new HashMap<>();
        if (claimCaseObject.getCategory() == 1) {     // 人伤
            enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("1");
            model.addAttribute("enumMapByParentCode", enumMapByParentCode);
            return "claimCase4BS/objectGatherDetail4BS";
        }

        enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("2");
        model.addAttribute("enumMapByParentCode", enumMapByParentCode);
        return "claimCase4BS/objectGatherGoodsDetail4BS";
    }

    /**
     * 车损保司理算开始审核
     * @param id
     * @param comeFrom
     * @param checkCondition
     * @param model
     * @return
     */
    @RequestMapping(value = {"startTaskAdjustment4BS", ""})
    public String startTaskAdjustment4BS(String id, Integer comeFrom, String checkCondition, Model model) {

        String userId = ShiroSessionUtil.getLoginSession().getId();
        String realName = ShiroSessionUtil.getLoginSession().getRealName();
        String position = ShiroSessionUtil.getLoginRoleSession().get(0).getName();

        if (StringUtils.isBlank(id) || comeFrom == null) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }
        if (claimCaseObject.getIsCaseClosed() == 1) {
            model.addAttribute("exception", "该估损单案件已关闭");
            return "error/500";
        }

        // 保司获取审核任务
        if (comeFrom == 3) {
            if (StringUtils.isNotBlank(claimCaseObject.getInsAuditer()) && !claimCaseObject.getInsAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getInsAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX35.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getInsAuditer())) {
                    claimCaseObject.setInsAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        } else {
            return "error/403";
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);
        if (StringUtils.isNotBlank(claimCase.getPolicyPersonId())) {
            PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());
            if(policyPerson != null){
                Product product = productService.selectByEleProductCode(policyPerson.getPlanId());
                model.addAttribute("policyPerson", policyPerson);
                model.addAttribute("product", product);
            }else{
                model.addAttribute("policyPerson", new PolicyPerson());
                model.addAttribute("product", new Product());
            }
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseId", claimCase.getId());
        String skipAttach = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "not_search_file_content_type");
        if (StringUtils.isNotBlank(skipAttach)) {
            List<String> skipAttachList = Arrays.asList(skipAttach.split(","));
            paramMap.put("skipAttach", skipAttachList);
        }
        List<ClaimCaseAttach> claimAttachList = claimCaseAttachService.findAttachByParam(paramMap);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(claimAttachList)) {
            claimAttachList.stream().forEach(claimCaseAttach -> {
                claimCaseAttach.setFileObjectId(AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, claimCaseAttach.getFileObjectId(), 3600 * 1000 * 3));
                // 历史数据排在最后面
                if (claimCaseAttach.getAttachIndex() == null) {
                    claimCaseAttach.setAttachIndex(99999);
                }
            });
            Map<String, List<ClaimCaseAttach>> claimAttachMap = claimAttachList.stream().collect(groupingBy(
                    ClaimCaseAttach::getContentType,
                    collectingAndThen(
                            toList(),
                            list -> list.stream().sorted(Comparator.comparing(ClaimCaseAttach::getAttachIndex)).collect(toList())
                    )
            ));
            model.addAttribute("claimAttachMap", claimAttachMap);

        }
        Map<String, Object> imgInfoMap = CaseImgInfoEnum.getImgInfoMap();
        model.addAttribute("imgInfoMaps", imgInfoMap);
        model.addAttribute("claimAttachList", claimAttachList);
        model.addAttribute("claimCaseObject", claimCaseObject);

        if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
            if (bankInfo != null) {
                model.addAttribute("bankInfo", bankInfo);
            }
        }

        if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("readonly", true);
        }

        if (!ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("verifyReadonly", true);
        }

        model.addAttribute("comeFrom", comeFrom);
        model.addAttribute("checkCondition", checkCondition);

        List<ClaimCaseObjectAssessment> objectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(objectAssessmentList)){

            objectAssessmentList.stream().forEach(claimCaseObjectAssessment -> {
                if(claimCaseObjectAssessment.getAssessmentIndex() == null){
                    //历史数据排在最后面
                    claimCaseObjectAssessment.setAssessmentIndex(99999);
                }
            });
            //排序
            objectAssessmentList.sort(Comparator.comparingInt(ClaimCaseObjectAssessment::getType)
                    .thenComparingInt(ClaimCaseObjectAssessment::getCategory)
                    .thenComparingInt(ClaimCaseObjectAssessment::getAssessmentIndex));

            model.addAttribute("objectAssessmentList", objectAssessmentList);
        }


        List<ClaimCaseObjectPayment> objectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
        model.addAttribute("objectPaymentList", JsonTool.genByFastJson(objectPaymentList));

        Map<String, BankInfo> bankInfoMap = new HashMap<>();

        for (ClaimCaseObjectPayment objectPayment : objectPaymentList) {
            if (StringUtils.isNotBlank(objectPayment.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(objectPayment.getBankInfoId());
                if (bankInfo != null) {
                    bankInfoMap.put(objectPayment.getBankInfoId(), bankInfo);
                }
            }
        }
        model.addAttribute("bankInfoMap", JsonTool.genByFastJson(bankInfoMap));

        // 预估金额
        List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.findByParam(paramMap);
        BigDecimal objectAppraisalAmount = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(claimCaseObjectList)){
            for (ClaimCaseObject Object : claimCaseObjectList) {
                objectAppraisalAmount = objectAppraisalAmount.add(Object.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : Object.getEstimatedApprovedMoney());
            }
            model.addAttribute("objectAppraisalAmount",objectAppraisalAmount);
        }

        Map<String, String> isValidityMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "VALIDITY_CERTIFICATE_INSCODE");
        if (isValidityMap.containsKey(claimCase.getInsCode())) {
            model.addAttribute("isValidity", true);
        }
        Map<String, Object> enumMapByParentCode = new HashMap<>();
        if (claimCaseObject.getCategory() == 1) {     // 人伤
            enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("1");
            model.addAttribute("enumMapByParentCode", enumMapByParentCode);
            return "claimCase4BS/objectGatherDetail4BS";
        }

        // 查询车损日志参数
        Map<String, Object> costParamMap = new HashMap<>();
        costParamMap.put("claimCaseObjectId",claimCaseObject.getId());//不能为空
        costParamMap.put("position",position);//选填，主要是保司端自能看自己相关的日志使用

        // 查询车损日志
        List<ClaimCaseObjectLog4CarVo> claimCaseObjectLog4CarVoList = claimCaseObjectLogService.findlog4Car(costParamMap);

        // 查询历史案件
        if (StringUtils.isNotBlank(claimCase.getBaseUserId())) {
            paramMap.clear();
            paramMap.put("baseUserId", claimCase.getBaseUserId());
            List<ClaimCase> historyCase = claimCaseService.findByTreatParam(paramMap);
            model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(historyCase) ? historyCase.size() : 0);
            model.addAttribute("historyCaseList", historyCase);
        }

        if(claimCaseObject.getCategory() == 3 && comeFrom == 3){
            // 查询损失金额
            List<InsuranceClaimAssessmentVo> costList = insuranceClaimAssessmentService.selectByParam (costParamMap);
            costList.forEach(insuranceClaimAssessmentVo -> insuranceClaimAssessmentVo.setAssessmentType(CarAssessmentType.nameToMsg(insuranceClaimAssessmentVo.getAssessmentType())));
            model.addAttribute("costList", costList);
            model.addAttribute("logList", claimCaseObjectLog4CarVoList);
            model.addAttribute("paymentList", objectPaymentList);
            model.addAttribute("caseId",claimCase.getId());//案件id 必须要
            model.addAttribute("isShowUploadBotton","0");//是否显示上传影像 1：是；0：否
            model.addAttribute("isShowDeleteDir","0");//是否显示删除的文件夹 1：是；0：否
            model.addAttribute("isShowDeleteButton","0");//是否显示影像删除按钮 1：是；0：否
            return "accountingAuditTask/adjustmentTaskDetail";
        }

        enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("2");
        model.addAttribute("enumMapByParentCode", enumMapByParentCode);
        return "claimCase4BS/objectGatherGoodsDetail4BS";
    }

    /**
     * 车损保司理算提交
     */
    @RequestMapping(value = "submitConfirm4BS")
    public String submitConfirm4BS(String reportNo, String insCode, String checkCondition, Model model) {
        logger.info(">>>>>>>>>>理算复核通过: 报案号：{}", reportNo);
        if (reportNo != null && !reportNo.trim().isEmpty()) {
            try {
                // 设置提交确认页面的字段信息
                Map<String, String> submitInfo = new HashMap<>();
                submitInfo.put("reportNo", reportNo);
                submitInfo.put("taskType", "理算复核");
                submitInfo.put("taskStatus", "已提交");
                submitInfo.put("acceptDepartment", "/");
                submitInfo.put("insCode", insCode);
                submitInfo.put("checkCondition", checkCondition);
                model.addAttribute("submitInfo", submitInfo);

            } catch (Exception e) {
                e.printStackTrace();
                model.addAttribute("error", "系统异常：" + e.getMessage());
            }
        } else {
            model.addAttribute("error", "报案号不能为空");
        }

        return "accountingAuditTask/submitConfirm";
    }

    /**
     * 查看 估损清单
     *
     * @param id
     * @param model
     * @return
     */
    @RequestMapping(value = {"showObject", ""})
    public String showObject(String id, Model model) {

        if (StringUtils.isBlank(id)) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);
        if (StringUtils.isNotBlank(claimCase.getPolicyPersonId())) {
            PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());
            model.addAttribute("policyPerson", policyPerson);
            /*String redisPlanCode = policyPerson.getPlanId() + "_" + policyPerson.getPayPremium().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
            String planCode = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ELE_CCIC_PLAN_CODE", redisPlanCode);
            Product product = productService.selectByCode(planCode);
            model.addAttribute("product", product);*/
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseId", claimCase.getId());
        String skipAttach = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "not_search_file_content_type");
        if (StringUtils.isNotBlank(skipAttach)) {
            List<String> skipAttachList = Arrays.asList(skipAttach.split(","));
            paramMap.put("skipAttach", skipAttachList);
        }
        List<ClaimCaseAttach> claimAttachList = claimCaseAttachService.findAttachByParam(paramMap);
        if (CollectionUtils.isNotEmpty(claimAttachList)) {
            claimAttachList.stream().forEach(claimCaseAttach -> {
                claimCaseAttach.setFileObjectId(AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, claimCaseAttach.getFileObjectId(), 3600 * 1000 * 3));
                // 历史数据排在最后面
                if (claimCaseAttach.getAttachIndex() == null) {
                    claimCaseAttach.setAttachIndex(99999);
                }
            });
            Map<String, List<ClaimCaseAttach>> claimAttachMap = claimAttachList.stream().collect(groupingBy(
                    ClaimCaseAttach::getContentType,
                    collectingAndThen(
                            toList(),
                            list -> list.stream().sorted(Comparator.comparing(ClaimCaseAttach::getAttachIndex)).collect(toList())
                    )
            ));
            model.addAttribute("claimAttachMap", claimAttachMap);
        }

        Map<String, Object> imgInfoMap = CaseImgInfoEnum.getImgInfoMap();
        model.addAttribute("imgInfoMaps", imgInfoMap);
        model.addAttribute("claimCaseObject", claimCaseObject);

        model.addAttribute("isShow", true);

        //预估金额
        List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.findByParam(paramMap);
        if(CollectionUtils.isNotEmpty(claimCaseObjectList)){
            BigDecimal objectAppraisalAmount = BigDecimal.ZERO;
            for (ClaimCaseObject Object : claimCaseObjectList) {
                objectAppraisalAmount = objectAppraisalAmount.add(Object.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : Object.getEstimatedApprovedMoney());
            }
            model.addAttribute("objectAppraisalAmount",objectAppraisalAmount);
        }

        List<ClaimCaseObjectAssessment> objectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(objectAssessmentList)){
            objectAssessmentList.stream().forEach(claimCaseObjectAssessment -> {
                if(claimCaseObjectAssessment.getAssessmentIndex() == null){
                    //历史数据排在最后面
                    claimCaseObjectAssessment.setAssessmentIndex(99999);
                }
            });
            //排序
            objectAssessmentList.sort(Comparator.comparingInt(ClaimCaseObjectAssessment::getType)
                    .thenComparingInt(ClaimCaseObjectAssessment::getCategory)
                    .thenComparingInt(ClaimCaseObjectAssessment::getAssessmentIndex));

            model.addAttribute("objectAssessmentList", objectAssessmentList);
        }

        List<ClaimCaseObjectPayment> objectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
        model.addAttribute("objectPaymentList", JsonTool.genByFastJson(objectPaymentList));

        Map<String, BankInfo> bankInfoMap = new HashMap<>();

        for (ClaimCaseObjectPayment objectPayment : objectPaymentList) {
            if (StringUtils.isNotBlank(objectPayment.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(objectPayment.getBankInfoId());
                if (bankInfo != null) {
                    bankInfoMap.put(objectPayment.getBankInfoId(), bankInfo);
                }
            }
        }
        model.addAttribute("bankInfoMap", JsonTool.genByFastJson(bankInfoMap));

        if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
            if (bankInfo != null) {
                model.addAttribute("bankInfo", bankInfo);
            }
        }

        paramMap.clear();
        paramMap.put("baseUserId", claimCase.getBaseUserId());
        List<ClaimCase> claimCaseList = claimCaseService.findByTreatParam(paramMap);
        model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(claimCaseList) ? claimCaseList.size() : 0);
        Map<String, String> isValidityMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "VALIDITY_CERTIFICATE_INSCODE");
        if (isValidityMap.containsKey(claimCase.getInsCode())) {
            model.addAttribute("isValidity", true);
        }
        Map<String, Object> enumMapByParentCode = new HashMap<>();
        if (claimCaseObject.getCategory() == 1) {
            enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("1");
            model.addAttribute("enumMapByParentCode", enumMapByParentCode);
            return "claimCaseObject/objectGatherDetail";
        }

        enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("2");
        model.addAttribute("enumMapByParentCode", enumMapByParentCode);
        return "claimCaseObject/objectGatherGoodsDetail";
    }


    /**
     * 保司查看 估损清单
     *
     * @param id
     * @param model
     * @return
     */
    @RequestMapping(value = {"showObject4BS", ""})
    public String showObject4BS(String id, Model model) {

        if (StringUtils.isBlank(id)) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);
        if (StringUtils.isNotBlank(claimCase.getPolicyPersonId())) {
            PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());
            model.addAttribute("policyPerson", policyPerson);
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseId", claimCase.getId());
        String skipAttach = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "not_search_file_content_type");
        if (StringUtils.isNotBlank(skipAttach)) {
            List<String> skipAttachList = Arrays.asList(skipAttach.split(","));
            paramMap.put("skipAttach", skipAttachList);
        }
        List<ClaimCaseAttach> claimAttachList = claimCaseAttachService.findAttachByParam(paramMap);
        if (CollectionUtils.isNotEmpty(claimAttachList)) {
            claimAttachList.stream().forEach(claimCaseAttach -> {
                claimCaseAttach.setFileObjectId(AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, claimCaseAttach.getFileObjectId(), 3600 * 1000 * 3));
                // 历史数据排在最后面
                if (claimCaseAttach.getAttachIndex() == null) {
                    claimCaseAttach.setAttachIndex(99999);
                }
            });
            Map<String, List<ClaimCaseAttach>> claimAttachMap = claimAttachList.stream().collect(groupingBy(
                    ClaimCaseAttach::getContentType,
                    collectingAndThen(
                            toList(),
                            list -> list.stream().sorted(Comparator.comparing(ClaimCaseAttach::getAttachIndex)).collect(toList())
                    )
            ));
            model.addAttribute("claimAttachMap", claimAttachMap);
        }
        Map<String, Object> imgInfoMap = CaseImgInfoEnum.getImgInfoMap();
        model.addAttribute("imgInfoMaps", imgInfoMap);
        model.addAttribute("claimAttachList", claimAttachList);

        model.addAttribute("claimCaseObject", claimCaseObject);

        model.addAttribute("isShow", true);

        //预估金额
        List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.findByParam(paramMap);
        if(CollectionUtils.isNotEmpty(claimCaseObjectList)){
            BigDecimal objectAppraisalAmount = BigDecimal.ZERO;
            for (ClaimCaseObject Object : claimCaseObjectList) {
                objectAppraisalAmount = objectAppraisalAmount.add(Object.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : Object.getEstimatedApprovedMoney());
            }
            model.addAttribute("objectAppraisalAmount",objectAppraisalAmount);
        }

        List<ClaimCaseObjectAssessment> objectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(objectAssessmentList)){

            objectAssessmentList.stream().forEach(claimCaseObjectAssessment -> {
                if(claimCaseObjectAssessment.getAssessmentIndex() == null){
                    //历史数据排在最后面
                    claimCaseObjectAssessment.setAssessmentIndex(99999);
                }
            });
            //排序
            objectAssessmentList.sort(Comparator.comparingInt(ClaimCaseObjectAssessment::getType)
                    .thenComparingInt(ClaimCaseObjectAssessment::getCategory)
                    .thenComparingInt(ClaimCaseObjectAssessment::getAssessmentIndex));

            model.addAttribute("objectAssessmentList", objectAssessmentList);
        }

        List<ClaimCaseObjectPayment> objectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
        model.addAttribute("objectPaymentList", JsonTool.genByFastJson(objectPaymentList));


        Map<String, BankInfo> bankInfoMap = new HashMap<>();

        for (ClaimCaseObjectPayment objectPayment : objectPaymentList) {
            if (StringUtils.isNotBlank(objectPayment.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(objectPayment.getBankInfoId());
                if (bankInfo != null) {
                    bankInfoMap.put(objectPayment.getBankInfoId(), bankInfo);
                }
            }
        }
        model.addAttribute("bankInfoMap", JsonTool.genByFastJson(bankInfoMap));

        if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
            if (bankInfo != null) {
                model.addAttribute("bankInfo", bankInfo);
            }
        }

        paramMap.clear();
        paramMap.put("baseUserId", claimCase.getBaseUserId());
        List<ClaimCase> claimCaseList = claimCaseService.findByTreatParam(paramMap);
        model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(claimCaseList) ? claimCaseList.size() : 0);
        Map<String, String> isValidityMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "VALIDITY_CERTIFICATE_INSCODE");
        if (isValidityMap.containsKey(claimCase.getInsCode())) {
            model.addAttribute("isValidity", true);
        }
        Map<String, Object> enumMapByParentCode = new HashMap<>();
        if (claimCaseObject.getCategory() == 1) {
            enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("1");
            model.addAttribute("enumMapByParentCode", enumMapByParentCode);
            return "claimCase4BS/objectGatherDetail4BS";
        }

        enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("2");
        model.addAttribute("enumMapByParentCode", enumMapByParentCode);
        return "claimCase4BS/objectGatherGoodsDetail4BS";
    }

    /**
     * 海峡案件查询->查看详情->查看估损表
     *
     * @param id
     * @param model
     * @return
     */
    @RequestMapping(value = {"showObject2HX", ""})
    public String showObject2HX(String id, Model model) {

        if (StringUtils.isBlank(id)) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);
        if (StringUtils.isNotBlank(claimCase.getPolicyPersonId())) {
            PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());
            model.addAttribute("policyPerson", policyPerson);
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseId", claimCase.getId());
        String skipAttach = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "not_search_file_content_type");
        if (StringUtils.isNotBlank(skipAttach)) {
            List<String> skipAttachList = Arrays.asList(skipAttach.split(","));
            paramMap.put("skipAttach", skipAttachList);
        }
        List<ClaimCaseAttach> claimAttachList = claimCaseAttachService.findAttachByParam(paramMap);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(claimAttachList)) {
            claimAttachList.stream().forEach(claimCaseAttach -> {
                claimCaseAttach.setFileObjectId(AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, claimCaseAttach.getFileObjectId(), 3600 * 1000 * 3));
            });
        }
        model.addAttribute("claimAttachList", claimAttachList);

        model.addAttribute("claimCaseObject", claimCaseObject);

        model.addAttribute("isShow", true);

        //预估金额
        List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.findByParam(paramMap);
        if(CollectionUtils.isNotEmpty(claimCaseObjectList)){
            BigDecimal objectAppraisalAmount = BigDecimal.ZERO;
            for (ClaimCaseObject Object : claimCaseObjectList) {
                objectAppraisalAmount = objectAppraisalAmount.add(Object.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : Object.getEstimatedApprovedMoney());
            }
            model.addAttribute("objectAppraisalAmount",objectAppraisalAmount);
        }

        List<ClaimCaseObjectAssessment> objectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
        List<ClaimCaseObjectPayment> objectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
        model.addAttribute("objectAssessmentList", objectAssessmentList);
        model.addAttribute("objectPaymentList", JsonTool.genByFastJson(objectPaymentList));


        Map<String, BankInfo> bankInfoMap = new HashMap<>();

        for (ClaimCaseObjectPayment objectPayment : objectPaymentList) {
            if (StringUtils.isNotBlank(objectPayment.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(objectPayment.getBankInfoId());
                if (bankInfo != null) {
                    bankInfoMap.put(objectPayment.getBankInfoId(), bankInfo);
                }
            }
        }
        model.addAttribute("bankInfoMap", JsonTool.genByFastJson(bankInfoMap));

        if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
            if (bankInfo != null) {
                model.addAttribute("bankInfo", bankInfo);
            }
        }

        paramMap.clear();
        paramMap.put("baseUserId", claimCase.getBaseUserId());
        List<ClaimCase> claimCaseList = claimCaseService.findByTreatParam(paramMap);
        model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(claimCaseList) ? claimCaseList.size() : 0);
        Map<String, String> isValidityMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "VALIDITY_CERTIFICATE_INSCODE");
        if (isValidityMap.containsKey(claimCase.getInsCode())) {
            model.addAttribute("isValidity", true);
        }
        Map<String, Object> enumMapByParentCode = new HashMap<>();
        Map<String,String> rejectReason = claimCaseObjectLogService.findRejectLogByObjId(claimCaseObject.getId());
        model.addAttribute("rejectReason",rejectReason);
        if (claimCaseObject.getCategory() == 1) {
            enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("1");
            model.addAttribute("enumMapByParentCode", enumMapByParentCode);
            return "claimCaseObject/objectGatherDetail2HX";
        }

        enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("2");
        model.addAttribute("enumMapByParentCode", enumMapByParentCode);
        return "claimCaseObject/objectGatherGoodsDetail2HX";
    }

    @RequestMapping(value = {"auditPass", ""})
    @ResponseBody
    public String auditPass(@RequestBody String json) {

        try {

            ClaimCaseObjectReq claimCaseObjectReq = JSONObject.parseObject(json, ClaimCaseObjectReq.class);
            ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObjectReq.getClaimCaseId());

            List<ClaimCaseObjectAssessment> claimCaseObjectAssessmentList = claimCaseObjectReq.getClaimCaseObjectAssessmentList();

            ClaimCaseObject selectObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectReq.getId());
            if (selectObject.getIsCaseClosed() == 1) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "该估损单案件已关闭");
                }});
            }

            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(selectObject.getStatus())) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "当前任务状态已流转，请更换任务！");
                }});
            }

            List<ClaimCaseObject> updateClaimCaseObjectList = new ArrayList<>();
            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();

            String userName = ShiroSessionUtil.getLoginSession().getRealName();
            String userId = ShiroSessionUtil.getLoginSession().getId();


            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObjectReq.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObjectReq.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.保司操作估损清单.getCode());
            claimCaseLog.setStatus(claimCaseObjectReq.getStatus());
            String logDescription = SpringContextHolder.getBean(ClaimCaseObjectV2Controller.class).getResultDescription(claimCaseObjectReq);
            if ("-1".equals(logDescription)) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "系统异常,描述转换失败:");
                }});
            }
            claimCaseLog.setDescription(logDescription);
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObjectReq));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());

            if (ClaimCaseObjectStatusEnum.BAX23.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX22.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
                // 清空内部审核人
                claimCaseObjectReq.setCheckAuditer(null);
                claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX25.getCode());

                // 核损合计大于某金额 且案件为大地案件 到 估损保司审核
                String notExistAuditBS = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA", "NOT_EXIST_AUDIT_BS");
                List<String> bsList = Arrays.asList(notExistAuditBS.split(","));    // 无需保司审核的保司列表

                String insCode = StringUtils.isBlank(claimCase.getInsCode()) ? "DD" : claimCase.getInsCode();
                logger.info("====> 进入判断前"+""+claimCaseObjectReq.getResidualNuclearLossValue()+"claimCaseObjectReq.getLossAssessmentSum()"+claimCaseObjectReq.getNuclearLossSum()+"zhuangtai :"+claimCaseObjectReq.getStatus());
                // 判断案件保司 是否在无需保司审核列表里
                if (!bsList.contains(insCode)) {
                    claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX31.getCode());
                    // 估损复核通过
//                    String money = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA", insCode + "_APPRAISAL_MONEY");
//                    if (claimCaseObjectReq.getNuclearLossSum().compareTo(new BigDecimal(money)) == 1) {
//                        claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX25.getCode());
//                        // 如配置了Redis，则进入特定的账号中
//                        String insAuditer = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA", claimCase.getInsCode() + "_INS_AUDITER");
//                        if (StringUtils.isNotBlank(insAuditer)) {
//                            claimCaseObjectReq.setInsAuditer(insAuditer);
//                        }
//                    }

                    //第一级金额判断：判断核损合计金额是否大于设定值，大于则进入二级判断
                    String money = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA", insCode + "_APPRAISAL_MONEY");
                    //第二级金额判断：区分赔付物车物损，并进行判断

                    Integer type = claimCaseObjectReq.getType();
                    Integer category = claimCaseObjectReq.getCategory();

                    Map<String,String> redisMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA_AUTO_TC_" + insCode);
                    String money2 = "0"; //默认为0
                    if (!redisMap.isEmpty()) {
                        if (redisMap.containsKey("category-" + category)) {
                            //单区分人伤、车损、物损
                            money2 = redisMap.get("category-" + category);
                        } else if (redisMap.containsKey("type-" + type)) {
                            //单区分骑手、三者
                            money2 = redisMap.get("type-" + type);
                        } else if (redisMap.containsKey("type-category-" + type + category)) {
                            //区分骑手人伤、骑手车损...
                            money2 = redisMap.get("type-category-" + type + category);
                        }
                    }

                    //配置一级金额判断
                    //是否进入保司审核
                    if (StringUtils.isNotBlank(money) && claimCaseObjectReq.getNuclearLossSum().compareTo(new BigDecimal(money)) > 0) {
                        if (claimCaseObjectReq.getNuclearLossSum().compareTo(new BigDecimal(money2)) > 0) {
                            //二级判断
                            claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX25.getCode());

                            // 如配置了Redis，则进入特定的账号中
                            String insAuditer = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA", claimCase.getInsCode() + "_INS_AUDITER");
                            if (StringUtils.isNotBlank(insAuditer)) {
                                claimCaseObjectReq.setInsAuditer(insAuditer);
                            }
                        }
                    }
                    logger.info("====> 进入判断money2"+money2+""+claimCaseObjectReq.getResidualNuclearLossValue()+"claimCaseObjectReq.getLossAssessmentSum()"+claimCaseObjectReq.getLossAssessmentSum());
                    if(insCode.equals("HX") && category==1 && claimCaseObjectReq.getLossAssessmentSum().compareTo(new BigDecimal(money2)) > 0){
                        claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX25.getCode());
                    }

                    if(insCode.equals("HX") && category!=1 && claimCaseObjectReq.getNuclearLossSum().compareTo(new BigDecimal(money2)) > 0){
                        //海峡车物案件，估损金额>1500,进入复核
                        claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX25.getCode());
                        logger.info("====>money2"+money2+""+claimCaseObjectReq.getResidualNuclearLossValue()+"claimCaseObjectReq.getLossAssessmentSum()"+claimCaseObjectReq.getLossAssessmentSum());
                    }

                }
            }

            if (ClaimCaseObjectStatusEnum.BAX26.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
                // 估损保司审核通过 状态变为理算待提交
                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX31.getCode());
            }

            if (ClaimCaseObjectStatusEnum.BAX33.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX32.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
//                claimCaseObjectReq.setCheckAuditer(userId);
                claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
//                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX99.getCode());
                // 理算复核通过
                String money = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA", "VERIFY_MONEY");
                if (StringUtils.isBlank(money)) {
                    money = "99999999";
                }
                // 理算金额大于某金额 到 理算保司审核
                if (claimCaseObjectReq.getVerifyAmout().compareTo(new BigDecimal(money)) == 1) {
                    claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX35.getCode());
                    // 清空内部审核人
                    claimCaseObjectReq.setCheckAuditer(null);
                }else {
                    //理算初审通过后，如果不进入到理算复审，则进入签字进行中
                    claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX38.getCode());
                }


                Integer category = claimCaseObjectReq.getCategory();
                if(ApplyTypeNewEnum.三者物损.getCategory().equals(category) || ApplyTypeNewEnum.三者车损.getCategory().equals(category)){
                    //理算初审通过生成车物定损单影像信息
                    amqpTemplate.convertAndSend(QueueName.KD_GEN_CASE_OBJECT_IMAGE_INFO,selectObject.getId());
                }
            }

            if (ClaimCaseObjectStatusEnum.BAX36.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX35.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
                Integer category = claimCaseObjectReq.getCategory();
                if((ApplyTypeNewEnum.三者物损.getCategory().equals(category) || ApplyTypeNewEnum.三者车损.getCategory().equals(category))
                    && "FB".equals(claimCase.getInsCode())){
                    //富邦案件理算复核通过生成车物定损单影像信息
                    amqpTemplate.convertAndSend(QueueName.KD_GEN_CASE_OBJECT_IMAGE_INFO,selectObject.getId());
                }
                //理算复审通过后，进入签字进行中
                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX38.getCode());
//                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX99.getCode());
            }
            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(claimCaseObjectReq.getStatus())) {
                claimCaseObjectReq.setEstimatedApprovedMoney(claimCaseObjectReq.getVerifyAmout());
                claimCaseObjectReq.setCheckEndTime(new Date());
                logDescription += "<span style='color:red;font-size:18px'>赔付对象理算完成</span>";
                claimCaseLog.setDescription(logDescription);
            }

            if (ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObjectReq.getStatus())) {

                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_ATTACH_OCR, claimCaseObjectReq.getClaimCaseId());

                Set<String> labelSet = new HashSet<>();
                if (CollectionUtils.isNotEmpty(claimCaseObjectAssessmentList)) {
                    for (ClaimCaseObjectAssessment claimCaseObjectAssessment : claimCaseObjectAssessmentList) {
                        labelSet.add(EstimateInventoryEnum.codeToLabel(claimCaseObjectAssessment.getCode()));
                    }
                }
                claimCaseObjectReq.setLabel(String.join(",", labelSet));


            }

            // 入库sub
            // 获取object页面填写数据
            ClaimCaseObject claimCaseObject = new ClaimCaseObject();
            BeanUtils.copyProperties(claimCaseObjectReq, claimCaseObject);
            // 更新估损信息
            updateClaimCaseObjectList.add(claimCaseObject);

            // 日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseId(claimCase.getId());
            claimCaseObjectLog.setClaimCaseNo(claimCase.getClaimCaseNo());
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(logDescription);
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);

            insertClaimCaseLogList.add(claimCaseLog);

            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectCheckAuditerCanNullList(updateClaimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);
            if (CollectionUtils.isNotEmpty(claimCaseObjectAssessmentList)) {
                assessmentDataSup.setUpdateClaimCaseObjectAssessmentList(claimCaseObjectAssessmentList);
            }

            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            //更新claimCaseObject的modifyTime
            ClaimCaseObject claimCaseObjectUpdate = new ClaimCaseObject();
            claimCaseObjectUpdate.setModifyTime(new Date());
            claimCaseObjectUpdate.setId(claimCaseObject.getId());
            claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObjectUpdate);

            // 赔付对象进入估损审核， 进行自动推送保司
            if (ClaimCaseObjectStatusEnum.BAX22.getCode().equals(claimCaseObject.getStatus()) || ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus())) {
                Map<String, String> pushInsurance = new HashMap<>();
                pushInsurance.put("claimCaseNo", claimCaseObject.getClaimCaseNo());
                pushInsurance.put("importRemark", "估损审核");
                amqpTemplate.convertAndSend(QueueName.KD_PUSH_INSURANCE_CENTER_TTL_EXCHANGE, QueueName.KD_PUSH_INSURANCE_CENTER_TTL, JsonTool.genByFastJson(pushInsurance), message -> {
                    message.getMessageProperties().setExpiration(String.valueOf(10L * 1000L));
                    return message;
                });
            }

            // 推送mq，校验所有赔付对象是否完成
            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(claimCaseObjectReq.getStatus())) {
                Map<String, String> objectVerifyMap = new HashMap<>();
                objectVerifyMap.put("claimCaseId", claimCaseObjectReq.getClaimCaseId());
                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_OBJECT_VERIFY, JsonTool.genByFastJson(objectVerifyMap));

                // 监听修改估损金额是否需推饿了么
                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_PUSH_ELM, claimCase.getId());
            }

        } catch (Exception e) {
            logger.error("系统异常，审核通过失败，请联系管理员！");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }


    @RequestMapping(value = {"auditPassCar", ""})
    @ResponseBody
    public String auditPassCar(@RequestBody String json) {

        try {

            ClaimCaseObjectReq claimCaseObjectReq = JSONObject.parseObject(json, ClaimCaseObjectReq.class);
            ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObjectReq.getClaimCaseId());

//            List<ClaimCaseObjectAssessment> claimCaseObjectAssessmentList = claimCaseObjectReq.getClaimCaseObjectAssessmentList();

            ClaimCaseObject selectObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectReq.getId());
            if (selectObject.getIsCaseClosed() == 1) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "该估损单案件已关闭");
                }});
            }

            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(selectObject.getStatus())) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "当前任务状态已流转，请更换任务！");
                }});
            }

            //lily.Yin 2025/7/25 18:06: 校验核损数据是否存在,避免误操作
            if (StringUtils.isNotBlank(claimCaseObjectReq.getId())) {
                List<InsuranceClaimAssessmentVo> costList = insuranceNuclearAuditTaskService.findCostDataByLossNo(claimCaseObjectReq.getId());
                String assessmentType = StringUtils.isBlank(claimCaseObjectReq.getAssessmentType()) ? "复审" : claimCaseObjectReq.getAssessmentType();
                if (CollectionUtils.isEmpty(costList)) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "缺失定损数据！");
                    }});
                }
                Map<String, InsuranceClaimAssessmentVo> costMap = costList.stream().collect(toMap(
                        vo -> vo.getAssessmentType(),
                        vo -> vo
                ));
                if(!costMap.containsKey(assessmentType) || costMap.get(assessmentType).getSubmitted() == null || costMap.get(assessmentType).getSubmitted() == 0){
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "未查询到核损信息，请核实！");
                    }});
                }
                boolean flag = costMap.values().stream().map(o->{
                    return o.getTotalExcludingRescue();
                }).distinct().limit(2).count() <= 1;
                if (!costMap.containsKey(assessmentType) || costMap.get(assessmentType).getTotalExcludingRescue().compareTo(BigDecimal.ZERO) == 0) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "请先去核损！");
                    }});
                } else if (!costMap.containsKey("定损")) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "缺失定损数据！");
                    }});
                } else if (!flag) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "核损金额跟定损金额不一致，请核实！");
                    }});
                }
            }

            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();

            String userName = ShiroSessionUtil.getLoginSession().getRealName();
            String userId = ShiroSessionUtil.getLoginSession().getId();


            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObjectReq.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObjectReq.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.保司操作估损清单.getCode());
            claimCaseLog.setStatus(claimCaseObjectReq.getStatus());
            String logDescription = "";
            /*if ("-1".equals(logDescription)) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "系统异常,描述转换失败:");
                }});
            }*/
            claimCaseLog.setDescription(logDescription);
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObjectReq));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());


            if (ClaimCaseObjectStatusEnum.BAX26.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
                // 估损保司审核通过 状态变为理算待提交
                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX31.getCode());
            }

            if (ClaimCaseObjectStatusEnum.BAX33.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX32.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
//                claimCaseObjectReq.setCheckAuditer(userId);
                claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
//                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX99.getCode());
                // 理算复核通过
                String money = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA", "VERIFY_MONEY");
                if (StringUtils.isBlank(money)) {
                    money = "99999999";
                }
                // 理算金额大于某金额 到 理算保司审核
                if (claimCaseObjectReq.getVerifyAmout().compareTo(new BigDecimal(money)) == 1) {
                    claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX35.getCode());
                    // 清空内部审核人
                    claimCaseObjectReq.setCheckAuditer(null);
                }else {
                    //理算初审通过后，如果不进入到理算复审，则进入签字进行中
                    claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX38.getCode());
                }


                Integer category = claimCaseObjectReq.getCategory();
                if(ApplyTypeNewEnum.三者物损.getCategory().equals(category) || ApplyTypeNewEnum.三者车损.getCategory().equals(category)){
                    //理算初审通过生成车物定损单影像信息
                    amqpTemplate.convertAndSend(QueueName.KD_GEN_CASE_OBJECT_IMAGE_INFO,selectObject.getId());
                }
            }

            if (ClaimCaseObjectStatusEnum.BAX36.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX35.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
                Integer category = claimCaseObjectReq.getCategory();
                if((ApplyTypeNewEnum.三者物损.getCategory().equals(category) || ApplyTypeNewEnum.三者车损.getCategory().equals(category))
                        && "FB".equals(claimCase.getInsCode())){
                    //富邦案件理算复核通过生成车物定损单影像信息
                    amqpTemplate.convertAndSend(QueueName.KD_GEN_CASE_OBJECT_IMAGE_INFO,selectObject.getId());
                }
                //理算复审通过后，进入签字进行中
                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX38.getCode());
//                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX99.getCode());
            }
            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(claimCaseObjectReq.getStatus())) {
                claimCaseObjectReq.setEstimatedApprovedMoney(claimCaseObjectReq.getVerifyAmout());
                claimCaseObjectReq.setCheckEndTime(new Date());
                logDescription += "<span style='color:red;font-size:18px'>赔付对象理算完成</span>";
                claimCaseLog.setDescription(logDescription);
            }


            List<ClaimCaseObject> updateClaimCaseObjectList = new ArrayList<>();
            // 入库sub
            // 获取object页面填写数据
            ClaimCaseObject claimCaseObject = new ClaimCaseObject();
            BeanUtils.copyProperties(claimCaseObjectReq, claimCaseObject);
            updateClaimCaseObjectList.add(claimCaseObject);


            // 日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseId(claimCase.getId());
            claimCaseObjectLog.setClaimCaseNo(claimCase.getClaimCaseNo());
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(logDescription);
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);

            //lily.Yin 2025/7/24 0:18: 新增备注意见
            ClaimCaseObjectLog4CarReq claimCaseObjectLog4CarReq = new ClaimCaseObjectLog4CarReq();
            claimCaseObjectLog4CarReq.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog4CarReq.setOperatingMode(ClaimCaseObjectLogStatus4CarEnum.保司核损通过.getCode());
            if(selectObject.getLossAssessmentSum() != null){
                claimCaseObjectLog4CarReq.setMoney(String.valueOf(selectObject.getLossAssessmentSum()));
            }
            claimCaseObjectLog4CarReq.setDealOpinion("估损复核通过");
            if (claimCaseObjectLog4CarReq.checkData()) {
                claimCaseObjectLogService.insertCarLog(claimCaseObjectLog4CarReq);
            }

            insertClaimCaseLogList.add(claimCaseLog);

            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectCheckAuditerCanNullList(updateClaimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);


            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            //更新claimCaseObject的modifyTime
            ClaimCaseObject claimCaseObjectUpdate = new ClaimCaseObject();
            claimCaseObjectUpdate.setModifyTime(new Date());
            claimCaseObjectUpdate.setId(claimCaseObject.getId());
            claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObjectUpdate);

            // 赔付对象进入估损审核， 进行自动推送保司
            if (ClaimCaseObjectStatusEnum.BAX22.getCode().equals(claimCaseObject.getStatus()) || ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus())) {
                Map<String, String> pushInsurance = new HashMap<>();
                pushInsurance.put("claimCaseNo", claimCaseObject.getClaimCaseNo());
                pushInsurance.put("importRemark", "估损审核");
                amqpTemplate.convertAndSend(QueueName.KD_PUSH_INSURANCE_CENTER_TTL_EXCHANGE, QueueName.KD_PUSH_INSURANCE_CENTER_TTL, JsonTool.genByFastJson(pushInsurance), message -> {
                    message.getMessageProperties().setExpiration(String.valueOf(10L * 1000L));
                    return message;
                });
            }

            // 推送mq，校验所有赔付对象是否完成
            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(claimCaseObjectReq.getStatus())) {
                Map<String, String> objectVerifyMap = new HashMap<>();
                objectVerifyMap.put("claimCaseId", claimCaseObjectReq.getClaimCaseId());
                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_OBJECT_VERIFY, JsonTool.genByFastJson(objectVerifyMap));

                // 监听修改估损金额是否需推饿了么
                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_PUSH_ELM, claimCase.getId());
            }

            Map<String, String> pushJy = new HashMap<>();
            pushJy.put("claimCaseObjectId", claimCaseObject.getId());
            pushJy.put("userId", userId);
            pushJy.put("sceneType","-1");
            amqpTemplate.convertAndSend(QueueName.KD_PUSH_JY_CENTER_TTL_EXCHANGE, QueueName.KD_PUSH_JY_CENTER_TTL, JsonTool.genByFastJson(pushJy), message -> {
                message.getMessageProperties().setExpiration(String.valueOf( 1000L));
                return message;
            });

        } catch (Exception e) {
            logger.error("系统异常，审核通过失败，请联系管理员！");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    /**
     * 保司复核审核通过
     * @param json
     * @return
     */
    @RequestMapping(value = {"auditPassCarV2", ""})
    @ResponseBody
    public String auditPassCarV2(@RequestBody String json) {

        try {

            ClaimCaseObjectReq claimCaseObjectReq = JSONObject.parseObject(json, ClaimCaseObjectReq.class);
            ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObjectReq.getClaimCaseId());

//            List<ClaimCaseObjectAssessment> claimCaseObjectAssessmentList = claimCaseObjectReq.getClaimCaseObjectAssessmentList();

            ClaimCaseObject selectObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectReq.getId());
            if (selectObject.getIsCaseClosed() == 1) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "该估损单案件已关闭");
                }});
            }

            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(selectObject.getStatus())) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "当前任务状态已流转，请更换任务！");
                }});
            }

            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();

            String userName = ShiroSessionUtil.getLoginSession().getRealName();
            String userId = ShiroSessionUtil.getLoginSession().getId();


            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObjectReq.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObjectReq.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.保司操作估损清单.getCode());
            claimCaseLog.setStatus(claimCaseObjectReq.getStatus());
            String logDescription = getResultDescription(claimCaseObjectReq);

            /*if ("-1".equals(logDescription)) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "系统异常,描述转换失败:");
                }});
            }*/
            claimCaseLog.setDescription(logDescription);
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObjectReq));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());


            if (ClaimCaseObjectStatusEnum.BAX26.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
                // 估损保司审核通过 状态变为理算待提交
                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX31.getCode());
            }

            if (ClaimCaseObjectStatusEnum.BAX33.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX32.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
//                claimCaseObjectReq.setCheckAuditer(userId);
                claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
//                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX99.getCode());
                // 理算复核通过
                String money = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA", "VERIFY_MONEY");
                if (StringUtils.isBlank(money)) {
                    money = "99999999";
                }
                // 理算金额大于某金额 到 理算保司审核
                if (claimCaseObjectReq.getVerifyAmout().compareTo(new BigDecimal(money)) == 1) {
                    claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX35.getCode());
                    // 清空内部审核人
                    claimCaseObjectReq.setCheckAuditer(null);
                }else {
                    //理算初审通过后，如果不进入到理算复审，则进入签字进行中
                    claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX38.getCode());
                }


                Integer category = claimCaseObjectReq.getCategory();
                if(ApplyTypeNewEnum.三者物损.getCategory().equals(category) || ApplyTypeNewEnum.三者车损.getCategory().equals(category)){
                    //理算初审通过生成车物定损单影像信息
                    amqpTemplate.convertAndSend(QueueName.KD_GEN_CASE_OBJECT_IMAGE_INFO,selectObject.getId());
                }
            }

            if (ClaimCaseObjectStatusEnum.BAX36.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX35.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
                Integer category = claimCaseObjectReq.getCategory();
                if((ApplyTypeNewEnum.三者物损.getCategory().equals(category) || ApplyTypeNewEnum.三者车损.getCategory().equals(category))
                        && "FB".equals(claimCase.getInsCode())){
                    //富邦案件理算复核通过生成车物定损单影像信息
                    amqpTemplate.convertAndSend(QueueName.KD_GEN_CASE_OBJECT_IMAGE_INFO,selectObject.getId());
                }
                //理算复审通过后，进入签字进行中
                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX38.getCode());
//                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX99.getCode());
            }
            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(claimCaseObjectReq.getStatus())) {
                claimCaseObjectReq.setEstimatedApprovedMoney(claimCaseObjectReq.getVerifyAmout());
                claimCaseObjectReq.setCheckEndTime(new Date());
                logDescription += "<span style='color:red;font-size:18px'>赔付对象理算完成</span>";
                claimCaseLog.setDescription(logDescription);
            }


            List<ClaimCaseObject> updateClaimCaseObjectList = new ArrayList<>();
            // 入库sub
            // 获取object页面填写数据
            ClaimCaseObject claimCaseObject = new ClaimCaseObject();
            BeanUtils.copyProperties(claimCaseObjectReq, claimCaseObject);
            updateClaimCaseObjectList.add(claimCaseObject);


            // 日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseId(claimCase.getId());
            claimCaseObjectLog.setClaimCaseNo(claimCase.getClaimCaseNo());
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(logDescription);
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);

            //lily.Yin 2025/7/24 0:18: 新增备注意见
            ClaimCaseObjectLog4CarReq claimCaseObjectLog4CarReq = new ClaimCaseObjectLog4CarReq();
            claimCaseObjectLog4CarReq.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog4CarReq.setOperatingMode(ClaimCaseObjectLogStatus4CarEnum.保司理算通过.getCode());
            if(selectObject.getLossAssessmentSum() != null){
                claimCaseObjectLog4CarReq.setMoney(String.valueOf(selectObject.getLossAssessmentSum()));
            }
            claimCaseObjectLog4CarReq.setDealOpinion("保司理算复核通过");
            if (claimCaseObjectLog4CarReq.checkData()) {
                claimCaseObjectLogService.insertCarLog(claimCaseObjectLog4CarReq);
            }

            insertClaimCaseLogList.add(claimCaseLog);

            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectCheckAuditerCanNullList(updateClaimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);


            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            //更新claimCaseObject的modifyTime
            ClaimCaseObject claimCaseObjectUpdate = new ClaimCaseObject();
            claimCaseObjectUpdate.setModifyTime(new Date());
            claimCaseObjectUpdate.setId(claimCaseObject.getId());
            claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObjectUpdate);

            // 赔付对象进入估损审核， 进行自动推送保司
            if (ClaimCaseObjectStatusEnum.BAX22.getCode().equals(claimCaseObject.getStatus()) || ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus())) {
                Map<String, String> pushInsurance = new HashMap<>();
                pushInsurance.put("claimCaseNo", claimCaseObject.getClaimCaseNo());
                pushInsurance.put("importRemark", "估损审核");
                amqpTemplate.convertAndSend(QueueName.KD_PUSH_INSURANCE_CENTER_TTL_EXCHANGE, QueueName.KD_PUSH_INSURANCE_CENTER_TTL, JsonTool.genByFastJson(pushInsurance), message -> {
                    message.getMessageProperties().setExpiration(String.valueOf(10L * 1000L));
                    return message;
                });
            }

            // 推送mq，校验所有赔付对象是否完成
            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(claimCaseObjectReq.getStatus())) {
                Map<String, String> objectVerifyMap = new HashMap<>();
                objectVerifyMap.put("claimCaseId", claimCaseObjectReq.getClaimCaseId());
                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_OBJECT_VERIFY, JsonTool.genByFastJson(objectVerifyMap));

                // 监听修改估损金额是否需推饿了么
                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_PUSH_ELM, claimCase.getId());
            }

            Map<String, String> pushJy = new HashMap<>();
            pushJy.put("claimCaseObjectId", claimCaseObject.getId());
            pushJy.put("userId", userId);
            pushJy.put("sceneType","-1");
            //推送精友
            amqpTemplate.convertAndSend(QueueName.KD_PUSH_JY_CENTER_TTL_EXCHANGE, QueueName.KD_PUSH_JY_CENTER_TTL, JsonTool.genByFastJson(pushJy), message -> {
                message.getMessageProperties().setExpiration(String.valueOf( 1000L));
                return message;
            });

        } catch (Exception e) {
            logger.error("系统异常，审核通过失败，请联系管理员！");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }


    /**
     * 理算初审审核通过
     * @param json
     * @return
     */
    @RequestMapping(value = {"auditPassAccountingCar", ""})
    @ResponseBody
    public String auditPassAccountingCar(@RequestBody String json) {

        try {

            ClaimCaseObjectReq claimCaseObjectReq = JSONObject.parseObject(json, ClaimCaseObjectReq.class);
            ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObjectReq.getClaimCaseId());

//            List<ClaimCaseObjectAssessment> claimCaseObjectAssessmentList = claimCaseObjectReq.getClaimCaseObjectAssessmentList();

            ClaimCaseObject selectObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectReq.getId());
            if (selectObject.getIsCaseClosed() == 1) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "该估损单案件已关闭");
                }});
            }

            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(selectObject.getStatus())) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "当前任务状态已流转，请更换任务！");
                }});
            }

            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();

            String userName = ShiroSessionUtil.getLoginSession().getRealName();
            String userId = ShiroSessionUtil.getLoginSession().getId();


            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObjectReq.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObjectReq.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.保司操作估损清单.getCode());
            claimCaseLog.setStatus(claimCaseObjectReq.getStatus());
            String logDescription = getResultDescription(claimCaseObjectReq);
            /*if ("-1".equals(logDescription)) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "系统异常,描述转换失败:");
                }});
            }*/
            claimCaseLog.setDescription(logDescription);
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObjectReq));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());


            if (ClaimCaseObjectStatusEnum.BAX26.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
                // 估损保司审核通过 状态变为理算待提交
                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX31.getCode());
            }

            if (ClaimCaseObjectStatusEnum.BAX33.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX32.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
//                claimCaseObjectReq.setCheckAuditer(userId);
                claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
//                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX99.getCode());
                // 理算复核通过
                String money = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "OBJECT_MONEY_QUOTA", "VERIFY_MONEY");
                if (StringUtils.isBlank(money)) {
                    money = "99999999";
                }
                // 理算金额大于某金额 到 理算保司审核
                List<InsuranceClaimAssessmentVo> insuranceClaimAssessmentVoList = insuranceClaimAssessmentService.findByObjectId(selectObject.getId());
                Optional<InsuranceClaimAssessmentVo> settlementVo = insuranceClaimAssessmentVoList.stream()
                        .filter(vo -> CarAssessmentType.ADJUSTMENT.toString().equals(vo.getAssessmentType()))
                        .findFirst();
                InsuranceClaimAssessmentVo result = settlementVo.orElse(null);
                BigDecimal totalExcludingRescue = BigDecimal.ZERO;
                if (result != null) {
                    totalExcludingRescue = result.getTotalExcludingRescue();
                }
                if (totalExcludingRescue.compareTo(new BigDecimal(money)) == 1) {
                    claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX35.getCode());
                    // 清空内部审核人
                    claimCaseObjectReq.setCheckAuditer(null);
                }else {
                    //理算初审通过后，如果不进入到理算复审，则进入签字进行中
                    claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX38.getCode());
                }
                Integer category = claimCaseObjectReq.getCategory();
                if(ApplyTypeNewEnum.三者物损.getCategory().equals(category) || ApplyTypeNewEnum.三者车损.getCategory().equals(category)){
                    //理算初审通过生成车物定损单影像信息
                    amqpTemplate.convertAndSend(QueueName.KD_GEN_CASE_OBJECT_IMAGE_INFO,selectObject.getId());
                }
            }

            if (ClaimCaseObjectStatusEnum.BAX36.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX35.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
                Integer category = claimCaseObjectReq.getCategory();
                if((ApplyTypeNewEnum.三者物损.getCategory().equals(category) || ApplyTypeNewEnum.三者车损.getCategory().equals(category))
                        && "FB".equals(claimCase.getInsCode())){
                    //富邦案件理算复核通过生成车物定损单影像信息
                    amqpTemplate.convertAndSend(QueueName.KD_GEN_CASE_OBJECT_IMAGE_INFO,selectObject.getId());
                }
                //理算复审通过后，进入签字进行中
                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX38.getCode());
//                claimCaseObjectReq.setStatus(ClaimCaseObjectStatusEnum.BAX99.getCode());
            }
            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(claimCaseObjectReq.getStatus())) {
                claimCaseObjectReq.setEstimatedApprovedMoney(claimCaseObjectReq.getVerifyAmout());
                claimCaseObjectReq.setCheckEndTime(new Date());
                logDescription += "<span style='color:red;font-size:18px'>赔付对象理算完成</span>";
                claimCaseLog.setDescription(logDescription);
            }


            List<ClaimCaseObject> updateClaimCaseObjectList = new ArrayList<>();
            // 入库sub
            // 获取object页面填写数据
            ClaimCaseObject claimCaseObject = new ClaimCaseObject();
            BeanUtils.copyProperties(claimCaseObjectReq, claimCaseObject);
            updateClaimCaseObjectList.add(claimCaseObject);


            // 日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseId(claimCase.getId());
            claimCaseObjectLog.setClaimCaseNo(claimCase.getClaimCaseNo());
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(logDescription);
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);

            // 车损日志
            ClaimCaseObjectLog4CarReq claimCaseObjectLog4CarReq = new ClaimCaseObjectLog4CarReq();
            claimCaseObjectLog4CarReq.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog4CarReq.setOperatingMode(ClaimCaseObjectLogStatus4CarEnum.内部理算通过.getCode());
            if(claimCaseObject.getVerifyAmout() != null){
                claimCaseObjectLog4CarReq.setMoney(String.valueOf(claimCaseObject.getVerifyAmout()));
            }
            claimCaseObjectLog4CarReq.setDealOpinion("理算初审通过");
            claimCaseObjectLog4CarReq.setCreator(userId);
            if (claimCaseObjectLog4CarReq.checkData()) {
                claimCaseObjectLogService.insertCarLog(claimCaseObjectLog4CarReq);
            }

            insertClaimCaseLogList.add(claimCaseLog);

            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectCheckAuditerCanNullList(updateClaimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);


            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            //更新claimCaseObject的modifyTime
            ClaimCaseObject claimCaseObjectUpdate = new ClaimCaseObject();
            claimCaseObjectUpdate.setModifyTime(new Date());
            claimCaseObjectUpdate.setId(claimCaseObject.getId());
            claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObjectUpdate);

            // 赔付对象进入估损审核， 进行自动推送保司
            if (ClaimCaseObjectStatusEnum.BAX22.getCode().equals(claimCaseObject.getStatus()) || ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus())) {
                Map<String, String> pushInsurance = new HashMap<>();
                pushInsurance.put("claimCaseNo", claimCaseObject.getClaimCaseNo());
                pushInsurance.put("importRemark", "估损审核");
                amqpTemplate.convertAndSend(QueueName.KD_PUSH_INSURANCE_CENTER_TTL_EXCHANGE, QueueName.KD_PUSH_INSURANCE_CENTER_TTL, JsonTool.genByFastJson(pushInsurance), message -> {
                    message.getMessageProperties().setExpiration(String.valueOf(10L * 1000L));
                    return message;
                });
            }

            // 推送mq，校验所有赔付对象是否完成
            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(claimCaseObjectReq.getStatus())) {
                Map<String, String> objectVerifyMap = new HashMap<>();
                objectVerifyMap.put("claimCaseId", claimCaseObjectReq.getClaimCaseId());
                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_OBJECT_VERIFY, JsonTool.genByFastJson(objectVerifyMap));

                // 监听修改估损金额是否需推饿了么
                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_PUSH_ELM, claimCase.getId());
            }
            Map<String, String> pushJy = new HashMap<>();
            pushJy.put("claimCaseObjectId", claimCaseObject.getId());
            pushJy.put("userId", userId);
            pushJy.put("sceneType","-1");
            //推送精友
            amqpTemplate.convertAndSend(QueueName.KD_PUSH_JY_CENTER_TTL_EXCHANGE, QueueName.KD_PUSH_JY_CENTER_TTL, JsonTool.genByFastJson(pushJy), message -> {
                message.getMessageProperties().setExpiration(String.valueOf( 1000L));
                return message;
            });
        } catch (Exception e) {
            logger.error("系统异常，审核通过失败，请联系管理员！");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }


    @ResponseBody
    @RequestMapping(value = {"getTask", ""})
    public String getTask(String id) {
        if (StringUtils.isBlank(id)) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }

        String userId = ShiroSessionUtil.getLoginSession().getId();
        claimCaseObject.setCheckAuditer(userId);
        claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    @RequestMapping(value = {"completeTask", ""})
    @ResponseBody
    public String completeTask(String id) {
        if (StringUtils.isBlank(id)) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }

        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        if (claimCase == null) {
            return JsonBizTool.genJson(new HashMap<String, Object>() {{
                put("ret", "-1");
                put("msg", "案件不存在");
            }});
        }

        if (!(ClaimCaseObjectStatusEnum.BAX33.getCode().equals(claimCaseObject.getStatus()) || ClaimCaseObjectStatusEnum.BAX36.getCode().equals(claimCaseObject.getStatus())
                || ClaimCaseObjectStatusEnum.BAX40.getCode().equals(claimCaseObject.getStatus()))) {
            return JsonBizTool.genJson(ExRetEnum.FAIL_GET_APPLY);
        }

        try {
            List<ClaimCaseObject> updateClaimCaseObjectList = new ArrayList<>();
            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();

            String userName = ShiroSessionUtil.getLoginSession().getRealName();
            String userId = ShiroSessionUtil.getLoginSession().getId();

            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.人员操作记录.getCode());
            claimCaseLog.setStatus(ClaimCaseObjectStatusEnum.BAX99.getCode());
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObject));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());

            claimCaseObject.setStatus(ClaimCaseObjectStatusEnum.BAX99.getCode());
            claimCaseObject.setEstimatedApprovedMoney(claimCaseObject.getVerifyAmout());
            claimCaseObject.setCheckEndTime(new Date());
            String logDescription = "<span style='color:red;font-size:18px'>损失项目签章完成</span>";
            claimCaseLog.setDescription(logDescription);

            updateClaimCaseObjectList.add(claimCaseObject);

            // 日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(logDescription);
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);

            insertClaimCaseLogList.add(claimCaseLog);

            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectCheckAuditerCanNullList(updateClaimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);

            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            //lily.Yin 2025/6/6 23:46: 添加调估日志
            List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.selectByClaimCaseId(claimCase.getId());
            BigDecimal objectAppraisalAmount = BigDecimal.ZERO;
            if (claimCaseObjectList != null && CollectionUtils.isNotEmpty(claimCaseObjectList)) {
                for (ClaimCaseObject object : claimCaseObjectList) {
                    if (StringUtils.equals(id, object.getId())) {
                        objectAppraisalAmount = objectAppraisalAmount.add(claimCaseObject.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : claimCaseObject.getEstimatedApprovedMoney());
                    } else {
                        objectAppraisalAmount = objectAppraisalAmount.add(object.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : object.getEstimatedApprovedMoney());
                    }
                }
            }
            claimCase.setAppraisalAmount(objectAppraisalAmount.equals(BigDecimal.ZERO) && CollectionUtils.isEmpty(claimCaseObjectList) ? null : objectAppraisalAmount);
            ClaimCaseLossAdjustVo claimCaseLossAdjustVo = new ClaimCaseLossAdjustVo();
            claimCaseLossAdjustVo.setClaimCase(claimCase);
            claimCaseLossAdjustVo.setCause(null);
            claimCaseLossAdjustVo.setLossAdjust(claimCase.getAppraisalAmount() == null ? false : true);
            claimCaseLossAdjustVo.setCreateTime(new Date());
            amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_STATE_TRANSITION_PACK, JsonTool.genByFastJson(claimCaseLossAdjustVo));

            // 推送mq，校验所有赔付对象是否完成
            Map<String, String> objectVerifyMap = new HashMap<>();
            objectVerifyMap.put("claimCaseId", claimCaseObject.getClaimCaseId());
            amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_OBJECT_VERIFY, JsonTool.genByFastJson(objectVerifyMap));

            // 监听修改估损金额是否需推饿了么
            amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_PUSH_ELM, claimCase.getId());

        } catch (Exception e) {
            logger.error("系统异常，签章任务失败，请联系管理员！");
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    @RequestMapping(value = {"auditReject", ""})
    @ResponseBody
    public String auditReject(@RequestBody String json) {

        try {

            String userName = ShiroSessionUtil.getLoginSession().getRealName();
            String userId = ShiroSessionUtil.getLoginSession().getId();

            ClaimCaseObjectReq claimCaseObjectReq = JSONObject.parseObject(json, ClaimCaseObjectReq.class);

            ClaimCaseObject selectObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectReq.getId());
            if (selectObject.getIsCaseClosed() == 1) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "该估损单案件已关闭");
                }});
            }

            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(selectObject.getStatus())) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "当前任务状态已流转，请更换任务！");
                }});
            }
            if (ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX22.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX32.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX35.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }

            List<ClaimCaseObject> updateClaimCaseObjectList = new ArrayList<>();
            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();

            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObjectReq.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObjectReq.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.保司操作估损清单.getCode());
            if (ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObjectReq.getStatus()) || ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObjectReq.getStatus())) {
                claimCaseObjectReq.setCheckAuditer(null);  //  内部审核驳回时，清空内部审核人员
                claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
            }
            claimCaseLog.setStatus(claimCaseObjectReq.getStatus());
            String logDescription = SpringContextHolder.getBean(ClaimCaseObjectV2Controller.class).getResultDescription(claimCaseObjectReq);
            if ("-1".equals(logDescription)) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "系统异常,描述转换失败:");
                }});
            }
            claimCaseLog.setDescription(logDescription);
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObjectReq));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());

            // 入库sub
            // 获取object页面填写数据
            ClaimCaseObject claimCaseObject = new ClaimCaseObject();
            BeanUtils.copyProperties(claimCaseObjectReq, claimCaseObject);
            // 更新估损信息
            updateClaimCaseObjectList.add(claimCaseObject);

            //更新claimCaseObject的modifyTime
            ClaimCaseObject claimCaseObjectUpdate = new ClaimCaseObject();
            claimCaseObjectUpdate.setModifyTime(new Date());
            claimCaseObjectUpdate.setId(claimCaseObject.getId());
            claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObjectUpdate);

            // 日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseId(selectObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseNo(selectObject.getClaimCaseNo());
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(logDescription);
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);
            insertClaimCaseLogList.add(claimCaseLog);




            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectCheckAuditerCanNullList(updateClaimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);
            if (CollectionUtils.isNotEmpty(claimCaseObjectReq.getClaimCaseObjectAssessmentList())) {
                assessmentDataSup.setUpdateClaimCaseObjectAssessmentList(claimCaseObjectReq.getClaimCaseObjectAssessmentList());
            }

            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

        } catch (Exception e) {
            logger.error("系统异常，审核驳回失败，请联系管理员！");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }


    @RequestMapping(value = {"auditRejectCar", ""})
    @ResponseBody
    public String auditRejectCar(@RequestBody String json) {

        try {

            String userName = ShiroSessionUtil.getLoginSession().getRealName();
            String userId = ShiroSessionUtil.getLoginSession().getId();

            ClaimCaseObjectReq claimCaseObjectReq = JSONObject.parseObject(json, ClaimCaseObjectReq.class);

            ClaimCaseObject selectObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectReq.getId());
            if (selectObject.getIsCaseClosed() == 1) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "该估损单案件已关闭");
                }});
            }

            String assessmentType = CarAssessmentType.NUCLEAR_DAMAGE.getMsg();
            List<InsuranceClaimAssessmentVo> costList = insuranceNuclearAuditTaskService.findCostDataByLossNo(claimCaseObjectReq.getId());
            Map<String, InsuranceClaimAssessmentVo> costMap = costList.stream().collect(toMap(
                    vo -> vo.getAssessmentType(),
                    vo -> vo
            ));
            if(!costMap.containsKey(assessmentType) || costMap.get(assessmentType).getSubmitted() == null || costMap.get(assessmentType).getSubmitted() == 0){
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "未查询到核损信息，请核实！");
                }});
            }

            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(selectObject.getStatus())) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "当前任务状态已流转，请更换任务！");
                }});
            }
            if (ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX22.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX32.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX35.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }

            List<ClaimCaseObject> updateClaimCaseObjectList = new ArrayList<>();
            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();

            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObjectReq.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObjectReq.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.保司操作估损清单.getCode());
            if (ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObjectReq.getStatus()) || ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObjectReq.getStatus())) {
                claimCaseObjectReq.setCheckAuditer(null);  //  内部审核驳回时，清空内部审核人员
                claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
            }
            claimCaseLog.setStatus(claimCaseObjectReq.getStatus());
            String logDescription = "";
            claimCaseLog.setDescription(logDescription);
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObjectReq));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());

            // 入库sub
            // 获取object页面填写数据
            ClaimCaseObject claimCaseObject = new ClaimCaseObject();
            BeanUtils.copyProperties(claimCaseObjectReq, claimCaseObject);
            // 更新估损信息
            updateClaimCaseObjectList.add(claimCaseObject);

            //更新claimCaseObject的modifyTime
            ClaimCaseObject claimCaseObjectUpdate = new ClaimCaseObject();
            claimCaseObjectUpdate.setModifyTime(new Date());
            claimCaseObjectUpdate.setId(claimCaseObject.getId());
            claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObjectUpdate);

            // 日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseId(selectObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseNo(selectObject.getClaimCaseNo());
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(logDescription);
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);
            insertClaimCaseLogList.add(claimCaseLog);


            ClaimCaseObjectLog4CarReq claimCaseObjectLog4CarReq = new ClaimCaseObjectLog4CarReq();
            claimCaseObjectLog4CarReq.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog4CarReq.setOperatingMode(ClaimCaseObjectLogStatus4CarEnum.保司核损驳回.getCode());
            if(selectObject.getLossAssessmentSum() != null){
                claimCaseObjectLog4CarReq.setMoney(String.valueOf(selectObject.getLossAssessmentSum()));
            }
            claimCaseObjectLog4CarReq.setDealOpinion(claimCaseObjectReq.getReason());
            if (claimCaseObjectLog4CarReq.checkData()) {
                claimCaseObjectLogService.insertCarLog(claimCaseObjectLog4CarReq);
            }

            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectCheckAuditerCanNullList(updateClaimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);
            if (CollectionUtils.isNotEmpty(claimCaseObjectReq.getClaimCaseObjectAssessmentList())) {
                assessmentDataSup.setUpdateClaimCaseObjectAssessmentList(claimCaseObjectReq.getClaimCaseObjectAssessmentList());
            }

            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            Map<String, String> pushJy = new HashMap<>();
            pushJy.put("claimCaseObjectId", claimCaseObject.getId());
            pushJy.put("userId", userId);
            pushJy.put("sceneType","-1");
            //推送精友
            amqpTemplate.convertAndSend(QueueName.KD_PUSH_JY_CENTER_TTL_EXCHANGE, QueueName.KD_PUSH_JY_CENTER_TTL, JsonTool.genByFastJson(pushJy), message -> {
                message.getMessageProperties().setExpiration(String.valueOf( 1000L));
                return message;
            });
        } catch (Exception e) {
            logger.error("系统异常，审核驳回失败，请联系管理员！");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    /**
     * 保司复核驳回
     * @param json
     * @return
     */
    @RequestMapping(value = {"auditRejectCarV2", ""})
    @ResponseBody
    public String auditRejectCarV2(@RequestBody String json) {

        try {

            String userName = ShiroSessionUtil.getLoginSession().getRealName();
            String userId = ShiroSessionUtil.getLoginSession().getId();

            ClaimCaseObjectReq claimCaseObjectReq = JSONObject.parseObject(json, ClaimCaseObjectReq.class);

            ClaimCaseObject selectObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectReq.getId());
            if (selectObject.getIsCaseClosed() == 1) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "该估损单案件已关闭");
                }});
            }

            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(selectObject.getStatus())) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "当前任务状态已流转，请更换任务！");
                }});
            }
            if (ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX22.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX32.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX35.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }

            List<ClaimCaseObject> updateClaimCaseObjectList = new ArrayList<>();
            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();

            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObjectReq.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObjectReq.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.保司操作估损清单.getCode());
            if (ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObjectReq.getStatus()) || ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObjectReq.getStatus())) {
                claimCaseObjectReq.setCheckAuditer(null);  //  内部审核驳回时，清空内部审核人员
                claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
            }
            claimCaseLog.setStatus(claimCaseObjectReq.getStatus());
            String logDescription = getResultDescription(claimCaseObjectReq);
            claimCaseLog.setDescription(logDescription);
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObjectReq));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());

            // 入库sub
            // 获取object页面填写数据
            ClaimCaseObject claimCaseObject = new ClaimCaseObject();
            BeanUtils.copyProperties(claimCaseObjectReq, claimCaseObject);
            // 更新估损信息
            updateClaimCaseObjectList.add(claimCaseObject);

            //更新claimCaseObject的modifyTime
            ClaimCaseObject claimCaseObjectUpdate = new ClaimCaseObject();
            claimCaseObjectUpdate.setModifyTime(new Date());
            claimCaseObjectUpdate.setId(claimCaseObject.getId());
            claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObjectUpdate);

            // 日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseId(selectObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseNo(selectObject.getClaimCaseNo());
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(logDescription);
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);
            insertClaimCaseLogList.add(claimCaseLog);

            ClaimCaseObjectLog4CarReq claimCaseObjectLog4CarReq = new ClaimCaseObjectLog4CarReq();
            claimCaseObjectLog4CarReq.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog4CarReq.setOperatingMode(ClaimCaseObjectLogStatus4CarEnum.保司核损驳回.getCode());
            if(selectObject.getLossAssessmentSum() != null){
                claimCaseObjectLog4CarReq.setMoney(String.valueOf(selectObject.getLossAssessmentSum()));
            }
            claimCaseObjectLog4CarReq.setDealOpinion(claimCaseObjectReq.getReason());
            claimCaseObjectLog4CarReq.setCreator(userId);
            if (claimCaseObjectLog4CarReq.checkData()) {
                claimCaseObjectLogService.insertCarLog(claimCaseObjectLog4CarReq);
            }

            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectCheckAuditerCanNullList(updateClaimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);
            if (CollectionUtils.isNotEmpty(claimCaseObjectReq.getClaimCaseObjectAssessmentList())) {
                assessmentDataSup.setUpdateClaimCaseObjectAssessmentList(claimCaseObjectReq.getClaimCaseObjectAssessmentList());
            }

            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            Map<String, String> pushJy = new HashMap<>();
            pushJy.put("claimCaseObjectId", claimCaseObject.getId());
            pushJy.put("userId", userId);
            pushJy.put("sceneType","-1");
            //推送精友
            amqpTemplate.convertAndSend(QueueName.KD_PUSH_JY_CENTER_TTL_EXCHANGE, QueueName.KD_PUSH_JY_CENTER_TTL, JsonTool.genByFastJson(pushJy), message -> {
                message.getMessageProperties().setExpiration(String.valueOf(1000L));
                return message;
            });

        } catch (Exception e) {
            logger.error("系统异常，审核驳回失败，请联系管理员！");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    /**
     * 理算初审驳回
     * @param json
     * @return
     */
    @RequestMapping(value = {"auditRejectAccountingCar", ""})
    @ResponseBody
    public String auditRejectAccountingCar(@RequestBody String json) {

        try {

            String userName = ShiroSessionUtil.getLoginSession().getRealName();
            String userId = ShiroSessionUtil.getLoginSession().getId();

            ClaimCaseObjectReq claimCaseObjectReq = JSONObject.parseObject(json, ClaimCaseObjectReq.class);

            ClaimCaseObject selectObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectReq.getId());
            if (selectObject.getIsCaseClosed() == 1) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "该估损单案件已关闭");
                }});
            }

            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(selectObject.getStatus())) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "当前任务状态已流转，请更换任务！");
                }});
            }
            if (ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX22.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX32.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有内部审核人");
                    }});
                }
                if (!userId.equals(selectObject.getCheckAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }
            if (ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObjectReq.getStatus())) {
                if (!ClaimCaseObjectStatusEnum.BAX35.getCode().equals(selectObject.getStatus())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "当前任务状态已流转，请更换任务！");
                    }});
                }
                if (StringUtils.isBlank(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单没有保司审核人");
                    }});
                }
                if (!userId.equals(selectObject.getInsAuditer())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "该估损单任务不属于当前审核人");
                    }});
                }
            }

            List<ClaimCaseObject> updateClaimCaseObjectList = new ArrayList<>();
            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();

            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObjectReq.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObjectReq.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.保司操作估损清单.getCode());
            if (ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObjectReq.getStatus()) || ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObjectReq.getStatus())) {
                claimCaseObjectReq.setCheckAuditer(null);  //  内部审核驳回时，清空内部审核人员
                claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
            }
            claimCaseLog.setStatus(claimCaseObjectReq.getStatus());
            String logDescription = getResultDescription(claimCaseObjectReq);
            claimCaseLog.setDescription(logDescription);
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObjectReq));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());

            // 入库sub
            // 获取object页面填写数据
            ClaimCaseObject claimCaseObject = new ClaimCaseObject();
            BeanUtils.copyProperties(claimCaseObjectReq, claimCaseObject);
            // 更新估损信息
            updateClaimCaseObjectList.add(claimCaseObject);

            //更新claimCaseObject的modifyTime
            ClaimCaseObject claimCaseObjectUpdate = new ClaimCaseObject();
            claimCaseObjectUpdate.setModifyTime(new Date());
            claimCaseObjectUpdate.setId(claimCaseObject.getId());
            claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObjectUpdate);

            // 日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseId(selectObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseNo(selectObject.getClaimCaseNo());
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(logDescription);
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);
            insertClaimCaseLogList.add(claimCaseLog);


            ClaimCaseObjectLog4CarReq claimCaseObjectLog4CarReq = new ClaimCaseObjectLog4CarReq();
            claimCaseObjectLog4CarReq.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog4CarReq.setOperatingMode(ClaimCaseObjectLogStatus4CarEnum.内部理算驳回.getCode());
            if(selectObject.getVerifyAmout() != null){
                claimCaseObjectLog4CarReq.setMoney(String.valueOf(selectObject.getVerifyAmout()));
            }
            claimCaseObjectLog4CarReq.setDealOpinion(claimCaseObjectReq.getReason());
            claimCaseObjectLog4CarReq.setCreator(userId);
            if (claimCaseObjectLog4CarReq.checkData()) {
                claimCaseObjectLogService.insertCarLog(claimCaseObjectLog4CarReq);
            }

            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectCheckAuditerCanNullList(updateClaimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);
            if (CollectionUtils.isNotEmpty(claimCaseObjectReq.getClaimCaseObjectAssessmentList())) {
                assessmentDataSup.setUpdateClaimCaseObjectAssessmentList(claimCaseObjectReq.getClaimCaseObjectAssessmentList());
            }

            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            Map<String, String> pushJy = new HashMap<>();
            pushJy.put("claimCaseObjectId", claimCaseObject.getId());
            pushJy.put("userId", userId);
            pushJy.put("sceneType","-1");
            //推送精友
            amqpTemplate.convertAndSend(QueueName.KD_PUSH_JY_CENTER_TTL_EXCHANGE, QueueName.KD_PUSH_JY_CENTER_TTL, JsonTool.genByFastJson(pushJy), message -> {
                message.getMessageProperties().setExpiration(String.valueOf( 1000L));
                return message;
            });
        } catch (Exception e) {
            logger.error("系统异常，审核驳回失败，请联系管理员！");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }


//    @RequestMapping(value = {"claimCaseObjectStatistics", ""})
//    public String claimCaseObjectStatistics(String date, Model model) {
//
//        Date minDate = new Date();
//        if (date != null) {
//            minDate = DateUtils.parse(date + "-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
//        }
//
//        Calendar c = Calendar.getInstance();
//        c.setTime(minDate);
//        c.add(Calendar.MONTH, 1);
//
//        Date maxDate = c.getTime();
//
//        long startTime = System.nanoTime();
//        Map<String, List<ClaimCaseObject>> claimCaseObjectStatistics = claimCaseObjectService.findClaimCaseObjectStatistics();
//        long endTime = System.nanoTime();
//
//        if (!claimCaseObjectStatistics.isEmpty()) {
//            Map<String, Map<String, Integer>> statisticsMap = new HashMap<>();
//            Map<String, Integer> totalMap = new HashMap<>();
//            // 用户ID
//            List<String> managerIdList = new ArrayList<>();
//
//            Integer closeCountTotal = 0;                     // 关闭任务数合计
//            Integer monthReceiveCountTotal = 0;              // 月进案量合计
//            Integer todayFinishCountTotal = 0;               // 当日完成合计
//            Integer finishCountTotal = 0;                    // 完成任务数合计
//            Integer processCountTotal = 0;                   // 处理中任务数合计
//            Integer todayReceiveCountTotal = 0;              // 当日接收合计
//
//            long startTime1 = System.nanoTime();
//
//            for (String key : claimCaseObjectStatistics.keySet()) {
//
//                managerIdList.add(key);
//
//                Map<String, Integer> countMap = new HashMap<>();
//                Integer todayFinishCount = 0;               // 当日完成
//                Integer processCount = 0;                   // 处理中任务数
//                Integer closeCount = 0;                     // 月关闭案件数
//                Integer monthReceiveCount = 0;              // 月进案量
//                Integer finishCount = 0;                    // 月结案量
//                Integer todayReceiveCount = 0;              // 当日接收
//                //当日完成，处理中任务数
//                List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectStatistics.get(key);
//                if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
//                    for (ClaimCaseObject claimCaseObject : claimCaseObjectList) {
//
//                        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
//
//                        Date dates = claimCase.getCancelDate();
//                        Date auditerShareTime = claimCaseObject.getAuditerShareTime();
//                        String status = claimCaseObject.getStatus();
//                        Date checkEndTime = claimCaseObject.getCheckEndTime();
//                        Date endDate = claimCase.getEndDate();
//
//                        if (dates != null && dates.compareTo(minDate) >= 0 && dates.compareTo(maxDate) < 0) {
//                            if (claimCaseObject.getIsCaseClosed() == 1) {
//                                closeCount++;
//                                continue;
//                            }
//                        }
//
//                        if (auditerShareTime != null && auditerShareTime.compareTo(minDate) >= 0 && auditerShareTime.compareTo(maxDate) < 0) {
//                            monthReceiveCount++;
//                        }
//
//                        if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(status)) {
//                            if (endDate != null && endDate.compareTo(minDate) >= 0 && endDate.compareTo(maxDate) < 0) {
//                                finishCount++;
//                            }
//                        } else {
//                            processCount++;
//                        }
//
//                        if (DateUtils.format(new Date()).equals(DateUtils.format(checkEndTime))) {
//                            todayFinishCount++;
//                        }
//
//                        if (DateUtils.format(new Date()).equals(DateUtils.format(auditerShareTime))) {
//                            todayReceiveCount++;
//                        }
//
//                    }
//                }
//
//                todayFinishCountTotal += todayFinishCount;
//                processCountTotal += processCount;
//
//                closeCountTotal += closeCount;
//                monthReceiveCountTotal += monthReceiveCount;
//                finishCountTotal += finishCount;
//
//                todayReceiveCountTotal += todayReceiveCount;
//
//
//                countMap.put("todayFinishCount", todayFinishCount);
//                countMap.put("processCount", processCount);
//
//                countMap.put("closeCount", closeCount);
//                countMap.put("monthReceiveCount", monthReceiveCount);
//                countMap.put("finishCount", finishCount);
//
//                countMap.put("todayReceiveCount", todayReceiveCount);
//
//                statisticsMap.put(key, countMap);
//            }
//            long endTime1 = System.nanoTime();
//            System.out.println("循环耗时：" + (endTime1 - startTime1));
//            System.out.println("findClaimCaseObjectStatistics耗时：" + (endTime - startTime));
//            totalMap.put("closeCountTotal", closeCountTotal);
//            totalMap.put("monthReceiveCountTotal", monthReceiveCountTotal);
//            totalMap.put("todayFinishCountTotal", todayFinishCountTotal);
//            totalMap.put("finishCountTotal", finishCountTotal);
//            totalMap.put("processCountTotal", processCountTotal);
//
//            totalMap.put("todayReceiveCountTotal", todayReceiveCountTotal);
//
//            model.addAttribute("statisticsMap", statisticsMap);
//            model.addAttribute("totalMap", totalMap);
//
//            List<Manager> managerList = managerService.findByManagerIdList(new ArrayList<>(managerIdList));
//            Map<String, String> managerMap = managerList.stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
//            model.addAttribute("managerMap", managerMap);
//
//        }
//        return "businessManage/claimCaseObjectStatistics";
//    }

    @RequestMapping(value = {"claimCaseObjectStatistics", ""})
    public String claimCaseObjectStatistics(Model model) {
        ClaimCaseObjectScrollVo claimCaseObjectScrollVo = claimCaseObjectService.findListByScroll();
        Map<String, Integer> totalMap = new HashMap<>();
        // 用户ID
        List<String> managerIdList = new ArrayList<>();

        Integer closeCountTotal = 0;                     // 关闭任务数合计
        Integer todayReceiveCountTotal = 0;              // 当日接收合计
        Integer todayFinishCountTotal = 0;               // 当日完成合计
        Integer finishCountTotal = 0;                    // 完成任务数合计
        Integer processCountTotal = 0;                   // 处理中任务数合计
        Map<String, Map<String, Integer>> statisticsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(claimCaseObjectScrollVo.getClaimCaseObjectList())) {
            while (CollectionUtils.isNotEmpty(claimCaseObjectScrollVo.getClaimCaseObjectList())) {
                List<ClaimCaseObject> caseObjectList = claimCaseObjectScrollVo.getClaimCaseObjectList();
                Map<String, List<ClaimCaseObject>> claimCaseObjectStatistics = caseObjectList.stream().collect(groupingBy(a -> a.getAuditer()));

                for (String key : claimCaseObjectStatistics.keySet()) {

                    managerIdList.add(key);

                    Map<String, Integer> countMap = new HashMap<>();
                    Integer closeCount = 0;                     // 关闭任务数
                    Integer todayReceiveCount = 0;              // 当日接收
                    Integer todayFinishCount = 0;               // 当日完成
                    Integer finishCount = 0;                    // 完成任务数
                    Integer processCount = 0;                   // 处理中任务数

                    List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectStatistics.get(key);
                    if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
                        for (ClaimCaseObject claimCaseObject : claimCaseObjectList) {
                            if (claimCaseObject.getIsCaseClosed() == 1) {
                                closeCount++;
                                continue;
                            }
                            if (ClaimCaseObjectStatusEnum.BAX99.getCode().equals(claimCaseObject.getStatus())) {
                                finishCount++;
                            } else {
                                processCount++;
                            }

                            if (DateUtils.format(new Date()).equals(DateUtils.format(claimCaseObject.getCheckEndTime()))) {
                                todayFinishCount++;
                            }

                            if (DateUtils.format(new Date()).equals(DateUtils.format(claimCaseObject.getAuditerShareTime()))) {
                                todayReceiveCount++;
                            }
                        }
                    }

                    closeCountTotal += closeCount;
                    todayReceiveCountTotal += todayReceiveCount;
                    todayFinishCountTotal += todayFinishCount;
                    finishCountTotal += finishCount;
                    processCountTotal += processCount;

                    countMap.put("closeCount", closeCount);
                    countMap.put("todayReceiveCount", todayReceiveCount);
                    countMap.put("todayFinishCount", todayFinishCount);
                    countMap.put("finishCount", finishCount);
                    countMap.put("processCount", processCount);

                    if (statisticsMap.containsKey(key)) {
                        Map<String, Integer> map = statisticsMap.get(key);
                        map.put("closeCount",map.get("closeCount")+closeCount);
                        map.put("todayReceiveCount",map.get("todayReceiveCount")+todayReceiveCount);
                        map.put("todayFinishCount",map.get("closeCount")+todayFinishCount);
                        map.put("finishCount",map.get("finishCount")+finishCount);
                        map.put("processCount",map.get("processCount")+processCount);
                    } else {
                        statisticsMap.put(key, countMap);
                    }
                }
                claimCaseObjectScrollVo = claimCaseObjectService.findListByScrollId(claimCaseObjectScrollVo.getScrollId());
            }

            totalMap.put("closeCountTotal", closeCountTotal);
            totalMap.put("todayReceiveCountTotal", todayReceiveCountTotal);
            totalMap.put("todayFinishCountTotal", todayFinishCountTotal);
            totalMap.put("finishCountTotal", finishCountTotal);
            totalMap.put("processCountTotal", processCountTotal);

            model.addAttribute("statisticsMap", statisticsMap);
            model.addAttribute("totalMap", totalMap);

            List<Manager> managerList = managerService.findByManagerIdList(new ArrayList<>(managerIdList));
            Map<String, String> managerMap = managerList.stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
            model.addAttribute("managerMap", managerMap);
        }
        return "businessManage/claimCaseObjectStatistics";
    }

    @RequestMapping(value = {"claimCaseObjectStatisticsList", ""})
    public String claimCaseObjectStatisticsList(ClaimCaseObjectVo claimCaseObjectVo, HttpServletRequest request, Model model) {
        PageParam pp = Tool.genPageParam(request);
        Map<String, Object> paramMap = new HashMap<>();
        if (claimCaseObjectVo.getStatisticalType() == null) {
            return "error/404";
        }
        paramMap.put("statisticalType", claimCaseObjectVo.getStatisticalType());

        if (StringUtils.isNotBlank(claimCaseObjectVo.getAuditer())) {
            paramMap.put("auditer", claimCaseObjectVo.getAuditer());
        }

        if (StringUtils.isNotBlank(claimCaseObjectVo.getClaimCaseNo())) {
            paramMap.put("claimCaseNo", claimCaseObjectVo.getClaimCaseNo());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatName())) {
            paramMap.put("treatName", claimCaseObjectVo.getTreatName());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatIdNum())) {
            paramMap.put("treatIdNum", claimCaseObjectVo.getTreatIdNum());
        }
        if (claimCaseObjectVo.getType() != null) {
            paramMap.put("type", claimCaseObjectVo.getType());
        }
        if (claimCaseObjectVo.getCategory() != null) {
            paramMap.put("category", claimCaseObjectVo.getCategory());
        }

        PageInfo<ClaimCaseObject> page = claimCaseObjectService.findByStatisticsParam(paramMap, pp);
        model.addAttribute("page", page);
        model.addAttribute("claimCaseObjectVo", claimCaseObjectVo);

        Map<String, Object> statusMap = ClaimCaseObjectStatusEnum.getStatusMap();
        model.addAttribute("statusMap", statusMap);

        Set<String> managerIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (ClaimCaseObject claimCaseObject : page.getList()) {
                if (StringUtils.isNotBlank(claimCaseObject.getAuditer())) {
                    managerIdList.add(claimCaseObject.getAuditer());
                }
            }
            if (CollectionUtils.isNotEmpty(managerIdList)) {
                List<Manager> managerList = managerService.findByManagerIdList(new ArrayList<>(managerIdList));
                Map<String, String> managerMap = managerList.stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
                model.addAttribute("managerMap", managerMap);
            }
        }


        // 出险类型
        Map<String, ApplyTypeNewEnum> typeCategoryMap = ApplyTypeNewEnum.getAppyTypeNewMap();
        model.addAttribute("typeCategoryMap", typeCategoryMap);


        return "claimCaseObject/claimCaseObjectStatisticsList";
    }

   /* @RequestMapping(value = {"claimCaseObject", ""})
    public String claimCaseObject(ClaimCaseObjectSup claimCaseObjectSup, Model model) {

        ClaimCaseObject claimCaseObject;

        List<ClaimCaseObjectAssessment> claimCaseObjectAssessmentList;

        switch (claimCaseObjectSup.getType()) {
            case 1:
            case 3:
                if (StringUtils.isNotBlank(claimCaseObjectSup.getObjectJson())) {
                    claimCaseObject = JSONObject.parseObject(claimCaseObjectSup.getObjectJson(), ClaimCaseObject.class);
                    model.addAttribute("claimCaseObject", claimCaseObject);
                }
                claimCaseObjectSup.setObjectId(Tool.uuid());
                break;
            case 2:
                claimCaseObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectSup.getObjectId());
                model.addAttribute("claimCaseObject", claimCaseObject);
                claimCaseObjectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObjectSup.getObjectId());
                model.addAttribute("claimCaseObjectAssessmentList", claimCaseObjectAssessmentList);
                break;
            case 4:
                JSONObject jsonObject = JSONObject.parseObject(claimCaseObjectSup.getObjectJson());
                claimCaseObject = JSONObject.parseObject(claimCaseObjectSup.getObjectJson(), ClaimCaseObject.class);
                model.addAttribute("claimCaseObject", claimCaseObject);
                claimCaseObjectAssessmentList = JSONObject.parseArray(jsonObject.getString("claimCaseObjectAssessmentList"), ClaimCaseObjectAssessment.class);
                model.addAttribute("claimCaseObjectAssessmentList", claimCaseObjectAssessmentList);
        }

        model.addAttribute("claimCaseObjectSup", claimCaseObjectSup);

        Map<String, String> enumMapPerson = ClaimCaseObjectAssessmentNameEnum.getEnumMapByParentCode("1");
        model.addAttribute("enumMapPerson",enumMapPerson);

        Map<String, String> enumMapMotor = ClaimCaseObjectAssessmentNameEnum.getEnumMapByParentCode("2");
        model.addAttribute("enumMapMotor",enumMapMotor);
        return "claimCaseObject/claimCaseObject";
    }*/

    @RequestMapping(value = {"claimCaseObject", ""})
    public String claimCaseObject(ClaimCaseObjectSup claimCaseObjectSup, Model model) {

        model.addAttribute("claimCaseObjectSup", claimCaseObjectSup);

        return "claimCaseObject/claimCaseObject";
    }

    // 新增/修改 object
    @RequestMapping(value = {"editObject", ""})
    public String editObject(String objectId, String claimCaseId, Model model) {
        if (StringUtils.isBlank(claimCaseId)) {
            return "error/403";
        }
        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseId);
        if (claimCase == null) {
            return "error/403";
        }
        model.addAttribute("claimCase", claimCase);

        ClaimCaseObject claimCaseObject = new ClaimCaseObject();
        if (StringUtils.isNotBlank(objectId)) {
            claimCaseObject = claimCaseObjectService.selectByPrimaryKey(objectId);
            model.addAttribute("claimCaseObject", claimCaseObject);

            // 查找支行
            if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
                if (bankInfo != null) {
                    model.addAttribute("bankInfo", bankInfo);
                }
            }
        } else {
            List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.findByUnique(new HashMap<String, Object>() {{
                this.put("claimCaseId", claimCaseId);
            }});
            if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
                claimCaseObject = claimCaseObjectList.get(claimCaseObjectList.size() - 1);
            }
        }

        model.addAttribute("accidentType", StringUtils.isNotBlank(claimCaseObject.getAccidentType()) ? claimCaseObject.getAccidentType() : "");
        model.addAttribute("accidentLiability", StringUtils.isNotBlank(claimCaseObject.getAccidentLiability()) ? claimCaseObject.getAccidentLiability() : "");
        model.addAttribute("accidentProportion", claimCaseObject.getAccidentProportion() != null ? claimCaseObject.getAccidentProportion().toString() : "");

        return "claimCaseObject/editObject";
    }

    /**
     * 提交
     *
     * @param json
     * @param request
     * @return
     */
    @RequestMapping(value = {"claimCaseObjectSubmit", ""})
    @ResponseBody
    public String claimCaseObjectSubmit(@RequestBody String json, HttpServletRequest request) {

        logger.info(">>>>>>>>>>调用claimCaseObjectSbumit出险对象信息处理接口开始，json：{}", json);

        try {

            ClaimCaseObject claimCaseObject = JSON.parseObject(json, new TypeReference<ClaimCaseObject>() {
            });

            if (claimCaseObject == null) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "传入数据为空");
                }});
            }

            String userId = ShiroSessionUtil.getLoginSession().getId();
            String userName = ShiroSessionUtil.getLoginSession().getRealName(); // 审核人员真实姓名
            String roleName = ShiroSessionUtil.getLoginRoleSession().get(0).getName();// 岗位角色名称

            List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();
            List<ClaimCase> updateClaimCaseList = new ArrayList<>();
//            List<AssessmentReport> insertAssessmentReportList = new ArrayList<>();

            String claimCaseObjectId = Tool.uuid();

            if (claimCaseObject.getType() == null || claimCaseObject.getCategory() == null
                    || StringUtils.isAnyBlank(claimCaseObject.getClaimCaseId(), claimCaseObject.getClaimCaseNo(), claimCaseObject.getName())) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "必填项为空");
                }});
            }

            ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());


            if (claimCase == null) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "案件不存在，caseId:" + claimCaseObject.getClaimCaseId());
                }});
            }

            if (ClaimCaseStatusEum.ACX21.getCode().equals(claimCase.getStatus()) || claimCase.getStatus().endsWith("-1") || claimCase.getStatus().startsWith("aex")) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "案件状态错误，caseId:" + claimCaseObject.getClaimCaseId());
                }});
            }

            if (ClaimCaseStatusEum.ACX10.getCode().equals(claimCase.getStatus())) {
                ClaimCase updateClaimCase = new ClaimCase();
                updateClaimCase.setId(claimCase.getId());
                updateClaimCase.setStatus(ClaimCaseStatusEum.ABX10.getCode());
                updateClaimCaseList.add(updateClaimCase);
            }

            // 判断赔付对象是否存在
            List<ClaimCaseObject> existList = claimCaseObjectService.findByUnique(new HashMap<String, Object>() {{
                this.put("claimCaseId", claimCaseObject.getClaimCaseId());
                this.put("type", claimCaseObject.getType());
                this.put("category", claimCaseObject.getCategory());
                this.put("name", claimCaseObject.getName());
            }});


            if (CollectionUtils.isNotEmpty(existList)) {
                if (existList.size() != 1 || !existList.get(0).getId().equals(claimCaseObject.getId())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "赔付对象已存在");
                    }});
                }
            }

            claimCaseObject.setId(claimCaseObjectId);
            claimCaseObject.setInsCode(claimCase.getInsCode());
            if (StringUtils.isBlank(claimCaseObject.getStatus())) {
                claimCaseObject.setStatus(ClaimCaseObjectStatusEnum.BAX10.getCode());
            }
//            claimCaseObject.setStatus("BAX10");// 初始化状态
            claimCaseObject.setCreator(userId);
            claimCaseObject.setCreateTime(new Date());

            if (StringUtils.isNotBlank(claimCaseObject.getTreatIdNum())) {
                // 根据证件号码判断证件类型
                String checkIdCard = IdCardTool2.IDCardValidate(claimCaseObject.getTreatIdNum());
                // 身份证校验
                if (checkIdCard.equals("")) {
                    claimCaseObject.setTreatIdType("身份证");
                    if (StringUtils.isNotBlank(claimCaseObject.getGender())) {
                        int gender = IdCardTool2.getGender(claimCaseObject.getTreatIdNum());
                        if ((gender == 1 && !"男".equals(claimCaseObject.getGender())) || (gender == 0 && !"女".equals(claimCaseObject.getGender()))) {
                            return JsonBizTool.genJson(new HashMap<String, Object>() {{
                                put("ret", "-1");
                                put("msg", "性别与证件号不符");
                            }});
                        }
                    }
                } else {
                    claimCaseObject.setTreatIdType("其他");
                }
            }


            claimCaseObjectList.add(claimCaseObject);


          /*  // 估损表信息
            AssessmentReport assessmentReport = new AssessmentReport();
            assessmentReport.setId(Tool.uuid());
            assessmentReport.setBaseUserId(userId);
            assessmentReport.setClaimCaseId(claimCase.getId());
            assessmentReport.setClaimCaseNo(claimCase.getClaimCaseNo());
            assessmentReport.setTreatName(claimCase.getTreatName());
            assessmentReport.setTreatIdNum(claimCase.getTreatIdNum());
            assessmentReport.setType(claimCaseObject.getCategory() == 1 ? 1 : 2 );// 1为人伤 2为机动车
            assessmentReport.setCode(claimCaseObject.getName());
            Map<String, Object> reqDataMap = new HashMap<>();
            reqDataMap.put("code", claimCaseObject.getName());
            reqDataMap.put("assessmentReport", new ArrayList<>());
            assessmentReport.setReqData(JsonTool.genByFastJson(reqDataMap));
            assessmentReport.setAuditer(userName);
            assessmentReport.setStatus(-1);
            assessmentReport.setCreator(userId);
            assessmentReport.setCreateTime(new Date());
            insertAssessmentReportList.add(assessmentReport);*/

            // 打印结果描述日志
            String description = getResultDescription(claimCaseObject);
            if ("-1".equals(description)) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "系统异常,描述转换失败:");
                }});
            }

            // 日志
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObjectId);
            claimCaseObjectLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(description);
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);

            // 案件日志
            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(roleName);
            claimCaseLog.setType(4);
            claimCaseLog.setStatus(claimCase.getStatus());
            claimCaseLog.setDescription(description);
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObject));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());
            insertClaimCaseLogList.add(claimCaseLog);


            // 入库sub
            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setInsertClaimCaseObjectList(claimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);
            assessmentDataSup.setUpdateClaimCaseList(updateClaimCaseList);
//            assessmentDataSup.setInsertAssessmentReportList(insertAssessmentReportList);

            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            //lily.Yin 2025/6/7 13:45:添加调估日志
            List<ClaimCaseObject> claimCaseObjectOldList = claimCaseObjectService.selectByClaimCaseId(claimCase.getId());
            BigDecimal objectAppraisalAmount = BigDecimal.ZERO;
            if (claimCaseObjectOldList != null && claimCaseObjectOldList.size() > 0) {
                for (ClaimCaseObject object : claimCaseObjectOldList) {
                    objectAppraisalAmount = objectAppraisalAmount.add(object.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : object.getEstimatedApprovedMoney());
                }
            }
            objectAppraisalAmount = objectAppraisalAmount.add(claimCaseObject.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : claimCaseObject.getEstimatedApprovedMoney());
            claimCase.setAppraisalAmount(objectAppraisalAmount);
            ClaimCaseLossAdjustVo claimCaseLossAdjustVo = new ClaimCaseLossAdjustVo();
            claimCaseLossAdjustVo.setClaimCase(claimCase);
            claimCaseLossAdjustVo.setCause(claimCaseLog.getDescription());
            claimCaseLossAdjustVo.setLossAdjust(claimCase.getAppraisalAmount() == null ? false : true);
            claimCaseLossAdjustVo.setCreateTime(new Date());
            amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_STATE_TRANSITION_PACK, JsonTool.genByFastJson(claimCaseLossAdjustVo));


            // 推送mq，监听案件状态
            Map<String, String> objectVerifyMap = new HashMap<>();
            objectVerifyMap.put("claimCaseId", claimCaseObject.getClaimCaseId());
            objectVerifyMap.put("claimCaseObjectId", claimCaseObjectList.get(0).getId());       // 目前该方法只有单赔付对象添加
            amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_OBJECT_VERIFY, JsonTool.genByFastJson(objectVerifyMap));

            // 监听修改估损金额是否需推饿了么
            amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_PUSH_ELM, claimCase.getId());

            return JsonBizTool.genJson(ExRetEnum.SUCCESS);

        } catch (Exception e) {
            logger.error("系统异常！出险对象信息入库失败");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }
    }

    /**
     * 修改
     *
     * @param json
     * @param request
     * @return
     */
    @RequestMapping(value = {"claimCaseObjectUpdate", ""})
    @ResponseBody
    public String claimCaseObjectUpdate(@RequestBody String json, HttpServletRequest request) {

        ClaimCaseObjectUpdateVo claimCaseObjectUpdateVo = JSON.parseObject(json, new TypeReference<ClaimCaseObjectUpdateVo>() {
        });

        String oldStatus = claimCaseObjectUpdateVo.getOldStatus();
        ClaimCaseObject claimCaseObject = new ClaimCaseObject();
        BeanUtils.copyProperties(claimCaseObjectUpdateVo, claimCaseObject);

        logger.info(">>>>>>>>>>调用claimCaseObjectUpdate出险对象信息修改接口开始，json：{}", JsonTool.genByFastJson(claimCaseObject));

        try {

            String userId = ShiroSessionUtil.getLoginSession().getId();
            String userName = ShiroSessionUtil.getLoginSession().getRealName(); // 审核人员真实姓名
            String roleName = ShiroSessionUtil.getLoginRoleSession().get(0).getName();// 岗位角色名称

            List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();

            ClaimCaseObject claimCaseObjectOld = claimCaseObjectService.selectByPrimaryKey(claimCaseObject.getId());
            if (claimCaseObjectOld == null) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "出险对象信息不存在，id:" + claimCaseObject.getId());
                }});
            }

            ClaimCase claimCase = claimCaseService.selectByPrimaryKey(claimCaseObjectOld.getClaimCaseId());

            if (claimCase == null) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "案件不存在，caseId:" + claimCaseObject.getClaimCaseId());
                }});
            }

            // 判断赔付对象是否存在
            List<ClaimCaseObject> existList = claimCaseObjectService.findByUnique(new HashMap<String, Object>() {{
                this.put("claimCaseId", claimCaseObjectOld.getClaimCaseId());
                this.put("type", claimCaseObject.getType());
                this.put("category", claimCaseObject.getCategory());
                this.put("name", claimCaseObject.getName());
            }});


            if (CollectionUtils.isNotEmpty(existList)) {
                if (existList.size() != 1 || !existList.get(0).getId().equals(claimCaseObject.getId())) {
                    return JsonBizTool.genJson(new HashMap<String, Object>() {{
                        put("ret", "-1");
                        put("msg", "赔付对象已存在");
                    }});
                }
            }

            if (StringUtils.isNotBlank(claimCaseObject.getTreatIdNum())) {
                // 根据证件号码判断证件类型
                String checkIdCard = IdCardTool2.IDCardValidate(claimCaseObject.getTreatIdNum());
                // 身份证校验
                if (checkIdCard.equals("")) {
                    claimCaseObject.setTreatIdType("身份证");
                    if (StringUtils.isNotBlank(claimCaseObject.getGender())) {
                        int gender = IdCardTool2.getGender(claimCaseObject.getTreatIdNum());
                        if ((gender == 1 && !"男".equals(claimCaseObject.getGender())) || (gender == 0 && !"女".equals(claimCaseObject.getGender()))) {
                            return JsonBizTool.genJson(new HashMap<String, Object>() {{
                                put("ret", "-1");
                                put("msg", "性别与证件号不符");
                            }});
                        }
                    }
                } else {
                    claimCaseObject.setTreatIdType("其他");
                }
            }

            if (StringUtils.isNotBlank(claimCaseObject.getStatus()) && !claimCaseObjectOld.getStatus().equals(oldStatus)) {
                claimCaseObject.setStatus(null);
            }

            claimCaseObjectList.add(claimCaseObject);

            // 获取变更描述
            String description = getChangeDescription(claimCaseObjectOld, claimCaseObject);
            if ("-1".equals(description)) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "系统异常,变更描述转换失败:");
                }});
            }

            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseId(claimCaseObjectOld.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseNo(claimCaseObjectOld.getClaimCaseNo());

            if (StringUtils.isNotBlank(oldStatus) && !claimCaseObjectOld.getStatus().equals(oldStatus)) {
                description = "出险对象状态已变化，此操作无效";
            }
            claimCaseObjectLog.setDescription(description);
            if (StringUtils.isNotBlank(claimCaseObject.getStatus())) {
                claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            } else {
                claimCaseObjectLog.setStatus(claimCaseObjectOld.getStatus());
            }
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);

            // 案件日志
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();
            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObjectOld.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObjectOld.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(roleName);
            claimCaseLog.setType(4);
            claimCaseLog.setStatus(claimCase.getStatus());
            claimCaseLog.setDescription(description);
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObject));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());
            insertClaimCaseLogList.add(claimCaseLog);

            // 入库sub
            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setUpdateClaimCaseObjectList(claimCaseObjectList);
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);

            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            Map<String, String> objectVerifyMap = new HashMap<>();
            objectVerifyMap.put("claimCaseId", claimCase.getId());
            amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_OBJECT_VERIFY, JsonTool.genByFastJson(objectVerifyMap));

            return JsonBizTool.genJson(ExRetEnum.SUCCESS);

        } catch (Exception e) {
            logger.error("系统异常！出险对象信息更新失败");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }
    }

    /**
     * 损失项目影像审核通过
     *
     * @param claimCaseObjectId
     * @param request
     * @return
     */
    @RequestMapping(value = {"claimCaseObjectAttachApprove", ""})
    @ResponseBody
    public String claimCaseObjectAttachApprove(String claimCaseObjectId, HttpServletRequest request) {
        if (StringUtils.isBlank(claimCaseObjectId)) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }
        ClaimCaseObject claimCaseObjectOld = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);
        if (claimCaseObjectOld == null) {
            return JsonBizTool.genJson(new HashMap<String, Object>() {{
                put("ret", "-1");
                put("msg", "出险对象信息不存在，id:" + claimCaseObjectId);
            }});
        }

        ClaimCaseObject updateClaimCaseObject = new ClaimCaseObject();
        updateClaimCaseObject.setId(claimCaseObjectId);
        updateClaimCaseObject.setStatus(ClaimCaseObjectStatusEnum.BAX21.getCode());
        claimCaseObjectService.updateByPrimaryKeySelective(updateClaimCaseObject);
        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    /**
     * 损失项目影像审核不通过
     *
     * @param json
     * @param request
     * @return
     */
    @RequestMapping(value = {"claimCaseObjectAttachRefuse", ""})
    @ResponseBody
    public String claimCaseObjectAttachRefuse(@RequestBody String json, HttpServletRequest request) {
        if (StringUtils.isBlank(json)) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }

        JSONObject jsonObject = JSONObject.parseObject(json);
        String claimCaseObjectId = jsonObject.getString("claimCaseObjectId");
        String missingAttachments = jsonObject.getString("missingAttachments");
        String remark = jsonObject.getString("remark");

        if (StringUtils.isBlank(claimCaseObjectId) || StringUtils.isBlank(missingAttachments)) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER);
        }

        ClaimCaseObject claimCaseObjectOld = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);
        if (claimCaseObjectOld == null) {
            return JsonBizTool.genJson(new HashMap<String, Object>() {{
                put("ret", "-1");
                put("msg", "出险对象信息不存在，id:" + claimCaseObjectId);
            }});
        }

        ClaimCaseObject updateClaimCaseObject = new ClaimCaseObject();
        updateClaimCaseObject.setId(claimCaseObjectId);
        updateClaimCaseObject.setStatus(ClaimCaseObjectStatusEnum.BAX11.getCode());
        updateClaimCaseObject.setMissingAttachments(missingAttachments);
        updateClaimCaseObject.setRemark(StringUtils.isNotBlank(remark) ? remark : "");
        claimCaseObjectService.updateByPrimaryKeySelective(updateClaimCaseObject);
        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    /**
     * 删除
     *
     * @param claimCaseObjectId
     * @param request
     * @return
     */
    @RequestMapping(value = {"claimCaseObjectDelete", ""})
    @ResponseBody
    public String claimCaseObjectDelete(String claimCaseObjectId, HttpServletRequest request) {

        logger.info(">>>>>>>>>>调用claimCaseObjectDelete出险对象信息删除接口开始，json：{}", claimCaseObjectId);

        try {

            String userId = ShiroSessionUtil.getLoginSession().getId();
            String userName = ShiroSessionUtil.getLoginSession().getRealName(); // 审核人员真实姓名
            String roleName = ShiroSessionUtil.getLoginRoleSession().get(0).getName();// 岗位角色名称

            List<ClaimCaseObject> deleteClaimCaseObjectList = new ArrayList<>();
            List<ClaimCaseObjectLog> claimCaseObjectLogList = new ArrayList<>();

            ClaimCaseObject claimCaseObjectOld = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);
            if (claimCaseObjectOld == null) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "出险对象信息不存在，id:" + claimCaseObjectId);
                }});
            }

            List<ClaimCaseObjectAssessment> claimCaseObjectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObjectId);
            List<ClaimCaseObjectPayment> claimCaseObjectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObjectId);


            ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObjectOld.getClaimCaseId());

            if (claimCase == null) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "案件不存在，caseId:" + claimCaseObjectOld.getClaimCaseId());
                }});
            }

            // 删除object表
            deleteClaimCaseObjectList.add(claimCaseObjectOld);

            String description = getResultDescription(claimCaseObjectOld);
            if ("-1".equals(description)) {
                return JsonBizTool.genJson(new HashMap<String, Object>() {{
                    put("ret", "-1");
                    put("msg", "系统异常,描述转换失败:");
                }});
            }

            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObjectOld.getId());
            claimCaseObjectLog.setClaimCaseId(claimCaseObjectOld.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseNo(claimCaseObjectOld.getClaimCaseNo());
            claimCaseObjectLog.setDescription("删除信息：" + description);
            claimCaseObjectLog.setStatus("BAX-1");
            claimCaseObjectLog.setCreator(userName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogList.add(claimCaseObjectLog);

            // 案件日志
            List<ClaimCaseLog> insertClaimCaseLogList = new ArrayList<>();
            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCase.getId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObjectOld.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(roleName);
            claimCaseLog.setType(4);
            claimCaseLog.setStatus(claimCase.getStatus());
            claimCaseLog.setDescription("删除信息：" + description);
            // 记录删除的赔付对象数据
            claimCaseLog.setReqData(JsonTool.genByFastJson(claimCaseObjectOld));
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());
            insertClaimCaseLogList.add(claimCaseLog);

            // 入库sub
            AssessmentDataSup assessmentDataSup = new AssessmentDataSup();
            assessmentDataSup.setDeleteClaimCaseObjectList(deleteClaimCaseObjectList);
            if (CollectionUtils.isNotEmpty(claimCaseObjectAssessmentList)) {
                assessmentDataSup.setDeleteClaimCaseObjectAssessmentList(claimCaseObjectAssessmentList);
            }
            if (CollectionUtils.isNotEmpty(claimCaseObjectPaymentList)) {
                assessmentDataSup.setDeleteClaimCaseObjectPaymentList(claimCaseObjectPaymentList);
            }
            assessmentDataSup.setInsertClaimCaseObjectLogList(claimCaseObjectLogList);
            assessmentDataSup.setInsertClaimCaseLogList(insertClaimCaseLogList);

            assessmentBusinessService.claimCaseObjectConfirm(assessmentDataSup);

            //lily.Yin 2025/6/7 13:36: 添加调估日志
            List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.selectByClaimCaseId(claimCase.getId());
            BigDecimal objectAppraisalAmount = BigDecimal.ZERO;
            // 数据库已经删除赔付物并同步es，刚好claimCaseObjectList查不出数据了
            if (claimCaseObjectList != null && CollectionUtils.isNotEmpty(claimCaseObjectList)) {
                claimCaseObjectList.removeIf(claimCaseObject -> StringUtils.equals(claimCaseObjectId, claimCaseObject.getId()));
                // 考虑赔付物只有一项
                if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
                    for (ClaimCaseObject object : claimCaseObjectList) {
                        if (!StringUtils.equals(claimCaseObjectId, object.getId())) {
                            objectAppraisalAmount = objectAppraisalAmount.add(object.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : object.getEstimatedApprovedMoney());
                        }
                    }
                }
            }
            //能删除赔付物就代表有过赔付物，则需修改案损调整，就算删到没有赔付物也算是调估了
            claimCase.setAppraisalAmount(objectAppraisalAmount);
            ClaimCaseLossAdjustVo claimCaseLossAdjustVo = new ClaimCaseLossAdjustVo();
            claimCaseLossAdjustVo.setClaimCase(claimCase);
            claimCaseLossAdjustVo.setCause(claimCaseLog.getDescription());
            claimCaseLossAdjustVo.setLossAdjust(claimCase.getAppraisalAmount() == null ? false : true);
            claimCaseLossAdjustVo.setCreateTime(new Date());
            amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_STATE_TRANSITION_PACK, JsonTool.genByFastJson(claimCaseLossAdjustVo));

            // 删除后 校验其他赔付对象是否都已完成
            Map<String, String> objectVerifyMap = new HashMap<>();
            objectVerifyMap.put("claimCaseId", claimCase.getId());
            amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_OBJECT_VERIFY, JsonTool.genByFastJson(objectVerifyMap));

            // 监听修改估损金额是否需推饿了么
            amqpTemplate.convertAndSend(QueueName.KD_CLAIM_CASE_PUSH_ELM, claimCase.getId());

            return JsonBizTool.genJson(ExRetEnum.SUCCESS);
        } catch (Exception e) {
            logger.error("系统异常！出险对象信息删除失败");
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.FAIL);
        }
    }

    @RequestMapping(value = {"sendUploadImgSms"})
    @ResponseBody
    public String sendUploadImgSms(String id) {
        if (StringUtils.isBlank(id)) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            return JsonBizTool.genJson("-1", "赔付对象不存在");
        }
        if (!claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX11.getCode())) {
            return JsonBizTool.genJson("-1", "当前非补材状态");
        }
        if (StringUtils.isBlank(claimCaseObject.getMobile())) {
            return JsonBizTool.genJson("-1", "当前赔付对象手机号为空");
        }

        if (StringUtils.isBlank(claimCaseObject.getMissingAttachments())) {
            return JsonBizTool.genJson("-1", "当前无缺少材料");
        }

        List<String> attachmentList = Arrays.asList(claimCaseObject.getMissingAttachments().split(","));

        List<String> remarkList = new ArrayList<>();

        // 只获取3个
        for (int i = 0; i < attachmentList.size(); i++) {
            if (i >= 3) {
                remarkList.set(2, remarkList.get(2) + "等");
                break;
            }
            remarkList.add(ImgInfoEnum.codeToName(attachmentList.get(i)));
        }
        // 发送 赔付对象补材
        Map<String, Object> smsMap = new TreeMap<>();
        smsMap.put("claimCaseId", claimCaseObject.getClaimCaseId());
        smsMap.put("remark", StringUtils.join(remarkList, "、"));
        smsMap.put("mobile", claimCaseObject.getMobile());
        smsMap.put("type", "09");
        amqpTemplate.convertAndSend(QueueName.KD_ROASTDUCK_RABBIT_SEND_MESSAGE, JsonTool.genByFastJson(smsMap));

        return JsonBizTool.genJson("0", "短信已成功发送至" + claimCaseObject.getMobile());

    }

    /**
     * 获取结果描述
     *
     * @param reqData status -1 暂存 0提交
     * @return
     */
    public String getResultDescription(ClaimCaseObject reqData) {

        // object字段描述
        StringBuilder objectSb = new StringBuilder();

        // 列表描述
        StringBuilder listSb = new StringBuilder();

        listSb.append("出险对象估损信息清单：<br><br>");

        List<String> fieldList = new ArrayList<>(Arrays.asList("type", "category", "treatName", "treatIdNum", "mobile", "gender", "injuredArea", "injuredDesc", "hospitalName", "estimatedApprovedMoney",
                "workAddress", "homeAddress", "name", "carNumber", "bankAccount", "bankCard", "bankName", "bankInfoId", "accidentType", "accidentLiability", "accidentProportion"
        ));


        for (Field declaredField : reqData.getClass().getDeclaredFields()) {
            try {
                String declaredFieldName = declaredField.getName();
                declaredField.setAccessible(true);

                /*if (!"id".equals(declaredFieldName) && !"claimCaseId".equals(declaredFieldName) && !"auditer".equals(declaredFieldName)
                        && !"insAuditer".equals(declaredFieldName) && !"status".equals(declaredFieldName) && !"creator".equals(declaredFieldName)
                        && !"createTime".equals(declaredFieldName)) {*/
                if (fieldList.contains(declaredFieldName)) {
                    if (declaredField.get(reqData) != null) {
                        // 时间转换
                        if ("firstRegistrationTime".equals(declaredFieldName) || "lossAssessmentTime".equals(declaredFieldName)) {
                            objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName) + ":" + DateUtils.format((Date) declaredField.get(reqData)) + "<br>");
                        } else if ("type".equals(declaredFieldName)) {
                            objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName) + ":" + ((Integer) declaredField.get(reqData) == 1 ? "骑手" : "三者") + "<br>");
                        } else if ("category".equals(declaredFieldName)) {
                            objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName) + ":" + ((Integer) declaredField.get(reqData) == 1 ? "人伤" : (Integer) declaredField.get(reqData) == 2 ? "物损" : "机动车") + "<br>");
                        } else if ("bankInfoId".equals(declaredFieldName) && StringUtils.isNotBlank(declaredField.get(reqData).toString())) {
                            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(declaredField.get(reqData).toString());
                            objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName) + ":" + bankInfo.getBankName() + "<br>");
                        } else {
                            objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName) + ":" + declaredField.get(reqData) + "<br>");
                        }
                    }
                }


            } catch (IllegalAccessException e) {
                return "-1";
            }
        }

        return listSb.toString() + "<br>" + objectSb.toString();
    }


    /**
     * 获取前后对比描述
     *
     * @param beforeData
     * @param newData
     * @return
     */
    private String getChangeDescription(ClaimCaseObject beforeData, ClaimCaseObject newData) {

        // 增加描述
        StringBuilder firstSb = new StringBuilder();

        // 增加描述
        StringBuilder addSb = new StringBuilder();
        // delete描述
        StringBuilder deleteSb = new StringBuilder();
        // 变更描述
        StringBuilder changeSb = new StringBuilder();

        // 其他字段描述
        StringBuilder objectSb = new StringBuilder();

        firstSb.append("客服修改提交此估损清单：<br><br>");

        List<String> fieldList = new ArrayList<>(Arrays.asList("type", "category", "treatName", "treatIdNum", "mobile", "gender", "injuredArea", "injuredDesc", "hospitalName", "estimatedApprovedMoney",
                "workAddress", "homeAddress", "name", "carNumber", "bankAccount", "bankCard", "bankName", "bankInfoId"
        ));

        // 变更描述
        for (Field field : newData.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            try {

                /*if (!"id".equals(fieldName) && !"claimCaseId".equals(fieldName) && !"auditer".equals(fieldName)
                && !"insAuditer".equals(fieldName) && !"status".equals(fieldName) && !"creator".equals(fieldName)
                && !"createTime".equals(fieldName) && !"treatIdType".equals(fieldName)) {*/
                if (fieldList.contains(fieldName)) {
                    String oldValue = field.get(beforeData) == null ? null : field.get(beforeData).toString();
                    String newValue = field.get(newData) == null ? null : field.get(newData).toString();

                    // 转换时间格式
                    if ("firstRegistrationTime".equals(fieldName) || "lossAssessmentTime".equals(fieldName)) {

                        if (StringUtils.isNotBlank(oldValue)) {
                            oldValue = DateUtils.format((Date) field.get(beforeData));
                        }

                        if (StringUtils.isNotBlank(newValue)) {
                            newValue = DateUtils.format((Date) field.get(newData));
                        }
                    }

                    // 金额转换
                    if ("accidentProportion".equals(fieldName) || "lossAssessmentSum".equals(fieldName) || "nuclearLossSum".equals(fieldName)
                            || "residualValue".equals(fieldName) || "residualNuclearLossValue".equals(fieldName) || "verifyAmout".equals(fieldName)
                            || "dutyRate".equals(fieldName) || "deductFee".equals(fieldName) || "estimatedOverallLoss".equals(fieldName)
                            || "estimatedApprovedMoney".equals(fieldName)) {

                        if (StringUtils.isNotBlank(oldValue)) {
                            oldValue = new BigDecimal(oldValue).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                        }

                        if (StringUtils.isNotBlank(newValue)) {
                            newValue = new BigDecimal(newValue).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                        }
                    }

                    // 支行转换
                    if ("bankInfoId".equals(fieldName)) {
                        if (StringUtils.isNotBlank(oldValue)) {
                            oldValue = bankInfoService.selectByPrimaryKey(oldValue).getBankName();
                        }

                        if (StringUtils.isNotBlank(newValue)) {
                            newValue = bankInfoService.selectByPrimaryKey(newValue).getBankName();
                        }
                    }

                    if (StringUtils.isBlank(oldValue) && StringUtils.isNotBlank(newValue)) {
                        addSb.append("新增" + ObjectChineseEnum.codeToName(fieldName) + ":" + newValue + "<br>");
                    }

                    if (StringUtils.isNotBlank(oldValue) && StringUtils.isBlank(newValue)) {
                        deleteSb.append("删除" + ObjectChineseEnum.codeToName(fieldName) + ":" + oldValue + "<br>");
                    }

                    if (StringUtils.isNotBlank(oldValue) && StringUtils.isNotBlank(newValue) && !oldValue.equals(newValue)) {
                        changeSb.append("修改" + ObjectChineseEnum.codeToName(fieldName) + ":" + oldValue + " --> " + newValue + "<br>");
                    }
                }

            } catch (Exception e) {
                System.out.println(e);
                return "-1";
            }

        }


        // 结果描述
        for (Field declaredField : newData.getClass().getDeclaredFields()) {
            try {
                declaredField.setAccessible(true);
                String declaredFieldName = declaredField.getName();

                /*if (!"id".equals(declaredFieldName) && !"claimCaseId".equals(declaredFieldName) && !"auditer".equals(declaredFieldName)
                        && !"insAuditer".equals(declaredFieldName) && !"status".equals(declaredFieldName) && !"creator".equals(declaredFieldName)
                        && !"createTime".equals(declaredFieldName)) {*/
                if (fieldList.contains(declaredFieldName)) {
                    if (declaredField.get(newData) != null) {
                        // 时间转换
                        if ("firstRegistrationTime".equals(declaredFieldName) || "lossAssessmentTime".equals(declaredFieldName)) {
                            objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName) + ":" + DateUtils.format((Date) declaredField.get(newData)) + "<br>");
                        } else if ("type".equals(declaredFieldName)) {
                            objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName) + ":" + ((Integer) declaredField.get(newData) == 1 ? "骑手" : "三者") + "<br>");
                        } else if ("category".equals(declaredFieldName)) {
                            objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName) + ":" + ((Integer) declaredField.get(newData) == 1 ? "人伤" : (Integer) declaredField.get(newData) == 2 ? "物损" : "机动车") + "<br>");
                        } else if ("bankInfoId".equals(declaredFieldName) && StringUtils.isNotBlank(declaredField.get(newData).toString())) {
                            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(declaredField.get(newData).toString());
                            objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName) + ":" + bankInfo.getBankName() + "<br>");
                        } else {
                            objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName) + ":" + declaredField.get(newData) + "<br>");
                        }
                    }
                }

            } catch (IllegalAccessException e) {
                return "-1";
            }
        }

        // 按顺序拼接
        if (StringUtils.isNotBlank(addSb)) {
            firstSb.append(addSb);
        }
        if (StringUtils.isNotBlank(deleteSb)) {
            firstSb.append(deleteSb);
        }
        if (StringUtils.isNotBlank(changeSb)) {
            firstSb.append(changeSb);
        }

        firstSb.append("---------------------------------------------------------------------------------------------------------<br><br>");

        firstSb.append(objectSb);

        return firstSb.toString();
    }


    @RequestMapping("claimCaseObjectInsStatusStatistics")
    public String claimCaseObjectInsStatusStatistics(Model model, String enumStr) {

        Map<String, Object> paramMap = new HashMap<>();
        Map<String, String> insStatusMap = new LinkedHashMap<>();
        if (StringUtils.isNotBlank(enumStr)) {
            Arrays.asList(enumStr.split(",")).stream().forEach(code -> {
                insStatusMap.put(code, ClaimCaseObjectStatusEnum.codeToMsg(code));
            });
        } else {
            insStatusMap.put(ClaimCaseObjectStatusEnum.BAX25.getCode(), ClaimCaseObjectStatusEnum.BAX25.getMsg());
            insStatusMap.put(ClaimCaseObjectStatusEnum.BAX26.getCode(), ClaimCaseObjectStatusEnum.BAX26.getMsg());
            insStatusMap.put(ClaimCaseObjectStatusEnum.BAX27.getCode(), ClaimCaseObjectStatusEnum.BAX27.getMsg());
            insStatusMap.put(ClaimCaseObjectStatusEnum.BAX35.getCode(), ClaimCaseObjectStatusEnum.BAX35.getMsg());
            insStatusMap.put(ClaimCaseObjectStatusEnum.BAX36.getCode(), ClaimCaseObjectStatusEnum.BAX36.getMsg());
            insStatusMap.put(ClaimCaseObjectStatusEnum.BAX37.getCode(), ClaimCaseObjectStatusEnum.BAX37.getMsg());
        }


        Map<String, String> insCodeMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "POLICY_REPUSH_INS_CODE");


        paramMap.put("status", insStatusMap);
        model.addAttribute("insStatusMap", insStatusMap);
        paramMap.put("insCode", insCodeMap);
        model.addAttribute("insCodeMap", insCodeMap);

        Map<String, Map<String, String>> resultMap = claimCaseObjectService.findCount4StatusAndInsCode(paramMap);
        String resultJsonStr = JSON.toJSONString(resultMap);
        if (resultJsonStr.contains("其他")) {
            model.addAttribute("isHaveChannel", "true");
        }
        model.addAttribute("resultMap", resultMap);

        return "businessManage/claimCaseObjectInsStatusStatistics";
    }

    @RequestMapping(value = {"claimCaseObjectMonthStatistics", ""})
    public String claimCaseObjectMonthStatistics(ClaimCaseObjectMonthStatisticsVo claimCaseObjectVo, Model model) {
        Map<String, Object> paramMap = new HashMap<>();
        if (StringUtils.isBlank(claimCaseObjectVo.getCreateTime())) {
            claimCaseObjectVo.setCreateTime(DateUtils.format(new Date(), kd.beijingRoastDuck.util.DateUtils.FORMAT_DATE_YYYY_MM));
        }
        // 校验开始日期和结束日期的非空依赖关系
        if ((StringUtils.isNotBlank(claimCaseObjectVo.getStartTime()) && StringUtils.isBlank(claimCaseObjectVo.getEndTime())) ||
                (StringUtils.isBlank(claimCaseObjectVo.getStartTime()) && StringUtils.isNotBlank(claimCaseObjectVo.getEndTime()))) {
            model.addAttribute("error", "开始日期和结束日期必须同时填写");
            return "businessManage/claimCaseObjectMonthStatistics";
        }

        // 验证日期范围
        if (StringUtils.isNotBlank(claimCaseObjectVo.getStartTime()) && StringUtils.isNotBlank(claimCaseObjectVo.getEndTime())) {
            try {
                Date startDate = DateUtils.parse(claimCaseObjectVo.getStartTime(), "yyyy-MM-dd");
                Date endDate = DateUtils.parse(claimCaseObjectVo.getEndTime(), "yyyy-MM-dd");

                // 计算日期差值（90天）
                Calendar cal = Calendar.getInstance();
                cal.setTime(startDate);
                cal.add(Calendar.DAY_OF_YEAR, 90);
                Date ninetyDaysLater = cal.getTime();

                if (endDate.after(ninetyDaysLater)) {
                    model.addAttribute("error", "查询范围不能超过90天");
                    return "businessManage/claimCaseObjectMonthStatistics";
                }
            } catch (Exception e) {
                model.addAttribute("error", "日期格式错误");
                return "businessManage/claimCaseObjectMonthStatistics";
            }
        }

        if (StringUtils.isNotBlank(claimCaseObjectVo.getStartTime())) {
            paramMap.put("startTime",claimCaseObjectVo.getStartTime());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getEndTime())) {
            paramMap.put("endTime",claimCaseObjectVo.getEndTime());
        }
        paramMap.put("createTime", claimCaseObjectVo.getCreateTime());
        if (StringUtils.isNotBlank(claimCaseObjectVo.getCheckName())) {
            Manager manager = managerService.selectByRealName(claimCaseObjectVo.getCheckName());
            if (manager != null && manager.getStatus()==1) {
                paramMap.put("auditer", manager.getId());
            }
        }

        Map<String, Map<String, Integer>> statisticsMap = new HashMap<>();

        Map<String, List<ClaimCaseObjectV1>> claimCaseObjectStatistics = claimCaseObjectService.findClaimCaseObjectMonthStatistics(paramMap);
        if (claimCaseObjectStatistics != null && !claimCaseObjectStatistics.isEmpty()) {
            for (String key : claimCaseObjectStatistics.keySet()) {
                Map<String, Integer> countMap = new HashMap<>();

                Set<String> closeSet = new HashSet<>();
                Set<String> finishSet = new HashSet<>();
                Integer monthCount = 0;                     // 月进件量
                Integer finishCount = 0;                    // 当月结案数量

                List<ClaimCaseObjectV1> claimCaseObjectList = claimCaseObjectStatistics.get(key);
                if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
                    for (ClaimCaseObjectV1 claimCaseObject : claimCaseObjectList) {
                        //如果案件状态为已关闭，则关闭案件量+1，其他数量不添加
                        if (ClaimCaseStatusEum.AAX_1.getCode().equals(claimCaseObject.getClaimCaseStatus())
                                || ClaimCaseStatusEum.ABX_1.getCode().equals(claimCaseObject.getClaimCaseStatus())
                                || ClaimCaseStatusEum.ACX_1.getCode().equals(claimCaseObject.getClaimCaseStatus())) {
                            if (!closeSet.contains(claimCaseObject.getClaimCaseNo())) {
                                closeSet.add(claimCaseObject.getClaimCaseNo());
                            }
                            continue;
                        }
                        monthCount++;


                        if (ClaimCaseStatusEum.AEX20.getCode().equals(claimCaseObject.getClaimCaseStatus()) && !finishSet.contains(claimCaseObject.getClaimCaseNo())) {
                            finishCount++;
                            finishSet.add(claimCaseObject.getClaimCaseNo());
                        }
                    }
                }


                countMap.put("monthCount", monthCount);
                countMap.put("finishCount", finishCount);
                countMap.put("passCount", 0);
                countMap.put("closeCountFromStart", 0);
                countMap.put("finishCountFromStart", 0);
                statisticsMap.put(key, countMap);
            }
        }


        Map<String, List<ClaimCaseObjectV1>> passCountMap = claimCaseObjectService.findClaimCaseObjectMonthPassCount(paramMap);
        if (passCountMap != null && !passCountMap.isEmpty()) {
            for (String key : passCountMap.keySet()) {
                Map<String, Integer> countMap = new HashMap<>();
                if (statisticsMap.containsKey(key)) {
                    countMap = statisticsMap.get(key);
                } else {
                    countMap.put("monthCount", 0);
                    countMap.put("finishCount", 0);
                    countMap.put("closeCountFromStart", 0);
                    countMap.put("finishCountFromStart", 0);
                }

                countMap.put("passCount", passCountMap.get(key).size());
            }
        }


        Map<String, List<ClaimCaseObjectV1>> fromStartMap = claimCaseObjectService.findClaimCaseObjectMonthStatisticsFromStart(paramMap);
        if (fromStartMap != null && !fromStartMap.isEmpty()) {
            for (String key : fromStartMap.keySet()) {
                Map<String, Integer> countMap = new HashMap<>();
                if (statisticsMap.containsKey(key)) {
                    countMap = statisticsMap.get(key);
                } else {
                    countMap.put("monthCount", 0);
                    countMap.put("finishCount", 0);
                    countMap.put("passCount", 0);
                }

                Set<String> closeSet = new HashSet<>();
                Set<String> finishSet = new HashSet<>();
                Integer closeCountFromStart = 0;                     // 平台开通至今当月关闭案件量
                Integer finishCountFromStart = 0;                    // 平台开通至今当月结案数量

                List<ClaimCaseObjectV1> claimCaseObjectList = fromStartMap.get(key);
                if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
                    for (ClaimCaseObjectV1 claimCaseObject : claimCaseObjectList) {
                        //如果案件状态为已关闭，则关闭案件量+1，其他数量不添加
                        if (ClaimCaseStatusEum.AAX_1.getCode().equals(claimCaseObject.getClaimCaseStatus())
                                || ClaimCaseStatusEum.ABX_1.getCode().equals(claimCaseObject.getClaimCaseStatus())
                                || ClaimCaseStatusEum.ACX_1.getCode().equals(claimCaseObject.getClaimCaseStatus())) {
                            if (!closeSet.contains(claimCaseObject.getClaimCaseNo())) {
                                closeCountFromStart++;
                                closeSet.add(claimCaseObject.getClaimCaseNo());
                            }
                            continue;
                        }


                        if (ClaimCaseStatusEum.AEX20.getCode().equals(claimCaseObject.getClaimCaseStatus()) && !finishSet.contains(claimCaseObject.getClaimCaseNo())) {
                            finishCountFromStart++;
                            finishSet.add(claimCaseObject.getClaimCaseNo());
                        }
                    }
                }

                countMap.put("closeCountFromStart", closeCountFromStart);
                countMap.put("finishCountFromStart", finishCountFromStart);

                statisticsMap.put(key, countMap);
            }
        }

        List<Map.Entry<String, Map<String, Integer>>> entries = new ArrayList<>(statisticsMap.entrySet());
        entries.sort((e1, e2) -> {
            // 降序排序monthCount
            int monthCount2 = e2.getValue().getOrDefault("monthCount", 0);
            int monthCount1 = e1.getValue().getOrDefault("monthCount", 0);
            return Integer.compare(monthCount2, monthCount1);
        });
        Map<String, Map<String, Integer>> sortedMap = new LinkedHashMap<>();
        for (Map.Entry<String, Map<String, Integer>> entry : entries) {
            sortedMap.put(entry.getKey(), entry.getValue());
        }
        //用户ID
        List<String> managerIdList = new ArrayList<>(statisticsMap.keySet());
        model.addAttribute("statisticsMap", sortedMap);
        model.addAttribute("claimCaseObjectVo", claimCaseObjectVo);

        List<Manager> managerList = managerService.findByManagerIdListIsValid(new ArrayList<>(managerIdList));
        Map<String, String> managerMap = managerList.stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
        model.addAttribute("managerMap", managerMap);
        return "businessManage/claimCaseObjectMonthStatistics";
    }


    @RequestMapping(value = {"auditingClaimCaseObject", ""})
    public String auditingClaimCaseObject(String claimCaseObjectId, String claimCaseId , Model model) {

        if (StringUtils.isBlank(claimCaseObjectId) || StringUtils.isBlank(claimCaseId)) {
            return "error/403";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);
        if (claimCaseObject == null) {
            return "error/403";
        }
        ClaimCase claimCase = claimCaseService.selectByPrimaryKey(claimCaseObject.getClaimCaseId());
        if (claimCase == null) {
            return "error/403";
        }
        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
        claimCaseObjectList.add(claimCaseObject);

        // 审核人对应进行中任务数
        Map<String,Integer> countMap = claimCaseObjectService.findAuditerDoingCountMap();

        // 人伤审核岗
        String rsRoleIds = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "CCIC_POLICY_ELE", "RS_AUDITER_ROLE_ID");
        List<String> rsRoleIdList = Arrays.asList(rsRoleIds.split(","));
        // 物损审核岗
        String wsRoleIds = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "CCIC_POLICY_ELE", "WS_AUDITER_ROLE_ID");
        List<String> wsRoleIdList = Arrays.asList(wsRoleIds.split(","));

        // 人伤审核人员信息
        List<Manager> allRsManagerList = new ArrayList<>();

        for (String rsRoleId : rsRoleIdList) {
            List<String> rsManagerIdList= managerService.findByRoleId(rsRoleId);
            List<Manager> rsManagerList = managerService.findByManagerIdList(rsManagerIdList);
            allRsManagerList.addAll(rsManagerList);
        }

        // userId - 剩余任务数
        Map<String,String> rsAuditerMap = allRsManagerList.stream().filter(manager -> manager.getStatus() != 0).distinct().collect(Collectors.toMap(a -> a.getId(),a -> a.getRealName() + "("+ (countMap.get(a.getId()) == null ? "0" : countMap.get(a.getId()))  + ")"));

        // 物损审核人员信息
        List<Manager> allWsManagerList = new ArrayList<>();

        for (String wsRoleId : wsRoleIdList) {
            List<String> wsManagerIdList= managerService.findByRoleId(wsRoleId);
            List<Manager> wsManagerList = managerService.findByManagerIdList(wsManagerIdList);
            allWsManagerList.addAll(wsManagerList);
        }

        // userId - 剩余任务数
        Map<String,String> wsAuditerMap = allWsManagerList.stream().filter(manager -> manager.getStatus() != 0).distinct().collect(Collectors.toMap(a -> a.getId(),a -> a.getRealName() + "("+ (countMap.get(a.getId()) == null ? "0" : countMap.get(a.getId()))  + ")"));

        // 案件状态Map
        Map<String, ClaimCaseStatusEum> claimCaseStatusEumMap = new HashMap<>();
        for (ClaimCaseStatusEum claimCaseStatusEum : ClaimCaseStatusEum.values()) {
            claimCaseStatusEumMap.put(claimCaseStatusEum.getCode(), claimCaseStatusEum);
        }

        Map<String, ApplyTypeNewEnum> applyTypeNewEnumHashMap = new HashMap<>();
        for (ApplyTypeNewEnum applyTypeNewEnum : ApplyTypeNewEnum.values()) {
            applyTypeNewEnumHashMap.put(applyTypeNewEnum.getCode(), applyTypeNewEnum);
        }
        model.addAttribute("applyTypeNewEnumMap", applyTypeNewEnumHashMap);
        model.addAttribute("claimCaseStatusEumMap", claimCaseStatusEumMap);
        model.addAttribute("rsAuditerMap", letterSorting(rsAuditerMap));
        model.addAttribute("wsAuditerMap", letterSorting(wsAuditerMap));
        model.addAttribute("claimCaseObjectList", claimCaseObjectList);
        model.addAttribute("claimCase", claimCase);

        Map<String, Object> imgInfoMap = ImgInfoEnum.getImgInfoMap();
        model.addAttribute("supplementaryMaterials", imgInfoMap);
        model.addAttribute("objectId", claimCaseObjectId);
        model.addAttribute("claimCaseId", claimCaseId);

        return "claimCaseObject/auditingPretreatmentCaseObject";
    }


    // 新增/修改 object
    @RequestMapping(value = {"editPretreatmentCaseObject", ""})
    public String editPretreatmentCaseObject(String objectId, String claimCaseId, Model model) {
        if (StringUtils.isBlank(claimCaseId)) {
            return "error/403";
        }
        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseId);
        if (claimCase == null) {
            return "error/403";
        }
        model.addAttribute("claimCase", claimCase);

        ClaimCaseObject claimCaseObject = new ClaimCaseObject();
        if (StringUtils.isNotBlank(objectId)) {
            claimCaseObject = claimCaseObjectService.selectByPrimaryKey(objectId);
            model.addAttribute("claimCaseObject", claimCaseObject);

            // 查找支行
            if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
                if (bankInfo != null) {
                    model.addAttribute("bankInfo", bankInfo);
                }
            }
        } else {
            List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.findByUnique(new HashMap<String, Object>() {{
                this.put("claimCaseId", claimCaseId);
            }});
            if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
                claimCaseObject = claimCaseObjectList.get(claimCaseObjectList.size() - 1);
            }
        }

        Map<String, Object> imgInfoMap = ImgInfoEnum.getImgInfoMap();
        model.addAttribute("supplementaryMaterials", imgInfoMap);
        model.addAttribute("accidentType", StringUtils.isNotBlank(claimCaseObject.getAccidentType()) ? claimCaseObject.getAccidentType() : "");
        model.addAttribute("accidentLiability", StringUtils.isNotBlank(claimCaseObject.getAccidentLiability()) ? claimCaseObject.getAccidentLiability() : "");
        model.addAttribute("accidentProportion", claimCaseObject.getAccidentProportion() != null ? claimCaseObject.getAccidentProportion().toString() : "");

        return "claimCaseObject/editPretreatmentCaseObject";
    }

    @RequestMapping(value = {"exportCopyOfLossAssessment"})
    @ResponseBody
    public String exportLegalConveyancing(String id, String claimCaseId, HttpServletRequest request, HttpServletResponse response, Model model) {
        if (StringUtils.isBlank(id) || StringUtils.isBlank(claimCaseId)) {
            return JsonBizTool.genJson(new HashMap<String, Object>() {{
                put("msg", "传递参数异常");
            }});
        }

        String userName = ShiroSessionUtil.getLoginSession().getRealName();

        //1分钟之内导出副本只能操作一次
        if (RedisTool3.get(userName + "_" + claimCaseId) != null) {
            return JsonBizTool.genJson(new HashMap<String, Object>() {{
                put("msg", "您导出操作太过频繁,请一分钟以后再试");
            }});
        }

        RedisTool3.set(userName + "_" + claimCaseId, id, "NX", "EX", 60);

        try {

            ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
            ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseId);
            PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());


            List<ClaimCaseObjectAssessment> objectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());

            Map<String, String> map = new HashMap<>();
            map.put("claimCaseNo", claimCaseObject.getClaimCaseNo());
            map.put("treatName", claimCaseObject.getTreatName());
            map.put("auditRemark", claimCaseObject.getRemark() == null ? "" : claimCaseObject.getRemark());
            map.put("customerPolicyNo", claimCase.getCustomerPolicyNo());
            String lossAdvice = "";
            String userDesc = "";
            //区分雇主众包
            if ("YW".equals(claimCase.getCaseType())){
                //众包
                lossAdvice = policyPerson.getName();
                userDesc = "骑手本人";
            } else if ("GZ".equals(claimCase.getCaseType())) {
                lossAdvice = policyPerson.getSubsidiaryAgentName();
                userDesc = "承保单位";
            }
            map.put("lossAdvice",lossAdvice);
            map.put("userDesc",userDesc);


            List<ClaimCaseObjectAssessment> finalObjectAssessmentList = objectAssessmentList.stream().filter(Objects::nonNull).collect(Collectors.toList());

            //定义项目中名称的简写
            Map<String, String> ProjectTypeMap = new HashMap<>();
            ProjectTypeMap.put("狂犬疫苗", "kqym");
            ProjectTypeMap.put("住院", "zy");
            ProjectTypeMap.put("门诊", "mz");
            ProjectTypeMap.put("衣物损", "yws");
            ProjectTypeMap.put("精神损失费", "js");
            ProjectTypeMap.put("住宿费", "zsf");
            ProjectTypeMap.put("交通费", "jtf");
            ProjectTypeMap.put("被扶养人生活费", "fy");
            ProjectTypeMap.put("死亡赔偿金", "sw");
            ProjectTypeMap.put("残疾用具", "cjyj");
            ProjectTypeMap.put("残疾赔偿金", "cj");
            ProjectTypeMap.put("误工费", "wg");
            ProjectTypeMap.put("丧葬费", "sz");
            ProjectTypeMap.put("护理费", "hl");
            ProjectTypeMap.put("住院伙食费", "zyhsf");
            ProjectTypeMap.put("营养费", "yyf");
            ProjectTypeMap.put("后续治疗费", "hxzlf");
            ProjectTypeMap.put("医疗费自费部分", "zf");
            //2024-11-13：ylf拆分成ylfzy和ylfmz,同时新增qt
            ProjectTypeMap.put("住院医疗费", "ylfzy");
            ProjectTypeMap.put("门诊医疗费", "ylfmz");
            ProjectTypeMap.put("其他", "qt");

            StringBuilder swAppeal = new StringBuilder();
            StringBuilder swRemark = new StringBuilder();
            StringBuilder swLossAssessment = new StringBuilder();
            StringBuilder cjAppeal = new StringBuilder();
            StringBuilder cjRemark = new StringBuilder();
            StringBuilder cjLossAssessment = new StringBuilder();

            StringBuilder jtAppeal = new StringBuilder();
            StringBuilder jtRemark = new StringBuilder();
            StringBuilder jtcLossAssessment = new StringBuilder();
            StringBuilder zsAppeal = new StringBuilder();
            StringBuilder zsRemark = new StringBuilder();
            StringBuilder zscLossAssessment = new StringBuilder();

            //
            for (ClaimCaseObjectAssessment claimCaseObjectAssessment : finalObjectAssessmentList) {
                String name = claimCaseObjectAssessment.getName();
                String typeName = ProjectTypeMap.getOrDefault(name, null);
                if (typeName != null) {
                    String appealKey = typeName + "Appeal";
                    String RemarkKey = typeName + "Remark";
                    String LossAssessmentKey = typeName + "LossAssessment";
                    if (typeName.equals("sw") || typeName.equals("cj")) {
                        if (typeName.equals("sw")) {
                            swAppeal.append(claimCaseObjectAssessment.getAppeal());
                            swRemark.append(claimCaseObjectAssessment.getRemark());
                            swLossAssessment.append(claimCaseObjectAssessment.getLossAssessment());
                        } else {
                            cjAppeal.append(claimCaseObjectAssessment.getAppeal());
                            cjRemark.append(claimCaseObjectAssessment.getRemark());
                            cjLossAssessment.append(claimCaseObjectAssessment.getLossAssessment());
                        }
                        continue;
                    }

                    if (typeName.equals("jtf") || typeName.equals("zsf")) {
                        if (typeName.equals("jtf")) {
                            jtAppeal.append(claimCaseObjectAssessment.getAppeal());
                            jtRemark.append(claimCaseObjectAssessment.getRemark());
                            jtcLossAssessment.append(claimCaseObjectAssessment.getLossAssessment());
                        } else {
                            zsAppeal.append(claimCaseObjectAssessment.getAppeal());
                            zsRemark.append(claimCaseObjectAssessment.getRemark());
                            zscLossAssessment.append(claimCaseObjectAssessment.getLossAssessment());
                        }

                        continue;
                    }

                    map.put(appealKey, claimCaseObjectAssessment.getAppeal().toString());
                    map.put(RemarkKey, claimCaseObjectAssessment.getRemark());
                    map.put(LossAssessmentKey, claimCaseObjectAssessment.getLossAssessment().toString());

                }

            }

            if (finalObjectAssessmentList.size() > 0) {
//                if (cjAppeal.length() == 0) {
                    map.put("swAppeal", swAppeal.toString());
                    map.put("swRemark", swRemark.toString());
                    map.put("swLossAssessment", swLossAssessment.toString());
//                } else {
                    map.put("scAppeal", cjAppeal.toString());
                    map.put("scRemark", cjRemark.toString());
                    map.put("scLossAssessment", cjLossAssessment.toString());
//                }

                if (zsAppeal.length() == 0) {
                    map.put("jtzsappeal", jtAppeal.append(zsAppeal).toString());
                    map.put("jtzsremark", jtRemark.append(zsRemark).toString());
                    map.put("jtzsLossAssessment", jtcLossAssessment.append(zscLossAssessment).toString());
                } else {
                    map.put("jtzsappeal",mergeStrings(jtAppeal,zsAppeal));
                    map.put("jtzsremark",mergeStrings(jtRemark,zsRemark));
                    map.put("jtzsLossAssessment",mergeStrings(jtcLossAssessment,zscLossAssessment));
                }
            }


            //计算小计金额
            String rslpPattern = ".*(swAppeal|scAppeal|cjyjAppeal|fyAppeal|szAppeal|wgAppeal|jtzsappeal).*";
            //2024-11-13：新增正则表达式(ylf拆分成ylfzy和ylfmz)
            String yllpPattern = ".*(ylfzyAppeal|ylfmzAppeal|hlAppeal|zyhsfAppeal|yyfAppeal|hxzlfAppeal|jsAppeal).*";
            String qtlpPattern = ".*(qtAppeal).*";
            BigDecimal rslpSubtotal = calculateWordSubtotal(rslpPattern, map);
            BigDecimal yllpSubtotal = calculateWordSubtotal(yllpPattern, map);
            BigDecimal qtlpSubtotal = calculateWordSubtotal(qtlpPattern, map);

            //计算定损金额和小计

            String rsdsPattern = ".*(swLossAssessment|scLossAssessment|cjyjLossAssessment|fyLossAssessment|szLossAssessment|wgLossAssessment|jtzsLossAssessment).*";
            //2024-11-13：新增正则表达式(ylf拆分成ylfzy和ylfmz)
            String yldsPattern = ".*(ylfzyLossAssessment|ylfmzLossAssessment|hlLossAssessment|zyhsfLossAssessment|yyfLossAssessment|hxzlfLossAssessment|jsLossAssessment).*";
            String qtdsPattern = ".*(qtLossAssessment).*";
            BigDecimal rsdsSubtotal = calculateWordSubtotal(rsdsPattern, map);
            BigDecimal yldsSubtotal = calculateWordSubtotal(yldsPattern, map);
            BigDecimal qtdsSubtotal = calculateWordSubtotal(qtdsPattern, map);

            BigDecimal lphjSubtotal = BigDecimal.ZERO;
            BigDecimal dshjSubtotal = BigDecimal.ZERO;

            if (rslpSubtotal != null && rslpSubtotal.longValue() != 0) {
//                map.put("rslpSubtotal", rslpSubtotal.toString());
                lphjSubtotal = lphjSubtotal.add(rslpSubtotal);
            }
            if (yllpSubtotal != null && yllpSubtotal.longValue() != 0) {
//                map.put("yllpSubtotal", yllpSubtotal.toString());
                lphjSubtotal = lphjSubtotal.add(yllpSubtotal);
            }
            if(qtlpSubtotal != null && qtlpSubtotal.longValue() != 0) {
                lphjSubtotal = lphjSubtotal.add(qtlpSubtotal);
            }

            if (rsdsSubtotal != null && rsdsSubtotal.longValue() != 0) {
//                map.put("rsdsSubtotal", rsdsSubtotal.toString());
                dshjSubtotal = dshjSubtotal.add(rsdsSubtotal);
            }
            if (yldsSubtotal != null && yldsSubtotal.longValue() != 0) {
//                map.put("yldsSubtotal", yldsSubtotal.toString());
                dshjSubtotal = dshjSubtotal.add(yldsSubtotal);
            }
            if (qtdsSubtotal != null && qtdsSubtotal.longValue() != 0) {
                dshjSubtotal = dshjSubtotal.add(qtdsSubtotal);
            }

            //理赔合计
            if (lphjSubtotal != null && lphjSubtotal.longValue() != 0) {
                map.put("lphjSubtotal", lphjSubtotal.toString());
            }

            //定损合计
            if (dshjSubtotal != null && dshjSubtotal.longValue() != 0) {
                map.put("dshjSubtotal", dshjSubtotal.toString());
            }

            //导出
            FileInputStream templateFile = new FileInputStream(getResourceUri("/files/CopyOfLossAssessment.docx"));
            XWPFDocument document = new XWPFDocument(templateFile);
            this.replaceTextInTables(document, map);
            this.replaceTextInParagragh(document, map);
            OutputStream fileOutputStream = response.getOutputStream();
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            String downLoadName = new String(("定损单.docx").getBytes("utf-8"), "iso8859-1");
            response.setHeader("Content-Disposition", "attachment;filename=" + downLoadName);
            document.write(fileOutputStream);
            fileOutputStream.close();

            // 记录日志 t_claim_case_object_log
            String userId = ShiroSessionUtil.getLoginSession().getId();
            String username = ShiroSessionUtil.getLoginSession().getRealName();
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseId(claimCaseId);
            claimCaseObjectLog.setClaimCaseObjectId(id);
            claimCaseObjectLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
            claimCaseObjectLog.setCreator(username + "-" + userId);
            claimCaseObjectLog.setCreateTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(username + "导出了我的任务-开始任务中的副本定损单");
            claimCaseObjectLogService.insertSelective(claimCaseObjectLog);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return JsonBizTool.genJson(new HashMap<String, Object>() {{
            put("msg", "导出成功");
        }});

    }

    private String mergeStrings(StringBuilder existing, StringBuilder toAppend) {
        return (existing.length() > 0) ? existing.append("、").append(toAppend).toString() : toAppend.toString();
    }

    //定损单小计计算
    private BigDecimal calculateWordSubtotal(String pattern, Map<String, String> map) {
        Pattern r = Pattern.compile(pattern);
        BigDecimal Subtotal = BigDecimal.ZERO;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (r.matcher(entry.getKey()).matches()) {
                try {
                    if (!entry.getValue().equals("") && entry.getValue() != null) {
                        if (entry.getValue().contains("、")) {
                            String[] split = entry.getValue().split("、");
                            for (String str : split) {
                                BigDecimal value = new BigDecimal(str);
                                Subtotal = Subtotal.add(value);
                            }
                        } else {
                            BigDecimal value = new BigDecimal(entry.getValue());
                            Subtotal = Subtotal.add(value);
                        }
                    }
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
            }
        }
        return Subtotal;
    }


    private String getResourceUri(String resource) {
        return ClaimCaseObjectController.class.getResource(resource).getPath();
    }

    private void replaceTextInTables(XWPFDocument document, Map<String, String> textMap) {
        //获取表格对象集合
        List<XWPFTable> tables = document.getTables();
        //只会有一个表格对象，所以取第一个表格对象
        List<XWPFTableRow> rows = tables.get(0).getRows();
        //遍历表格,并替换模板
        eachTable(rows, textMap);
    }

    /**
     * 遍历表格
     *
     * @param rows    表格行对象
     * @param textMap 需要替换的信息集合
     */
    private void eachTable(List<XWPFTableRow> rows, Map<String, String> textMap) {
        for (XWPFTableRow row : rows) {
            //得到表格每一行的所有表格
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                //判断单元格是否需要替换
                if (checkText(cell.getText())) {
                    List<XWPFParagraph> paragraphs = cell.getParagraphs();
                    for (XWPFParagraph paragraph : paragraphs) {
                        List<XWPFRun> runs = paragraph.getRuns();
                        for (XWPFRun run : runs) {
                            run.setText(changeValue(run.toString(), textMap), 0);
                        }
                    }
                }
            }
        }
    }


    /**
     * 判断文本中时候包含$
     *
     * @param text 文本
     * @return 包含返回true, 不包含返回false
     */
    private boolean checkText(String text) {
        boolean check = false;
        if (text.indexOf("$") != -1) {
            check = true;
        }
        return check;

    }

    /**
     * 匹配传入信息集合与模板
     *
     * @param value   模板需要替换的区域
     * @param textMap 传入信息集合
     * @return 模板需要替换区域信息集合对应值
     */
    private String changeValue(String value, Map<String, String> textMap) {
        Set<Map.Entry<String, String>> textSets = textMap.entrySet();
        for (Map.Entry<String, String> textSet : textSets) {
            //匹配模板与替换值 格式${key}
            String key = "${" + textSet.getKey() + "}";
            if (value.indexOf(key) != -1) {
                value = textSet.getValue();
            }
        }
        //模板未匹配到区域替换为空
        if (checkText(value)) {
            value = "";
        }
        return value;
    }

    /**
     * 替换段落中的占位符
     */
    private void replaceTextInParagragh(XWPFDocument document, Map<String, String> dataMap) {
        //获取整个Word所有段落：包含页眉或页脚文本的段落
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        //循环
        for (XWPFParagraph paragragh : paragraphs) {
            //获取一段的所有本文
            List<XWPFRun> runs = paragragh.getRuns();
            //获取段落内容：paragragh.getText();
            //循环
            for (int i = 0; i < runs.size(); i++) {
                //XWPFRun--代表具有相同属性的一段文本
                XWPFRun xwpfRun = runs.get(i);
                //获取文本中的内容
                String paraString = xwpfRun.getText(xwpfRun.getTextPosition());
                if (paraString != null) {
                    //替换文字
                    paraString = this.replaceText(paraString, dataMap);
                    //设置替换后的段落
                    xwpfRun.setText(paraString, 0);
                }
            }
        }
    }

    /**
     * 替换文字
     */
    private String replaceText(String text, Map<String, String> dataMap) {
        String paraString = text;
        //遍历map,将段落里面的${}替换成map里的value
        Iterator<Map.Entry<String, String>> iterator = dataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            String key = entry.getKey();
            String value = entry.getValue();
            //组装map里的key为${key}
            StringBuffer sb = new StringBuffer();
            String placeHolder = sb.append("${").append(key).append("}").toString();
            //替换:将"${as}dasdas" --> value+dasdas
            paraString = paraString.replace(placeHolder, value);
        }
        return paraString;
    }

    /**
     * 分配人员首字母排序
     * 、
     */
    private Map<String, String> letterSorting(Map<String, String> map){

        Map<String, String> resultmap = new LinkedHashMap<>();

        if(map.size() == 0 && map.isEmpty()) {
            return resultmap;
        }

        Comparator<Map.Entry<String, String>> valueComparator = new Comparator<Map.Entry<String,String>>() {
            @Override
            public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
                String key1 = PinyinUtil.getInitial(o1.getValue());
                String key2 = PinyinUtil.getInitial(o2.getValue());
                return key1.compareTo(key2);
            }
        };
        List<Map.Entry<String, String>> list = new ArrayList<Map.Entry<String,String>>(map.entrySet());
        Collections.sort(list,valueComparator);

        for (Map.Entry<String, String> entry : list) {
            resultmap.put(entry.getKey(),entry.getValue());
        }

        return resultmap;
    }


    /**
     * 开始签字任务，根据赔付对象id，获取其对应的签字配置信息
     * @param claimCaseObjectId
     * @param request
     * @param model
     * @return
     */
    @RequestMapping("startSignTask")
    public String startSignTask(String claimCaseObjectId, HttpServletRequest request, Model model) {
        String userId = ShiroSessionUtil.getLoginSession().getId();
        String userName = ShiroSessionUtil.getLoginSession().getRealName();
        if (StringUtils.isBlank(claimCaseObjectId)){
            model.addAttribute("error","估损单信息缺少，请重新登录后重新发起签字任务！");
            return "claimCaseObject/startSignTask";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);
        if(claimCaseObject == null){
            model.addAttribute("error","估损单缺少，请重新登录后重新发起签字任务！");
            return "claimCaseObject/startSignTask";
        }
        if(!claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX38.getCode()) && !claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX41.getCode())){
            model.addAttribute("error","当前估损单状态发生变更，请刷新后重试！");
            return "claimCaseObject/startSignTask";
        }
        if(claimCaseObject.getIsCaseClosed()==1){
            model.addAttribute("error","当前估损单已关闭，请刷新后重试！");
            return "claimCaseObject/startSignTask";
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.clear();
        paramMap.put("baseUserId", claimCase.getBaseUserId());
        List<ClaimCase> claimCaseList = claimCaseService.findByTreatParam(paramMap);
        model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(claimCaseList) ? claimCaseList.size() : 0);

        model.addAttribute("claimCase", claimCase);
        model.addAttribute("claimCaseObjectId", claimCaseObjectId);

        if(claimCase == null){
            model.addAttribute("error","估损单对应案件缺少，请联系管理员！");
            return "claimCaseObject/startSignTask";
        }


        paramMap.clear();
        paramMap.put("insCode",claimCase.getInsCode());
        paramMap.put("claimType", claimCase.getCaseType());
        paramMap.put("type",claimCaseObject.getType());
        paramMap.put("category",claimCaseObject.getCategory());
        paramMap.put("status","1");

        List<SignTemplateConfig> findByParam = signTemplateConfigService.findByParamMap(paramMap);
        if(CollectionUtils.isEmpty(findByParam)){
            model.addAttribute("error","未查询到相关签字配置");
            return "claimCaseObject/startSignTask";
        }

        Map<String,Map<String,Object>> signTemplateShow = new LinkedHashMap<>();
        for (SignTemplateConfig signTemplateConfig : findByParam) {
            Map<String, Object> resultMap = signTemplateInit(signTemplateConfig.getSignTemplateId(), claimCase,claimCaseObject);
            signTemplateShow.put(signTemplateConfig.getSignTemplateId(),resultMap);
        }

        model.addAttribute("signTemplateShow",signTemplateShow);
        model.addAttribute("isCanPush","true");


        ClaimCaseLog claimCaseLog = new ClaimCaseLog();
        claimCaseLog.setId(Tool.uuid());
        claimCaseLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
        claimCaseLog.setClaimCaseObjectId(claimCaseObject.getId());
        claimCaseLog.setLevel(3);
        claimCaseLog.setSecurityClassification(100);
        claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
        claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
        claimCaseLog.setStatus(claimCaseObject.getStatus());
        claimCaseLog.setDescription("开始签字任务");
        claimCaseLog.setCreator(userName + "-" + userId);
        claimCaseLog.setCreateTime(new Date());

        claimCaseLogService.insertSelective(claimCaseLog);

        return "claimCaseObject/startSignTask";
    }


    /**
     * 初始化签字模板信息，如果模板有对应的初始化查询逻辑，则进行数据初始化；反之则空值返回到页面上面
     * @param templateId 签字模板id
     * @param claimCase 案件数据
     * @param claimCaseObject 赔付对象数据
     * @return 组装好的签字模板数据
     */
    private Map<String,Object> signTemplateInit(String templateId,ClaimCase claimCase,ClaimCaseObject claimCaseObject){
        String templateCode = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "SIGN_TEMPLATE_MAPPING", templateId);
        if(templateCode == null){
            templateCode = "";
        }
        /**
         * 1.利用redis配置，设置系统中的签字模板是否对应了，以下的某个查询逻辑
         * 2.将查询逻辑中得到的map数据和签字模板配置的json数据一一对应，实现数据初始化，如果没有数据就空值回显到页面上面
         */
        SignTemplate signTemplate = signTemplateService.selectByPrimaryKey(templateId);
        PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());
        String insuranceCompany = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "INSURANCE_COMPANY_NAME", claimCase.getInsCode());
        Map<String,Object> resultMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        switch (templateCode){
            case "CXTZS":
                //出险通知书
            {

                if (policyPerson != null) {
                    map.put("subsidiaryAgentName", policyPerson.getSubsidiaryAgentName());
                    map.put("policyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 设置日期格式
                String treatDateStr = sdf.format(claimCase.getTreatDate());

                map.put("treatDate", treatDateStr);
                String address = claimCase.getProvince()+"-"+claimCase.getCity()+"-"+claimCase.getDistrict()+claimCase.getAddress();
                map.put("address",address);
                sdf = new SimpleDateFormat("yyyy年MM月dd日"); // 设置日期格式
                String accidentTimeStr = sdf.format(claimCase.getTreatDate());
                map.put("accidentTime", accidentTimeStr);
                map.put("applyIdNum", claimCase.getApplyIdNum()==null ? "" : claimCase.getApplyIdNum());
                map.put("applyMobile", Optional.ofNullable(claimCase.getApplyMobile()).orElse(""));
                map.put("insuranceCompany",Optional.ofNullable(insuranceCompany).orElse(""));
                map.put("treatName",Optional.ofNullable(claimCase.getTreatName()).orElse(""));

                //银行信息
                List<ClaimCaseObjectPayment> byClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                if(CollectionUtils.isNotEmpty(byClaimCaseObjectId)){
                    StringBuffer bankInfo = new StringBuffer();
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byClaimCaseObjectId){
                        bankInfo.append("账户名称："+claimCaseObjectPayment1.getBankAccount()+"\n");
                        bankInfo.append("开户银行："+claimCaseObjectPayment1.getBankName()+"\n");
                        bankInfo.append("银行账户："+claimCaseObjectPayment1.getBankCard()+"\n");
                    }
                    map.put("bankInfo",bankInfo.toString());
                }

                //理算金额
                map.put("verifyAmout",claimCaseObject.getVerifyAmout().toString());
                //委托日期
                String commissionTimeStr= sdf.format(new Date());
                map.put("commissionTimeStr",commissionTimeStr);


                //初始化签字人
                PersonInfo personInfo = new PersonInfo();
                personInfo.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo.setPersonPhone(claimCase.getTreatMobile());
                List<PersonInfo> personInfoList = new ArrayList<>();
                personInfoList.add(personInfo);

                resultMap.put("personInfo",personInfoList);
            }
            break;
            case "QYZR":
                //企业转让
            {
                if (policyPerson != null) {
                    map.put("policyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }

                map.put("insuranceCompany", Optional.ofNullable(insuranceCompany).orElse(""));
                map.put("treatName", Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                map.put("claimCaseNo", Optional.ofNullable(claimCase.getClaimCaseNo()).orElse(""));
                String address = claimCase.getProvince()+"-"+claimCase.getCity()+"-"+claimCase.getDistrict()+claimCase.getAddress();
                map.put("description",DateUtils.format(claimCase.getTreatDate(),"yyyy年MM月dd日")+"在"+address);
                //估损金额
                map.put("convertUpVerifyAmout", ConvertUpMoneyTool.toChinese(claimCaseObject.getVerifyAmout().toString()));

                //银行信息
                List<ClaimCaseObjectPayment> byClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                if(CollectionUtils.isNotEmpty(byClaimCaseObjectId)){
                    StringBuffer bankInfo = new StringBuffer();
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byClaimCaseObjectId){
                        bankInfo.append("账户名称："+claimCaseObjectPayment1.getBankAccount()+"\n");
                        bankInfo.append("开户银行："+claimCaseObjectPayment1.getBankName()+"\n");
                        bankInfo.append("银行账户："+claimCaseObjectPayment1.getBankCard()+"\n");
                    }
                    map.put("bankInfo",bankInfo.toString());
                }
                PersonInfo personInfo = new PersonInfo();
                personInfo.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo.setPersonPhone(claimCase.getTreatMobile());
                List<PersonInfo> personInfoList = new ArrayList<>();
                personInfoList.add(personInfo);
                resultMap.put("personInfo",personInfoList);
            }
            break;
            case "TJXYS":
                //调解协议书
                //骑手三者人伤事故赔偿调解协议书，只兼容一个三者人伤的赔付
            {
                String IDNumber = claimCase.getTreatIdNum();
                map.put("insuranceCompany", insuranceCompany);
                map.put("name", claimCase.getTreatName());
                try {
                    int gender = IdCardTool2.getGender(IDNumber);
                    map.put("gender", gender == 0 ? "女" : "男");
                }catch (Exception e){

                }

                try {
                    int age = IdCardTool2.getAge(IDNumber);
                    map.put("age", age+"");
                }catch (Exception e){

                }
                map.put("IDNumber", IDNumber);
                map.put("mobile", claimCase.getTreatMobile());

                //三者证件号码
                String thirdIDNumber = claimCaseObject.getTreatIdNum();
                map.put("thirdName", claimCaseObject.getTreatName());
                try {
                    int thirdGender = IdCardTool2.getGender(thirdIDNumber);
                    map.put("thirdGender", thirdGender == 0 ? "女" : "男");
                }catch (Exception e){

                }

                try {
                    int thirdAge = IdCardTool2.getAge(thirdIDNumber);
                    map.put("thirdAge", thirdAge+"");
                }catch (Exception e){

                }
                map.put("thirdIDNumber", thirdIDNumber);
                map.put("thirdMobile", claimCaseObject.getMobile());

                //事故简要经过
                StringBuffer description = new StringBuffer();
                String address = claimCase.getProvince()+"-"+claimCase.getCity()+"-"+claimCase.getDistrict()+claimCase.getAddress();
                description.append("    事故当事人"+claimCase.getTreatName()+"于"+DateUtils.format(claimCase.getTreatDate(),"yyyy年MM月dd日")+"在"+address +
                        "发生交通事故，该事故经交警队第        号道路交通事故认定书认定甲方负"+claimCase.getAccidentLiability()+"责任，乙方"+claimCaseObject.getAccidentLiability()+
                        "责任");
                map.put("description",description.toString());

                //基本协议
                BigDecimal verifyAmout = claimCaseObject.getVerifyAmout();
                StringBuffer baseAgreement = new StringBuffer();
                baseAgreement.append("1、当事人承诺本案所提供材料在其他任何途径未行报销，如有会提供如结算单等材料辅证，并承担相应的法律后果。\n");
                baseAgreement.append("2、甲方赔偿乙方医药费、护理费、营养费、误工费等合计"+verifyAmout.toString()+"元。\n");
                baseAgreement.append("3、甲方赔偿乙方车辆损失"+"元。\n");
                baseAgreement.append("4、上述赔款合计"+verifyAmout.toString()+"元，扣除责任比例后计"+verifyAmout.toString()+"元。\n");
                baseAgreement.append("5、今后双方无涉。\n");
                map.put("baseAgreement",baseAgreement.toString());


                //赔款支付方式
                StringBuffer payWay = new StringBuffer();
                List<ClaimCaseObjectPayment> byClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                String bankAccount1 = "";
                String bankName1 = "";
                String bankCard1 = "";
                String approveMoney1 = "";

                if(CollectionUtils.isNotEmpty(byClaimCaseObjectId)){
                    Integer index=1;
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byClaimCaseObjectId){
                        bankAccount1 = claimCaseObjectPayment1.getBankAccount();
                        bankName1 = claimCaseObjectPayment1.getBankName();
                        bankCard1 = claimCaseObjectPayment1.getBankCard();
                        approveMoney1 = claimCaseObjectPayment1.getPayAmount().toString();
                        payWay.append(index+". 被保险人向保险公司申请，将赔"+approveMoney1+"元，即（大写："+(StringUtils.isNotBlank(approveMoney1)?ConvertUpMoneyTool.toChinese(approveMoney1):"")+
                                "）直接划付"+bankAccount1+"，开户名称："+bankAccount1+"；开户银行："+bankName1+"；银行账号："+bankCard1+"。");
                        index++;
                    }
                }

                map.put("payWay",payWay.toString());

                //签署人分别为出险人、三者和处理岗人员
                //第一个是出险人信息
                PersonInfo personInfo = new PersonInfo();
                personInfo.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo.setPersonPhone(claimCase.getTreatMobile());
                List<PersonInfo> personInfoList = new ArrayList<>();
                personInfoList.add(personInfo);

                //第二个是三者信息
                PersonInfo personInfoThird = new PersonInfo();
                personInfoThird.setPersonPhone(claimCaseObject.getMobile());
                personInfoThird.setPersonIdType(claimCaseObject.getTreatIdType());
                personInfoThird.setPersonIdNumber(claimCaseObject.getTreatIdNum());
                personInfoThird.setPersonName(claimCaseObject.getTreatName());
                personInfoList.add(personInfoThird);

                //第三个签署人是处理岗
                PersonInfo personInfo2 = new PersonInfo();
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getAuditer());
                if(manager!=null){
                    personInfo2.setPersonName(Optional.ofNullable(manager.geteSingnName()).orElse(""));
                    personInfo2.setPersonPhone(StringUtils.isBlank(manager.getMobile())?manager.getTelephone():manager.getMobile());
                }
                personInfoList.add(personInfo2);
                resultMap.put("personInfo",personInfoList);

            }
            break;
            case "KSCLXYS":
                //快速处理协议书
            {
                if (policyPerson != null) {

                    map.put("policyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));

                }
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); // 设置日期格式
                String treatDateStr = sdf.format(claimCase.getTreatDate());
                map.put("insuranceCompany", Optional.ofNullable(insuranceCompany).orElse(""));
                map.put("name", Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                map.put("claimCaseNo", Optional.ofNullable(claimCase.getClaimCaseNo()).orElse(""));
                map.put("treatTime", treatDateStr);
                map.put("mobile", Optional.ofNullable(claimCase.getTreatMobile()).orElse(""));
                map.put("IDNumber", Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                String address = claimCase.getProvince()+"-"+claimCase.getCity()+"-"+claimCase.getDistrict()+claimCase.getAddress();
                map.put("address",address);

                //事故经过及伤情描述及责任划分=事故经过+事故责任
                String description = claimCase.getDescription();
                String accidentLiability = claimCase.getAccidentLiability();
                map.put("description","事故经过及伤情描述及责任划分：\n"+description+"\n"+accidentLiability);

                //defaultData  基本协议
                /*经事故各方自行协商，自愿达成如下协议：
                1、乙方就甲方本次受伤， 一次性向甲方赔偿    元 ，人民币大写：         。其中包括 医疗费、误工费、护理费、伤残等
                2、本次事故甲方受伤相关赔偿结束，各方均无异议，签字确认生效后生效，今后无涉。
                与各方均无关系。
                3、乙方依据本协议，在保险责任范围内赔偿甲方相应费用。
                4、最终解释权归保司*/

                //损失项目
                List<ClaimCaseObjectAssessment> byClaimCaseObjectId = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
                List<String> assessmentCollect = byClaimCaseObjectId.stream().map(ClaimCaseObjectAssessment::getName).collect(Collectors.toList());


                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("经事故各方自行协商，自愿达成如下协议：\n");
                stringBuffer.append("1、当事人承诺本案所提供材料在其他任何途径未行报销，如有会提供如结算单等材料辅证，并承担相应的法律后果。\n");
                stringBuffer.append("2、乙方就甲方本次受伤， 一次性向甲方赔偿 "+claimCaseObject.getVerifyAmout().toString()+" 元 ，人民币大写："+ConvertUpMoneyTool.toChinese(claimCaseObject.getVerifyAmout().toString())+"。");
                stringBuffer.append("其中包括 "+String.join("、",assessmentCollect)+"等\n" );
                stringBuffer.append("3、本次事故甲方受伤相关赔偿结束，各方均无异议，签字确认生效后生效，今后无涉。\n与各方均无关系。\n");
                stringBuffer.append("4、乙方依据本协议，在保险责任范围内赔偿甲方相应费用。本保险金承担医药费的首次赔付责任，被保险人或受益人需承诺未在本保险意外的其他渠道(包括但不限于其他保险公司、社会医宁保险等)存在二次赔付行为。如发现被保险人或受益人存在重复理赔行为，我司有权采职相应措施。\n");
                stringBuffer.append("5、最终解释权归保司\n");

                map.put("defaultData",stringBuffer.toString());

                //第一个签署人是出险人
                List<PersonInfo> personInfoList = new ArrayList<>();
                PersonInfo personInfo = new PersonInfo();
                personInfo.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo.setPersonPhone(claimCase.getTreatMobile());

                personInfoList.add(personInfo);

                //第二个签署人是处理岗
                PersonInfo personInfo2 = new PersonInfo();
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getAuditer());
                if(manager!=null){
                    personInfo2.setPersonName(Optional.ofNullable(manager.geteSingnName()).orElse(""));
                    personInfo2.setPersonPhone(StringUtils.isBlank(manager.getMobile())?manager.getTelephone():manager.getMobile());
                }
                personInfoList.add(personInfo2);


                resultMap.put("personInfo",personInfoList);

            }
            break;
            case "PFYXJQYZRS":
                //赔付意向及权益转让书
            {
                if (policyPerson != null) {
                    String name = claimCase.getTreatName();
                    if ("GZ".equals(claimCase.getCaseType())){
                        name = policyPerson.getSubsidiaryAgentName();
                    }
                    map.put("name", name);
                    map.put("customerPolicyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }
                Date date = claimCase.getTreatDate();
                map.put("timeStr",DateUtils.format(date,"yyyy年MM月dd日 HH:mm:ss"));


                //大写理算金额
                map.put("convertUpVerifyAmout",ConvertUpMoneyTool.toChinese(claimCaseObject.getVerifyAmout().toString()));

                //赔付项目
                List<ClaimCaseObjectAssessment> byClaimCaseObjectId = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
                List<String> assessmentCollect = byClaimCaseObjectId.stream().map(ClaimCaseObjectAssessment::getName).collect(Collectors.toList());
                map.put("assessmentCollect",String.join("、",assessmentCollect));

                //银行信息
                List<ClaimCaseObjectPayment> byPayClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                if(CollectionUtils.isNotEmpty(byPayClaimCaseObjectId)){
                    ClaimCaseObjectPayment claimCaseObjectPayment = byPayClaimCaseObjectId.get(0);
                    map.put("bankAccount",claimCaseObjectPayment.getBankAccount());
                    map.put("bankName",claimCaseObjectPayment.getBankName());
                    map.put("bankCard",claimCaseObjectPayment.getBankCard());
                }

                //签字人
                List<PersonInfo> personInfoList = new ArrayList<>();
                PersonInfo personInfo = new PersonInfo();
                personInfo.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo.setPersonPhone(claimCase.getTreatMobile());
                personInfoList.add(personInfo);
                personInfoList.add(personInfo);
                /*PersonInfo personInfo1 = new PersonInfo();
                BeanUtils.copyProperties(personInfo,personInfo1);
                personInfoList.add(personInfo1);*/

                resultMap.put("personInfo",personInfoList);

                String enterprise = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX+"EXPORT_INSCODE_ENTERPRISE", claimCase.getInsCode());
                map.put("enterprise", Optional.ofNullable(enterprise).orElse(""));
                map.put("productName",Optional.ofNullable(claimCase.getProductName()).orElse(""));
                map.put("caseNo", Optional.ofNullable(claimCase.getClaimCaseNo()).orElse(""));


            }
            break;
            case "PFYXYJQYZRS":
                //赔付意向及权益转让书
            {
                if (policyPerson != null) {
                    String name = claimCase.getTreatName();
                    if ("GZ".equals(claimCase.getCaseType())){
                        name = policyPerson.getSubsidiaryAgentName();
                    }
                    map.put("name", name);
                    map.put("customerPolicyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }
                Date date = claimCase.getTreatDate();
                map.put("timeStr",DateUtils.format(date,"yyyy年MM月dd日 HH:mm:ss"));


                //大写理算金额
                map.put("convertUpVerifyAmout",ConvertUpMoneyTool.toChinese(claimCaseObject.getVerifyAmout().toString()));

                //赔付项目
                List<ClaimCaseObjectAssessment> byClaimCaseObjectId = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
                List<String> assessmentCollect = byClaimCaseObjectId.stream().map(ClaimCaseObjectAssessment::getName).collect(Collectors.toList());
                map.put("assessmentCollect",String.join("、",assessmentCollect));

                //银行信息
                List<ClaimCaseObjectPayment> byPayClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                if(CollectionUtils.isNotEmpty(byPayClaimCaseObjectId)){
                    StringBuffer bankInfo = new StringBuffer();
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byPayClaimCaseObjectId){
                        bankInfo.append("账户名称："+claimCaseObjectPayment1.getBankAccount()+"\n");
                        bankInfo.append("开户银行："+claimCaseObjectPayment1.getBankName()+"\n");
                        bankInfo.append("银行账户："+claimCaseObjectPayment1.getBankCard()+"\n");
                    }
                    map.put("bankInfo",bankInfo.toString());
                }

                //签字人
                List<PersonInfo> personInfoList = new ArrayList<>();
                PersonInfo personInfo = new PersonInfo();
                personInfo.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo.setPersonPhone(claimCase.getTreatMobile());
                personInfoList.add(personInfo);
                personInfoList.add(personInfo);
                /*PersonInfo personInfo1 = new PersonInfo();
                BeanUtils.copyProperties(personInfo,personInfo1);
                personInfoList.add(personInfo1);*/

                resultMap.put("personInfo",personInfoList);

                String enterprise = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX+"EXPORT_INSCODE_ENTERPRISE", claimCase.getInsCode());
                map.put("enterprise", Optional.ofNullable(enterprise).orElse(""));
                map.put("productName",Optional.ofNullable(claimCase.getProductName()).orElse(""));
                map.put("caseNo", Optional.ofNullable(claimCase.getClaimCaseNo()).orElse(""));

            }
            break;
            case "ZBQS":
                //众包骑手
            {
                //出险通知书
                if (policyPerson != null) {
                    map.put("subsidiaryAgentName", policyPerson.getSubsidiaryAgentName());
                    map.put("policyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 设置日期格式
                String treatDateStr = sdf.format(claimCase.getTreatDate());

                map.put("treatDate", treatDateStr);
                String address = claimCase.getProvince()+"-"+claimCase.getCity()+"-"+claimCase.getDistrict()+claimCase.getAddress();
                map.put("address",address);
                sdf = new SimpleDateFormat("yyyy年MM月dd日"); // 设置日期格式
                String accidentTimeStr = sdf.format(claimCase.getTreatDate());
                map.put("accidentTime", accidentTimeStr);
                map.put("applyIdNum", claimCase.getApplyIdNum()==null ? "" : claimCase.getApplyIdNum());
                map.put("applyMobile", Optional.ofNullable(claimCase.getApplyMobile()).orElse(""));
                map.put("insuranceCompany",Optional.ofNullable(insuranceCompany).orElse(""));
                map.put("treatName",Optional.ofNullable(claimCase.getTreatName()).orElse(""));

                //银行信息
                List<ClaimCaseObjectPayment> byClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                if(CollectionUtils.isNotEmpty(byClaimCaseObjectId)){
                    StringBuffer bankInfo = new StringBuffer();
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byClaimCaseObjectId){
                        bankInfo.append("账户名称："+claimCaseObjectPayment1.getBankAccount()+"\n");
                        bankInfo.append("开户银行："+claimCaseObjectPayment1.getBankName()+"\n");
                        bankInfo.append("银行账户："+claimCaseObjectPayment1.getBankCard()+"\n");
                    }
                    map.put("bankInfo",bankInfo.toString());
                }

                //理算金额
                map.put("verifyAmout",claimCaseObject.getVerifyAmout().toString());
                //委托日期
                String commissionTimeStr= sdf.format(new Date());
                map.put("commissionTimeStr",commissionTimeStr);


//                初始化签字人
                PersonInfo personInfo = new PersonInfo();
                personInfo.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo.setPersonPhone(claimCase.getTreatMobile());
                List<PersonInfo> personInfoList = new ArrayList<>();
                personInfoList.add(personInfo);
//
//                resultMap.put("personInfo",personInfoList);
                // 赔付意向及权益转让书
                if (policyPerson != null) {
                    String name = claimCase.getTreatName();
                    if ("GZ".equals(claimCase.getCaseType())){
                        name = policyPerson.getSubsidiaryAgentName();
                    }
                    map.put("name", name);
                    map.put("customerPolicyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }
                Date date = claimCase.getTreatDate();
                map.put("timeStr",DateUtils.format(date,"yyyy年MM月dd日 HH:mm:ss"));


                //大写理算金额
                map.put("convertUpVerifyAmout",ConvertUpMoneyTool.toChinese(claimCaseObject.getVerifyAmout().toString()));
                map.put("payVerifyAmout",claimCaseObject.getVerifyAmout().toString());
                map.put("convertUpPayAmout",ConvertUpMoneyTool.toChinese(claimCaseObject.getVerifyAmout().toString()));


                //银行信息
                List<ClaimCaseObjectPayment> byPayClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                if(CollectionUtils.isNotEmpty(byPayClaimCaseObjectId)){
                    StringBuffer bankInfo = new StringBuffer();
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byPayClaimCaseObjectId){
                        bankInfo.append("账户名称："+claimCaseObjectPayment1.getBankAccount()+"\n");
                        bankInfo.append("开户银行："+claimCaseObjectPayment1.getBankName()+"\n");
                        bankInfo.append("银行账户："+claimCaseObjectPayment1.getBankCard()+"\n");
                    }
                    map.put("bankInfoTwo",bankInfo.toString());
                }

                //签字人
//                List<PersonInfo> personInfoList = new ArrayList<>();
                PersonInfo personInfo2 = new PersonInfo();
                personInfo2.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo2.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo2.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo2.setPersonPhone(claimCase.getTreatMobile());
                personInfoList.add(personInfo2);
//                personInfoList.add(personInfo);
                /*PersonInfo personInfo1 = new PersonInfo();
                BeanUtils.copyProperties(personInfo,personInfo1);
                personInfoList.add(personInfo1);*/

//                resultMap.put("personInfo",personInfoList);

                String enterprise = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX+"EXPORT_INSCODE_ENTERPRISE", claimCase.getInsCode());
                map.put("enterprise", Optional.ofNullable(enterprise).orElse(""));
                map.put("productName",Optional.ofNullable(claimCase.getProductName()).orElse(""));
                map.put("caseNo", Optional.ofNullable(claimCase.getClaimCaseNo()).orElse(""));

                //快速处理协议书
                if (policyPerson != null) {

                    map.put("policyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));

                }
                SimpleDateFormat sdff = new SimpleDateFormat("yyyy-MM-dd"); // 设置日期格式
                String treatDateStrf = sdff.format(claimCase.getTreatDate());
                map.put("insuranceCompany", Optional.ofNullable(insuranceCompany).orElse(""));
                map.put("nameTwo", Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                map.put("claimCaseNo", Optional.ofNullable(claimCase.getClaimCaseNo()).orElse(""));
                map.put("treatTime", treatDateStrf);
                map.put("mobile", Optional.ofNullable(claimCase.getTreatMobile()).orElse(""));
                map.put("IDNumber", Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                String addressTwo = claimCase.getProvince()+"-"+claimCase.getCity()+"-"+claimCase.getDistrict()+claimCase.getAddress();
                map.put("addressTwo",addressTwo);

                //事故经过及伤情描述及责任划分=事故经过+事故责任
                String description = claimCase.getDescription();
                String accidentLiability = claimCase.getAccidentLiability();
                map.put("description","事故经过及伤情描述及责任划分：\n"+description+"\n"+accidentLiability);

                //defaultData  基本协议
                /*经事故各方自行协商，自愿达成如下协议：
                1、乙方就甲方本次受伤， 一次性向甲方赔偿    元 ，人民币大写：         。其中包括 医疗费、误工费、护理费、伤残等
                2、本次事故甲方受伤相关赔偿结束，各方均无异议，签字确认生效后生效，今后无涉。
                与各方均无关系。
                3、乙方依据本协议，在保险责任范围内赔偿甲方相应费用。
                4、最终解释权归保司*/

                //损失项目
                List<ClaimCaseObjectAssessment> byClaimCaseObjectId2 = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
                List<String> assessmentCollect2 = byClaimCaseObjectId2.stream().map(ClaimCaseObjectAssessment::getName).collect(Collectors.toList());


                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("经事故各方自行协商，自愿达成如下协议：\n");
                stringBuffer.append("1、当事人承诺本案所提供材料在其他任何途径未行报销，如有会提供如结算单等材料辅证，并承担相应的法律后果。\n");
                stringBuffer.append("2、乙方就甲方本次受伤， 一次性向甲方赔偿 "+claimCaseObject.getVerifyAmout().toString()+" 元 ，人民币大写："+ConvertUpMoneyTool.toChinese(claimCaseObject.getVerifyAmout().toString())+"。");
                stringBuffer.append("其中包括 "+String.join("、",assessmentCollect2)+"等\n" );
                stringBuffer.append("3、本次事故甲方受伤相关赔偿结束，各方均无异议，签字确认生效后生效，今后无涉。\n与各方均无关系。\n");
                stringBuffer.append("4、乙方依据本协议，在保险责任范围内赔偿甲方相应费用。本保险金承担医药费的首次赔付责任，被保险人或受益人需承诺未在本保险意外的其他渠道(包括但不限于其他保险公司、社会医宁保险等)存在二次赔付行为。如发现被保险人或受益人存在重复理赔行为，我司有权采职相应措施。\n");
                stringBuffer.append("5、最终解释权归保司\n");

                map.put("defaultData",stringBuffer.toString());

                //第一个签署人是出险人
//                List<PersonInfo> personInfoList = new ArrayList<>();
                PersonInfo personInfo3 = new PersonInfo();
                personInfo3.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo3.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo3.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo3.setPersonPhone(claimCase.getTreatMobile());

                personInfoList.add(personInfo3);

                //第二个签署人是处理岗
                PersonInfo personInfo4 = new PersonInfo();
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getAuditer());
                if(manager!=null){
//                    personInfo2.setPersonName(Optional.ofNullable(manager.geteSingnName()).orElse(""));
                    personInfo4.setPersonPhone(StringUtils.isBlank(manager.getMobile())?manager.getTelephone():manager.getMobile());
                }
                personInfoList.add(personInfo4);


                resultMap.put("personInfo",personInfoList);



            }
            break;
            case "ZBSZ":
                //众包三者
            {
                //出险通知书
                if (policyPerson != null) {
                    map.put("subsidiaryAgentName", policyPerson.getSubsidiaryAgentName());
                    map.put("policyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 设置日期格式
                String treatDateStr = sdf.format(claimCase.getTreatDate());

                map.put("treatDate", treatDateStr);
                String address = claimCase.getProvince()+"-"+claimCase.getCity()+"-"+claimCase.getDistrict()+claimCase.getAddress();
                map.put("address",address);
                sdf = new SimpleDateFormat("yyyy年MM月dd日"); // 设置日期格式
                String accidentTimeStr = sdf.format(claimCase.getTreatDate());
                map.put("accidentTime", accidentTimeStr);
                map.put("applyIdNum", claimCase.getApplyIdNum()==null ? "" : claimCase.getApplyIdNum());
                map.put("applyMobile", Optional.ofNullable(claimCase.getApplyMobile()).orElse(""));
                map.put("insuranceCompany",Optional.ofNullable(insuranceCompany).orElse(""));
                map.put("treatName",Optional.ofNullable(claimCase.getTreatName()).orElse(""));

                //银行信息
                List<ClaimCaseObjectPayment> byClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                if(CollectionUtils.isNotEmpty(byClaimCaseObjectId)){
                    StringBuffer bankInfo = new StringBuffer();
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byClaimCaseObjectId){
                        bankInfo.append("账户名称："+claimCaseObjectPayment1.getBankAccount()+"\n");
                        bankInfo.append("开户银行："+claimCaseObjectPayment1.getBankName()+"\n");
                        bankInfo.append("银行账户："+claimCaseObjectPayment1.getBankCard()+"\n");
                    }
                    map.put("bankInfo",bankInfo.toString());
                }

                //理算金额
                map.put("verifyAmout",claimCaseObject.getVerifyAmout().toString());
                //委托日期
                String commissionTimeStr= sdf.format(new Date());
                map.put("commissionTimeStr",commissionTimeStr);


                //初始化签字人
                PersonInfo personInfo = new PersonInfo();
                personInfo.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo.setPersonPhone(claimCase.getTreatMobile());
                List<PersonInfo> personInfoList = new ArrayList<>();
                personInfoList.add(personInfo);

//                resultMap.put("personInfo",personInfoList);
                //赔付意向及权益转让书
                if (policyPerson != null) {
                    String name = claimCase.getTreatName();
                    if ("GZ".equals(claimCase.getCaseType())){
                        name = policyPerson.getSubsidiaryAgentName();
                    }
                    map.put("name", name);
                    map.put("customerPolicyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }
                Date date = claimCase.getTreatDate();
                map.put("timeStr",DateUtils.format(date,"yyyy年MM月dd日 HH:mm:ss"));


                //大写理算金额
                map.put("convertUpVerifyAmout",ConvertUpMoneyTool.toChinese(claimCaseObject.getVerifyAmout().toString()));

                //赔付项目
//                List<ClaimCaseObjectAssessment> byClaimCaseObjectId = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
//                List<String> assessmentCollect = byClaimCaseObjectId.stream().map(ClaimCaseObjectAssessment::getName).collect(Collectors.toList());
//                map.put("assessmentCollect",String.join("、",assessmentCollect));

                //银行信息
                List<ClaimCaseObjectPayment> byPayClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                if(CollectionUtils.isNotEmpty(byPayClaimCaseObjectId)){
                    StringBuffer bankInfo = new StringBuffer();
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byPayClaimCaseObjectId){
                        bankInfo.append("账户名称："+claimCaseObjectPayment1.getBankAccount()+"\n");
                        bankInfo.append("开户银行："+claimCaseObjectPayment1.getBankName()+"\n");
                        bankInfo.append("银行账户："+claimCaseObjectPayment1.getBankCard()+"\n");
                    }
                    map.put("bankInfoTwo",bankInfo.toString());
                }

                //签字人
//                List<PersonInfo> personInfoList = new ArrayList<>();
                PersonInfo personInfo2 = new PersonInfo();
                personInfo2.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo2.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo2.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo2.setPersonPhone(claimCase.getTreatMobile());
                personInfoList.add(personInfo2);
//                personInfoList.add(personInfo);
//                /*PersonInfo personInfo1 = new PersonInfo();
//                BeanUtils.copyProperties(personInfo,personInfo1);
//                personInfoList.add(personInfo1);*/
//
//                resultMap.put("personInfo",personInfoList);

                String enterprise = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX+"EXPORT_INSCODE_ENTERPRISE", claimCase.getInsCode());
                map.put("enterprise", Optional.ofNullable(enterprise).orElse(""));
                map.put("productName",Optional.ofNullable(claimCase.getProductName()).orElse(""));
                map.put("caseNo", Optional.ofNullable(claimCase.getClaimCaseNo()).orElse(""));

                //tjxys
                //调解协议书
                //骑手三者人伤事故赔偿调解协议书，只兼容一个三者人伤的赔付
                String IDNumber = claimCase.getTreatIdNum();
                map.put("insuranceCompany", insuranceCompany);
                map.put("nameTwo", claimCase.getTreatName());
                try {
                    int gender = IdCardTool2.getGender(IDNumber);
                    map.put("gender", gender == 0 ? "女" : "男");
                }catch (Exception e){

                }

                try {
                    int age = IdCardTool2.getAge(IDNumber);
                    map.put("age", age+"");
                }catch (Exception e){

                }
                map.put("IDNumber", IDNumber);
                map.put("mobile", claimCase.getTreatMobile());

                //三者证件号码
                String thirdIDNumber = claimCaseObject.getTreatIdNum();
                map.put("thirdName", claimCaseObject.getTreatName());
                try {
                    int thirdGender = IdCardTool2.getGender(thirdIDNumber);
                    map.put("thirdGender", thirdGender == 0 ? "女" : "男");
                }catch (Exception e){

                }

                try {
                    int thirdAge = IdCardTool2.getAge(thirdIDNumber);
                    map.put("thirdAge", thirdAge+"");
                }catch (Exception e){

                }
                map.put("thirdIDNumber", thirdIDNumber);
                map.put("thirdMobile", claimCaseObject.getMobile());

                //事故简要经过
                StringBuffer description = new StringBuffer();
                String addressTwo = claimCase.getProvince()+"-"+claimCase.getCity()+"-"+claimCase.getDistrict()+claimCase.getAddress();
                description.append("    事故当事人"+claimCase.getTreatName()+"于"+DateUtils.format(claimCase.getTreatDate(),"yyyy年MM月dd日")+"在"+addressTwo +
                        "发生交通事故，该事故经交警队第        号道路交通事故认定书认定甲方负"+claimCase.getAccidentLiability()+"责任，乙方"+claimCaseObject.getAccidentLiability()+
                        "责任");
                map.put("description",description.toString());

                //基本协议
                BigDecimal verifyAmout = claimCaseObject.getVerifyAmout();
                StringBuffer baseAgreement = new StringBuffer();
                baseAgreement.append("1、当事人承诺本案所提供材料在其他任何途径未行报销，如有会提供如结算单等材料辅证，并承担相应的法律后果。\n");
                baseAgreement.append("2、甲方赔偿乙方医药费、护理费、营养费、误工费等合计"+verifyAmout.toString()+"元。\n");
                baseAgreement.append("3、甲方赔偿乙方车辆损失"+"元。\n");
                baseAgreement.append("4、上述赔款合计"+verifyAmout.toString()+"元，扣除责任比例后计"+verifyAmout.toString()+"元。\n");
                baseAgreement.append("5、今后双方无涉。\n");
                map.put("baseAgreement",baseAgreement.toString());


                //赔款支付方式
                StringBuffer payWay = new StringBuffer();
                List<ClaimCaseObjectPayment> byClaimCaseObjectId2 = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                String bankAccount1 = "";
                String bankName1 = "";
                String bankCard1 = "";
                String approveMoney1 = "";

                if(CollectionUtils.isNotEmpty(byClaimCaseObjectId2)){
                    Integer index=1;
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byClaimCaseObjectId2){
                        bankAccount1 = claimCaseObjectPayment1.getBankAccount();
                        bankName1 = claimCaseObjectPayment1.getBankName();
                        bankCard1 = claimCaseObjectPayment1.getBankCard();
                        approveMoney1 = claimCaseObjectPayment1.getPayAmount().toString();
                        payWay.append(index+". 被保险人向保险公司申请，将赔"+approveMoney1+"元，即（大写："+(StringUtils.isNotBlank(approveMoney1)?ConvertUpMoneyTool.toChinese(approveMoney1):"")+
                                "）直接划付"+bankAccount1+"，开户名称："+bankAccount1+"；开户银行："+bankName1+"；银行账号："+bankCard1+"。");
                        index++;
                    }
                }

                map.put("payWay",payWay.toString());

                //签署人分别为出险人、三者和处理岗人员
                //第一个是出险人信息
                PersonInfo personInfo3 = new PersonInfo();
                personInfo3.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo3.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo3.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo3.setPersonPhone(claimCase.getTreatMobile());
//                List<PersonInfo> personInfoList = new ArrayList<>();
                personInfoList.add(personInfo3);

                //第二个是三者信息
                PersonInfo personInfoThird = new PersonInfo();
                personInfoThird.setPersonPhone(claimCaseObject.getMobile());
                personInfoThird.setPersonIdType(claimCaseObject.getTreatIdType());
                personInfoThird.setPersonIdNumber(claimCaseObject.getTreatIdNum());
                personInfoThird.setPersonName(claimCaseObject.getTreatName());
                personInfoList.add(personInfoThird);

                //第三个签署人是处理岗
                PersonInfo personInfo4 = new PersonInfo();
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getAuditer());
                if(manager!=null){
//                    personInfo2.setPersonName(Optional.ofNullable(manager.geteSingnName()).orElse(""));
                    personInfo4.setPersonPhone(StringUtils.isBlank(manager.getMobile())?manager.getTelephone():manager.getMobile());
                }
                personInfoList.add(personInfo4);
                resultMap.put("personInfo",personInfoList);

            }
            break;
            case "ZBCS":
            {
                //出险通知书
                if (policyPerson != null) {
                    map.put("subsidiaryAgentName", policyPerson.getSubsidiaryAgentName());
                    map.put("policyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 设置日期格式
                String treatDateStr = sdf.format(claimCase.getTreatDate());

                map.put("treatDate", treatDateStr);
                String address = claimCase.getProvince()+"-"+claimCase.getCity()+"-"+claimCase.getDistrict()+claimCase.getAddress();
                map.put("address",address);
                sdf = new SimpleDateFormat("yyyy年MM月dd日"); // 设置日期格式
                String accidentTimeStr = sdf.format(claimCase.getTreatDate());
                map.put("accidentTime", accidentTimeStr);
                map.put("applyIdNum", claimCase.getApplyIdNum()==null ? "" : claimCase.getApplyIdNum());
                map.put("applyMobile", Optional.ofNullable(claimCase.getApplyMobile()).orElse(""));
                map.put("insuranceCompany",Optional.ofNullable(insuranceCompany).orElse(""));
                map.put("treatName",Optional.ofNullable(claimCase.getTreatName()).orElse(""));

                //银行信息
                List<ClaimCaseObjectPayment> byClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                if(CollectionUtils.isNotEmpty(byClaimCaseObjectId)){
                    StringBuffer bankInfo = new StringBuffer();
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byClaimCaseObjectId){
                        bankInfo.append("账户名称："+claimCaseObjectPayment1.getBankAccount()+"\n");
                        bankInfo.append("开户银行："+claimCaseObjectPayment1.getBankName()+"\n");
                        bankInfo.append("银行账户："+claimCaseObjectPayment1.getBankCard()+"\n");
                    }
                    map.put("bankInfo",bankInfo.toString());
                }

                //理算金额
                map.put("verifyAmout",claimCaseObject.getVerifyAmout().toString());
                //委托日期
                String commissionTimeStr= sdf.format(new Date());
                map.put("commissionTimeStr",commissionTimeStr);


                //初始化签字人
                PersonInfo personInfo = new PersonInfo();
                personInfo.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo.setPersonPhone(claimCase.getTreatMobile());
                List<PersonInfo> personInfoList = new ArrayList<>();
                personInfoList.add(personInfo);

//                resultMap.put("personInfo",personInfoList);
                //赔付意向及权益转让书
                if (policyPerson != null) {
                    String name = claimCase.getTreatName();
                    if ("GZ".equals(claimCase.getCaseType())){
                        name = policyPerson.getSubsidiaryAgentName();
                    }
                    map.put("name", name);
                    map.put("customerPolicyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }
                Date date = claimCase.getTreatDate();
                map.put("timeStr",DateUtils.format(date,"yyyy年MM月dd日 HH:mm:ss"));


                //大写理算金额
                map.put("convertUpVerifyAmout",ConvertUpMoneyTool.toChinese(claimCaseObject.getVerifyAmout().toString()));


                //银行信息
                List<ClaimCaseObjectPayment> byPayClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                if(CollectionUtils.isNotEmpty(byPayClaimCaseObjectId)){
                    StringBuffer bankInfo = new StringBuffer();
                    for(ClaimCaseObjectPayment claimCaseObjectPayment1:byPayClaimCaseObjectId){
                        bankInfo.append("账户名称："+claimCaseObjectPayment1.getBankAccount()+"\n");
                        bankInfo.append("开户银行："+claimCaseObjectPayment1.getBankName()+"\n");
                        bankInfo.append("银行账户："+claimCaseObjectPayment1.getBankCard()+"\n");
                    }
                    map.put("bankInfoTwo",bankInfo.toString());
                }

                //签字人
//                List<PersonInfo> personInfoList = new ArrayList<>();
                PersonInfo personInfo2 = new PersonInfo();
                personInfo2.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo2.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo2.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo2.setPersonPhone(claimCase.getTreatMobile());
                personInfoList.add(personInfo2);
//                personInfoList.add(personInfo);
                /*PersonInfo personInfo1 = new PersonInfo();
                BeanUtils.copyProperties(personInfo,personInfo1);
                personInfoList.add(personInfo1);*/


                resultMap.put("personInfo",personInfoList);

                String enterprise = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX+"EXPORT_INSCODE_ENTERPRISE", claimCase.getInsCode());
                map.put("enterprise", Optional.ofNullable(enterprise).orElse(""));
                map.put("productName",Optional.ofNullable(claimCase.getProductName()).orElse(""));
                map.put("caseNo", Optional.ofNullable(claimCase.getClaimCaseNo()).orElse(""));


            }
            break;
            case "SPSQS":
                //索赔申请书
            {
                if (policyPerson != null) {
                    map.put("customerPolicyNo", Optional.ofNullable(policyPerson.getPolicyNo()).orElse(""));
                }
                Date date = claimCase.getTreatDate();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss",Locale.CHINA); // 设置日期格式
                String treatDateStr = sdf.format(date);
                map.put("productName", Optional.ofNullable(claimCase.getProductName()).orElse(""));
                map.put("aName", Optional.ofNullable(claimCase.getApplyName()).orElse(""));
                map.put("aMobile", Optional.ofNullable(claimCase.getApplyMobile()).orElse(""));
                map.put("aIdTp", Optional.ofNullable(claimCase.getApplyIdType()).orElse(""));
                map.put("aIdNum", Optional.ofNullable(claimCase.getApplyIdNum()).orElse(""));
                map.put("tName", Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                map.put("tIdTp", Optional.ofNullable(claimCase.getTreatIdType()).orElse(""));
                map.put("tIdNum", Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                map.put("tDate", treatDateStr);
                map.put("tReason", "交通事故");
                String address = claimCase.getProvince()+"-"+claimCase.getCity()+"-"+claimCase.getDistrict()+claimCase.getAddress();
                map.put("address",address);
                map.put("description", Optional.ofNullable(claimCase.getDescription()).orElse(""));
            }
            break;
            case "CWKJCLD":
                //车物快捷处理单
                if(policyPerson != null){
                    FloatProduct floatProduct = floatProductService.selectByElmCode(policyPerson.getPlanId());
                    if(floatProduct != null){
                        map.put("floatProductName",floatProduct.getName());
                        if(floatProduct.getType() == 1){
                            map.put("treatName",policyPerson.getSubsidiaryAgentName());
                        }else if(floatProduct.getType() == 2){
                            map.put("treatName",policyPerson.getName());
                        }
                    }
                    map.put("policyNo",policyPerson.getPolicyNo());
                    map.put("name",policyPerson.getName());
                }
                String address = "";
                if(StringUtils.isNotBlank(claimCase.getProvince())){
                    address += claimCase.getProvince();
                }
                if(StringUtils.isNotBlank(claimCase.getCity())){
                    address += claimCase.getCity();
                }
                if(StringUtils.isNotBlank(claimCase.getDistrict())){
                    address += claimCase.getDistrict();
                }
                if(StringUtils.isNotBlank(claimCase.getAddress())){
                    address += claimCase.getAddress();
                }
                map.put("address",address);
                map.put("treateTime", kd.beijingRoastDuck.util.DateUtils.format(claimCase.getTreatDate(), kd.beijingRoastDuck.util.DateUtils.FORMAT_DATE_YYYY_MM_DD_CHINA));
                map.put("claimCaseNo",claimCase.getClaimCaseNo());

                String process = kd.beijingRoastDuck.util.DateUtils.format(claimCase.getTreatDate(), kd.beijingRoastDuck.util.DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS);
                if(StringUtils.isNotBlank(claimCase.getProvince())){
                    process +=  " " + claimCase.getProvince();
                }
                if(StringUtils.isNotBlank(claimCase.getCity())){
                    process += "-" + claimCase.getCity();
                }
                if(StringUtils.isNotBlank(claimCase.getDistrict())){
                    process += "-" + claimCase.getDistrict();
                }
                if(StringUtils.isNotBlank(claimCase.getAddress())){
                    process += " " + claimCase.getAddress();
                }
                if(StringUtils.isNotBlank(claimCase.getDescription())){
                    process += " " + claimCase.getDescription();
                }

                process += ",索赔金额为___元";
                map.put("description",process);

                //损失项区域
                List<ClaimCaseObjectAssessment> byAssessmentClaimCaseObjectId = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
                Map<String,BigDecimal> assessmentGroupByCode = new HashMap<>();
                byAssessmentClaimCaseObjectId.stream().forEach(claimCaseObjectAssessment -> {
                    if(!assessmentGroupByCode.containsKey(claimCaseObjectAssessment.getCode())){
                        assessmentGroupByCode.put(claimCaseObjectAssessment.getCode(),BigDecimal.ZERO);
                    }
                    if(claimCaseObjectAssessment.getApprovedAmount()!=null){
                        assessmentGroupByCode.put(claimCaseObjectAssessment.getCode(),assessmentGroupByCode.get(claimCaseObjectAssessment.getCode()).add(claimCaseObjectAssessment.getApprovedAmount()));
                    }
                });
                int index = 1;
                if(assessmentGroupByCode.keySet().size()>0){
                    for(String code : assessmentGroupByCode.keySet()){
                        BigDecimal bigDecimal = assessmentGroupByCode.get(code);
                        map.put("str"+(index++),EstimateInventoryEnum.codeToName(code));
                        map.put("str"+(index++),"1");
                        map.put("str"+(index++),bigDecimal.toString());
                        map.put("str"+(index++),bigDecimal.toString());
                        map.put("str"+(index++),"1");
                        map.put("str"+(index++),bigDecimal.toString());
                    }
                }
                map.put("str"+(index++),"残值");
                map.put("str"+(index++),"1");
                // BigDecimal数据为空
                if(claimCaseObject.getResidualNuclearLossValue()==null){
                    map.put("str"+(index++)," ");
                    map.put("str"+(index++)," ");
                    map.put("str"+(index++),"1");
                    map.put("str"+(index++)," ");
                }else {
                    map.put("str"+(index++),claimCaseObject.getResidualNuclearLossValue().toString());
                    map.put("str"+(index++),claimCaseObject.getResidualNuclearLossValue().toString());
                    map.put("str"+(index++),"1");
                    map.put("str"+(index++),claimCaseObject.getResidualNuclearLossValue().toString());
                }


                //核损合计
                if(claimCaseObject.getNuclearLossSum()==null){
                    map.put("nuclearLossSum"," ");
                }else {
                    map.put("nuclearLossSum",claimCaseObject.getNuclearLossSum().toString());
                }


                //理算金额
                if(claimCaseObject.getVerifyAmout()==null){
                    map.put("verifyAmout"," ");
                }else {
                    map.put("verifyAmout",claimCaseObject.getVerifyAmout().toString());
                }


                //银行信息
                List<ClaimCaseObjectPayment> byPayClaimCaseObjectId = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
                StringBuffer transferInfo =  new StringBuffer();
                transferInfo.append("致中国人民财产保险股份有限公司上海分公司：\n");
                transferInfo.append("本人（单位）同意贵公司将赔款直接划入本人（单位）下列指定账户：\n");
                if(CollectionUtils.isNotEmpty(byPayClaimCaseObjectId)){

                    byPayClaimCaseObjectId.stream().forEach(claimCaseObjectPayment -> {
                        transferInfo.append("户名（持卡人姓名）:"+claimCaseObjectPayment.getBankAccount()+"\n");
                        transferInfo.append("卡号（账号）："+claimCaseObjectPayment.getBankName()+"\n");
                        transferInfo.append("开户行："+claimCaseObjectPayment.getBankCard()+"\n");
                    });
                }else {
                    transferInfo.append("户名（持卡人姓名）:\n");
                    transferInfo.append("卡号（账号）：\n");
                    transferInfo.append("开户行：\n");
                }
                transferInfo.append("收款人与被保险人关系： £本人（单位） £三者方 £维修商  \n");
                map.put("transferInfo",transferInfo.toString());


                //第一个签署人是出险人
                List<PersonInfo> personInfoList = new ArrayList<>();
                PersonInfo personInfo = new PersonInfo();
                personInfo.setPersonName(Optional.ofNullable(claimCase.getTreatName()).orElse(""));
                personInfo.setPersonIdNumber(Optional.ofNullable(claimCase.getTreatIdNum()).orElse(""));
                personInfo.setPersonIdType(SignCenterTool.idType4SignCenter(claimCase.getTreatIdType()));
                personInfo.setPersonPhone(claimCase.getTreatMobile());

                personInfoList.add(personInfo);

                //第二个是三者信息
                PersonInfo personInfoThird = new PersonInfo();
                personInfoThird.setPersonPhone(claimCaseObject.getMobile());
                personInfoThird.setPersonIdType(claimCaseObject.getTreatIdType());
                personInfoThird.setPersonIdNumber(claimCaseObject.getTreatIdNum());
                personInfoThird.setPersonName(claimCaseObject.getTreatName());
                personInfoList.add(personInfoThird);


                resultMap.put("personInfo",personInfoList);

                break;
        }


        //初始化填充数据
        JSONArray jsonArray = new JSONArray();
        if(StringUtils.isNotBlank(signTemplate.getJsonData())){
            JSONArray fieldJson = JSONArray.parseArray(signTemplate.getJsonData());
            for(int i=0;i<fieldJson.size();i++){
                JSONObject jsonObject = fieldJson.getJSONObject(i);
                String field = jsonObject.getString("field");
                jsonObject.put("value",map.get(field));
                jsonArray.add(jsonObject);
            }
        }

        //如果签字模板配置的签字数量大于初始化数据的签字数量，则进行补充
        if(resultMap.containsKey("personInfo") && signTemplate.getSignNum() != null
                && signTemplate.getSignNum()>((List)resultMap.get("personInfo")).size()){
            int nowNum = ((List)resultMap.get("personInfo")).size();
            int signNum = signTemplate.getSignNum().intValue();
            for(;nowNum<signNum;nowNum++){
                ((List)resultMap.get("personInfo")).add(resultMap.get("personInfo"));
            }
        }

        resultMap.put("initValue",jsonArray);
        String privateUrlOuter = AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, signTemplate.getTemplateObjectId(), 3600 * 1000 * 3);
        resultMap.put("imgUrl",privateUrlOuter);
        resultMap.put("templateName",signTemplate.getTemplateName());
        resultMap.put("templateTitle",signTemplate.getTemplateTitle());
        return resultMap;
    }


    /**
     * 推送签字任务接口
     * @param jsonStr
     * @param claimCaseObjectId
     * @return
     */
    @RequestMapping("pushSign")
    @ResponseBody
    public String pushSign(String jsonStr,String claimCaseObjectId){
        try {
            String userId = ShiroSessionUtil.getLoginSession().getId();
            String userName = ShiroSessionUtil.getLoginSession().getRealName();
            logger.info("====推送签字任务:赔付对象id{},提交数据为{}======",claimCaseObjectId,jsonStr);
            if(StringUtils.isAnyBlank(claimCaseObjectId,jsonStr)){
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
            }

            ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);
            if(claimCaseObject == null){
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER);
            }
            if(!claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX38.getCode()) && !claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX39.getCode()) && !claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX41.getCode())){
                return JsonBizTool.genJson("-1","估损对象状态有误！！！");
            }

            String signCallBackUrl = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "SIGN_CALL_BACK_URL");
            if(StringUtils.isBlank(signCallBackUrl)){
                return JsonBizTool.genJson("-1","未配置签字回调地址，请联系管理员！！！");
            }

            List<SignPushTaskReq> signPushTaskReqList;
            try {
                signPushTaskReqList = JSON.parseArray(jsonStr, SignPushTaskReq.class);

                if(CollectionUtils.isEmpty(signPushTaskReqList)){
                    return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
                }
            }catch (Exception e){
                e.printStackTrace();
                return JsonBizTool.genJson("-1","json数据解析异常");
            }

            //组装推送调用签字中台的数据
            signPushTaskReqList.stream().forEach(signPushTaskReq -> {
                Map<String,Object> pushMap = new HashMap<>();
                pushMap.put("claimCaseObjectId",claimCaseObjectId);
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getAuditer());
                pushMap.put("signPushTaskReq",JSON.toJSONString(signPushTaskReq));
                String psnMobile=StringUtils.isBlank(manager.getMobile())?manager.getTelephone():manager.getMobile();
                pushMap.put("psnMobile",StringUtils.isBlank(manager.getMobile())?manager.getTelephone():manager.getMobile());
                logger.info("====组装推送调用签字中台的数据:赔付对象id{},提交数据为{}==={}===",claimCaseObjectId,JSON.toJSONString(signPushTaskReq),psnMobile);
                amqpTemplate.convertAndSend(QueueName.SIGN_TEMPLATE_PUSH,JsonBizTool.genJson(pushMap));
            });

            //发送队列成功之后，将估损对象修改成签字待返回状态
            Map<String,Object> modifyMap = new HashMap<>();
            ClaimCaseObject claimCaseObjectUpdate =  new ClaimCaseObject();
            claimCaseObjectUpdate.setId(claimCaseObject.getId());
            claimCaseObjectUpdate.setStatus(ClaimCaseObjectStatusEnum.BAX39.getCode());
            modifyMap.put("claimCaseObjectUpdate",claimCaseObjectUpdate);



            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
            claimCaseLog.setStatus(claimCaseObject.getStatus());
            claimCaseLog.setDescription("下发签字任务完成");
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());
            modifyMap.put("claimCaseLog",claimCaseLog);
            claimCaseObjectService.nextSignTask(modifyMap);

        }catch (Exception e){
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.ERROR_SYSTEMERROR);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    /**
     * 完成签字流程，进入到签章环节
     * @param claimCaseObjectId
     * @return
     */
    @RequestMapping("endSignTask")
    @ResponseBody
    public String endSignTask(String claimCaseObjectId){
        String userId = ShiroSessionUtil.getLoginSession().getId();
        String userName = ShiroSessionUtil.getLoginSession().getRealName();
        try {
            logger.info("====完成签字任务开始:赔付对象id{}======",claimCaseObjectId);
            if(StringUtils.isBlank(claimCaseObjectId)){
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
            }
            ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);
            if(claimCaseObject == null){
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER);
            }
            if(!(claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX39.getCode()) || claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX38.getCode()))){
                return JsonBizTool.genJson("-1","估损对象状态有误！！！");
            }

            Map<String,Object> modifyMap = new HashMap<>();

            ClaimCaseObject claimCaseObjectUpdate =  new ClaimCaseObject();
            claimCaseObjectUpdate.setId(claimCaseObject.getId());
            claimCaseObjectUpdate.setStatus(ClaimCaseObjectStatusEnum.BAX40.getCode());
            modifyMap.put("claimCaseObjectUpdate",claimCaseObjectUpdate);



            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseLog.setLevel(3);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
            claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
            claimCaseLog.setStatus(claimCaseObject.getStatus());
            claimCaseLog.setDescription("完成签字任务");
            claimCaseLog.setCreator(userName + "-" + userId);
            claimCaseLog.setCreateTime(new Date());
            modifyMap.put("claimCaseLog",claimCaseLog);
            claimCaseObjectService.nextSignTask(modifyMap);

            logger.info("====完成签字任务结束:赔付对象id{}======",claimCaseObjectId);
        }catch (Exception e){
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.ERROR_SYSTEMERROR);
        }

        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }



    /**
     * @author: hoomik
     * @description: 查看签署数据状态
     * @date: 2024/12/10 14:17
     * @param claimCaseObjectId
     * @return
     */
    @RequestMapping("detailSignPushLog")
    public String detailSignPushLog(String claimCaseObjectId, Model model){
        if(StringUtils.isBlank(claimCaseObjectId)){
            model.addAttribute("errorMsg","缺少损失id，请刷新后重试！！");
            return "claimCaseObject/detailSignPushLog";
        }

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseObjectId",claimCaseObjectId);
        List<SignTemplatePushVo> signTemplatePushLogList = signTemplatePushLogService.findByParam(paramMap);
        model.addAttribute("signTemplatePushLogList",signTemplatePushLogList);

        return "claimCaseObject/detailSignPushLog";
    }

    /**
     * 京东案件查勘估损列表
     * @param claimCaseObjectVo
     * @param request
     * @param model
     * @return
     */
    @RequestMapping(value = {"claimCaseObjectListJD", ""})
    public String claimCaseObjectListJD(ClaimCaseObjectVo claimCaseObjectVo, HttpServletRequest request, Model model) {
        PageParam pp = Tool.genPageParam(request);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("insCode", "JZTB");
        if (claimCaseObjectVo.getComeFrom() == null) {
            return "error/404";
        }
        paramMap.put("comeFrom", claimCaseObjectVo.getComeFrom());
        paramMap.put("userId", ShiroSessionUtil.getLoginSession().getId());

        if (claimCaseObjectVo.getStatus() != null) {
            paramMap.put("status", claimCaseObjectVo.getStatus());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getClaimCaseNo())) {
            paramMap.put("claimCaseNo", claimCaseObjectVo.getClaimCaseNo());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatName())) {
            paramMap.put("treatName", claimCaseObjectVo.getTreatName());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatIdNum())) {
            paramMap.put("treatIdNum", claimCaseObjectVo.getTreatIdNum());
        }
        if (claimCaseObjectVo.getType() != null) {
            paramMap.put("type", claimCaseObjectVo.getType());
        }
        if (claimCaseObjectVo.getCategory() != null) {
            paramMap.put("category", claimCaseObjectVo.getCategory());
        }

        PageInfo<ClaimCaseObjectReq> page = claimCaseObjectService.claimCaseObjectListJD(paramMap, pp);
        model.addAttribute("page", page);
        model.addAttribute("claimCaseObjectVo", claimCaseObjectVo);

        Map<String, Object> statusMap = ClaimCaseObjectStatusEnum.getStatusMap();
        model.addAttribute("statusMap", statusMap);

        Map<String, Object> labelShowMap = ClaimCaseLabelEnum.getClaimCaseLabelMap();
        model.addAttribute("labelShowMap", labelShowMap);

        // 出险类型
        Map<String, ApplyTypeNewEnum> typeCategoryMap = ApplyTypeNewEnum.getAppyTypeNewMap();
        model.addAttribute("typeCategoryMap", typeCategoryMap);

        //展示InsCode选择
        Map<String, String> insCodeMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "POLICY_REPUSH_INS_CODE");
        model.addAttribute("insCodeMap", insCodeMap);

        return "claimCaseObject/claimCaseObjectListJD";
    }

    /**
     * 京东估损初审、复核、理算初审列表
     * @param claimCaseObjectVo
     * @param request
     * @param model
     * @return
     */
    @RequestMapping(value = {"claimCaseObjectList4BSJD", ""})
    public String claimCaseObjectList4BSJD(ClaimCaseObjectVo claimCaseObjectVo, HttpServletRequest request, Model model) {
        PageParam pp = Tool.genPageParam(request);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("insCode", "JZTB");
        if (claimCaseObjectVo.getComeFrom() == null) {
            return "error/404";
        }
        paramMap.put("comeFrom", claimCaseObjectVo.getComeFrom());
        paramMap.put("userId", ShiroSessionUtil.getLoginSession().getId());

        if (claimCaseObjectVo.getStatus() != null) {
            paramMap.put("status", claimCaseObjectVo.getStatus());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getClaimCaseNo())) {
            paramMap.put("claimCaseNo", claimCaseObjectVo.getClaimCaseNo());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatName())) {
            paramMap.put("treatName", claimCaseObjectVo.getTreatName());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatIdNum())) {
            paramMap.put("treatIdNum", claimCaseObjectVo.getTreatIdNum());
        }
        if (claimCaseObjectVo.getType() != null) {
            paramMap.put("type", claimCaseObjectVo.getType());
        }
        if (claimCaseObjectVo.getCategory() != null) {
            paramMap.put("category", claimCaseObjectVo.getCategory());
        }

        PageInfo<ClaimCaseObjectReq> page = claimCaseObjectService.claimCaseObjectList4BSJD(paramMap, pp);
        model.addAttribute("page", page);
        model.addAttribute("claimCaseObjectVo", claimCaseObjectVo);

        Map<String, Object> statusMap = ClaimCaseObjectStatusEnum.getStatusMap();
        model.addAttribute("statusMap", statusMap);

        Set<String> managerIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (ClaimCaseObjectReq claimCaseObject : page.getList()) {
                if (StringUtils.isNotBlank(claimCaseObject.getAuditer())) {
                    managerIdList.add(claimCaseObject.getAuditer());
                }
            }
            if (CollectionUtils.isNotEmpty(managerIdList)) {
                List<Manager> managerList = managerService.findByManagerIdList(new ArrayList<>(managerIdList));
                Map<String, String> managerMap = managerList.stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
                model.addAttribute("managerMap", managerMap);
            }
        }


        // 出险类型
        Map<String, ApplyTypeNewEnum> typeCategoryMap = ApplyTypeNewEnum.getAppyTypeNewMap();
        model.addAttribute("typeCategoryMap", typeCategoryMap);

        //展示InsCode选择狂
        Map<String, String> insCodeMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "POLICY_REPUSH_INS_CODE");
        model.addAttribute("insCodeMap", insCodeMap);


        return "claimCaseObject/claimCaseObjectList4BSJD";
    }

    /**
     * 京东案件签章
     * @param claimCaseObjectVo
     * @param request
     * @param model
     * @return
     */
    @RequestMapping(value = {"claimCaseSignatureListJD", ""})
    public String claimCaseSignatureListJD(ClaimCaseObjectVo claimCaseObjectVo, HttpServletRequest request, Model model) {
        PageParam pp = Tool.genPageParam(request);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", ShiroSessionUtil.getLoginSession().getId());

        if (claimCaseObjectVo.getStatus() != null) {
            paramMap.put("status", claimCaseObjectVo.getStatus());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getClaimCaseNo())) {
            paramMap.put("claimCaseNo", claimCaseObjectVo.getClaimCaseNo());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatName())) {
            paramMap.put("treatName", claimCaseObjectVo.getTreatName());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getTreatIdNum())) {
            paramMap.put("treatIdNum", claimCaseObjectVo.getTreatIdNum());
        }
        if (claimCaseObjectVo.getType() != null) {
            paramMap.put("type", claimCaseObjectVo.getType());
        }
        if (claimCaseObjectVo.getCategory() != null) {
            paramMap.put("category", claimCaseObjectVo.getCategory());
        }

        if (StringUtils.isNotBlank(claimCaseObjectVo.getAuditer())){
            paramMap.put("auditer", claimCaseObjectVo.getAuditer());
        }
        if (StringUtils.isNotBlank(claimCaseObjectVo.getInsCode())){
            paramMap.put("insCode", claimCaseObjectVo.getInsCode());
        }


        PageInfo<ClaimCaseObjectReq> page = claimCaseObjectService.claimCaseSignatureListJD(paramMap, pp);
        model.addAttribute("page", page);
        model.addAttribute("claimCaseObjectVo", claimCaseObjectVo);

        Map<String, Object> statusMap = ClaimCaseObjectStatusEnum.getStatusMap();
        model.addAttribute("statusMap", statusMap);

        Set<String> managerIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (ClaimCaseObjectReq claimCaseObject : page.getList()) {
                if (StringUtils.isNotBlank(claimCaseObject.getAuditer())) {
                    managerIdList.add(claimCaseObject.getAuditer());
                }
            }
            if (CollectionUtils.isNotEmpty(managerIdList)) {
                List<Manager> managerList = managerService.findByManagerIdList(new ArrayList<>(managerIdList));
                Map<String, String> managerMap = managerList.stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
                model.addAttribute("managerMap", managerMap);
            }
        }

        // 出险类型
        Map<String, ApplyTypeNewEnum> typeCategoryMap = ApplyTypeNewEnum.getAppyTypeNewMap();
        model.addAttribute("typeCategoryMap", typeCategoryMap);

        Map<String, String> managerSelectMap = managerService.findAllManager(new HashMap<>()).stream().collect(Collectors.toMap(Manager::getId, Manager::getRealName));
        model.addAttribute("managerSelectMap", managerSelectMap);
        return "claimCaseObject/claimCaseSignatureListJD";
    }

    /**
     * 京东开始任务
     * @param id
     * @param comeFrom
     * @param checkCondition
     * @param model
     * @return
     */
    @RequestMapping(value = {"startTaskJD", ""})
    public String startTaskJD(String id, Integer comeFrom,String checkCondition, Model model) {

        String userId = ShiroSessionUtil.getLoginSession().getId();
        String realName = ShiroSessionUtil.getLoginSession().getRealName();
        String position = ShiroSessionUtil.getLoginRoleSession().get(0).getName();

        if (StringUtils.isBlank(id) || comeFrom == null) {
            model.addAttribute("exception", "缺少必填参数");
            return "error/500";
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            model.addAttribute("exception", "估损单不存在");
            return "error/500";
        }
        if (claimCaseObject.getIsCaseClosed() == 1) {
            model.addAttribute("exception", "该估损单案件已关闭");
            return "error/500";
        }

        if (comeFrom == 1) {
            if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus())
                    && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            }
        }

        // 内部获取审核任务
        if (comeFrom == 2) {
            if (StringUtils.isNotBlank(claimCaseObject.getCheckAuditer()) && !claimCaseObject.getCheckAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getCheckAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX22.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX32.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getCheckAuditer())) {
                    claimCaseObject.setCheckAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        }

        // 保司获取审核任务
        if (comeFrom == 3) {
            if (StringUtils.isNotBlank(claimCaseObject.getInsAuditer()) && !claimCaseObject.getInsAuditer().equals(userId)) {
                Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getInsAuditer());
                model.addAttribute("exception", "该案件已被" + manager.getRealName() + "获取");
                return "error/500";
            }
            if (!ClaimCaseObjectStatusEnum.BAX25.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX35.getCode().equals(claimCaseObject.getStatus())) {
                model.addAttribute("errorMsg", "当前任务状态已流转，请更换任务！");
            } else {
                if (StringUtils.isBlank(claimCaseObject.getInsAuditer())) {
                    claimCaseObject.setInsAuditer(userId);
                    claimCaseObject.setModifyTime(new Date());
                    claimCaseObjectService.updateByPrimaryKeySelective(claimCaseObject);
                }
            }
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());
        model.addAttribute("claimCase", claimCase);
        if (StringUtils.isNotBlank(claimCase.getPolicyPersonId())) {
            PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());
            model.addAttribute("policyPerson", policyPerson);
          /*  String redisPlanCode = policyPerson.getPlanId() + "_" + policyPerson.getPayPremium().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
            String planCode = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "ELE_CCIC_PLAN_CODE", redisPlanCode);*/
            Product product = productService.selectByEleProductCode(policyPerson.getPlanId());
            model.addAttribute("product", product);
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseId", claimCase.getId());
        String skipAttach = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "not_search_file_content_type");
        if (StringUtils.isNotBlank(skipAttach)) {
            List<String> skipAttachList = Arrays.asList(skipAttach.split(","));
            paramMap.put("skipAttach", skipAttachList);
        }
        List<ClaimCaseAttach> claimAttachList = claimCaseAttachService.findAttachByParam(paramMap);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(claimAttachList)) {
            claimAttachList.stream().forEach(claimCaseAttach -> {
                claimCaseAttach.setFileObjectId(AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, claimCaseAttach.getFileObjectId(), 3600 * 1000 * 3));
            });
        }
        model.addAttribute("claimAttachList", claimAttachList);
        model.addAttribute("claimCaseObject", claimCaseObject);

        if (StringUtils.isNotBlank(claimCaseObject.getBankInfoId())) {
            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(claimCaseObject.getBankInfoId());
            if (bankInfo != null) {
                model.addAttribute("bankInfo", bankInfo);
            }
        }

        if (!ClaimCaseObjectStatusEnum.BAX21.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX24.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX27.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("readonly", true);
        }

        if (!ClaimCaseObjectStatusEnum.BAX31.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX34.getCode().equals(claimCaseObject.getStatus()) && !ClaimCaseObjectStatusEnum.BAX37.getCode().equals(claimCaseObject.getStatus())) {
            model.addAttribute("verifyReadonly", true);
        }

        model.addAttribute("comeFrom", comeFrom);
        model.addAttribute("checkCondition", checkCondition);

        List<ClaimCaseObjectAssessment> objectAssessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObject.getId());
        List<ClaimCaseObjectPayment> objectPaymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObject.getId());
        model.addAttribute("objectAssessmentList", objectAssessmentList);
        model.addAttribute("objectPaymentList", JsonTool.genByFastJson(objectPaymentList));

        Map<String, BankInfo> bankInfoMap = new HashMap<>();

        for (ClaimCaseObjectPayment objectPayment : objectPaymentList) {
            if (StringUtils.isNotBlank(objectPayment.getBankInfoId())) {
                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(objectPayment.getBankInfoId());
                if (bankInfo != null) {
                    bankInfoMap.put(objectPayment.getBankInfoId(), bankInfo);
                }
            }
        }
        model.addAttribute("bankInfoMap", JsonTool.genByFastJson(bankInfoMap));

        //预估金额
        List<ClaimCaseObject> claimCaseObjectList = claimCaseObjectService.findByParam(paramMap);
        if(CollectionUtils.isNotEmpty(claimCaseObjectList)){
            BigDecimal objectAppraisalAmount = BigDecimal.ZERO;
            for (ClaimCaseObject Object : claimCaseObjectList) {
                objectAppraisalAmount = objectAppraisalAmount.add(Object.getEstimatedApprovedMoney() == null ? BigDecimal.ZERO : Object.getEstimatedApprovedMoney());
            }
            model.addAttribute("objectAppraisalAmount",objectAppraisalAmount);
        }

        paramMap.clear();
        paramMap.put("baseUserId", claimCase.getBaseUserId());
        List<ClaimCase> claimCaseList = claimCaseService.findByTreatParam(paramMap);
        model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(claimCaseList) ? claimCaseList.size() : 0);

        if (comeFrom == 1) {
            ClaimCaseObjectLog claimCaseObjectLog = new ClaimCaseObjectLog();
            claimCaseObjectLog.setId(Tool.uuid());
            claimCaseObjectLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
            claimCaseObjectLog.setClaimCaseObjectId(claimCaseObject.getId());
            claimCaseObjectLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
            claimCaseObjectLog.setPosition(position);
            claimCaseObjectLog.setStatus(claimCaseObject.getStatus());
            claimCaseObjectLog.setDescription(claimCaseObject.getStatus().startsWith("BAX2") ? "估损开始任务" : "理算开始任务");
            claimCaseObjectLog.setCreator(realName + "-" + userId);
            claimCaseObjectLog.setCreateTime(new Date());
            claimCaseObjectLogService.insertSelective(claimCaseObjectLog);
        }

        Map<String, String> isValidityMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "VALIDITY_CERTIFICATE_INSCODE");
        if (isValidityMap.containsKey(claimCase.getInsCode())) {
            model.addAttribute("isValidity", true);
        }
        Map<String, Object> enumMapByParentCode = new HashMap<>();
        if (claimCaseObject.getCategory() == 1) {
            enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("1");
            model.addAttribute("enumMapByParentCode", enumMapByParentCode);
            return "claimCaseObject/objectGatherDetailJD";
        }

        enumMapByParentCode = EstimateInventoryEnum.getEnumMapByParentCode("2");
        model.addAttribute("enumMapByParentCode", enumMapByParentCode);
        return "claimCaseObject/objectGatherGoodsDetailJD";
    }

    /**
     * 获取签署链接
     * @param claimCaseObjectId
     * @param model
     * @return
     */
    @RequestMapping("getSigningLink")
    public String getSigningLink(String claimCaseObjectId, Model model) {

        if (StringUtils.isBlank(claimCaseObjectId)) {
            model.addAttribute("error", "缺少损失id，请刷新后重试！！");
            return "claimCaseObject/shareLink";
        }

        String userId = ShiroSessionUtil.getLoginSession().getId();
        String userName = ShiroSessionUtil.getLoginSession().getRealName();

        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);

        if (claimCaseObject == null) {
            model.addAttribute("error", "估损单缺少，请重新登录后重试！");
            return "claimCaseObject/shareLink";
        }

        if (!claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX39.getCode()) && !claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX42.getCode()) && !claimCaseObject.getStatus().equals(ClaimCaseObjectStatusEnum.BAX43.getCode())) {
            model.addAttribute("error", "当前估损单状态发生变更，请刷新后重试！");
            return "claimCaseObject/shareLink";
        }

        if (claimCaseObject.getIsCaseClosed() == 1) {
            model.addAttribute("error", "当前估损单已关闭，请刷新后重试！");
            return "claimCaseObject/shareLink";
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());

        if (claimCase == null) {
            model.addAttribute("error", "估损单对应案件缺少，请联系管理员！");
            return "claimCaseObject/shareLink";
        }

        List<SignTemplatePushVo> param = signTemplatePushLogService.findByParam(new HashMap<String, Object>() {{
            put("claimCaseObjectId", claimCaseObjectId);
        }});
        // 接受为4的记录

        List<SignTemplatePushVo> signTemplatePushVos = param.stream().filter(o -> o.getStatus() == 4).collect(Collectors.toList());


        if (CollectionUtils.isEmpty(signTemplatePushVos)) {
            model.addAttribute("error", "当前估损单状态不为发起签署成功，获取链接失败，请联系管理员！");
            return "claimCaseObject/shareLink";
        }
        List<Map<String, Object>> pushList = new ArrayList<>();

        for (SignTemplatePushVo signTemplatePushVo : signTemplatePushVos) {
            String signatureTaskId = JSONObject.parseObject(signTemplatePushVo.getCallBackJson()).getString("fillUnique");
            String fileName = JSONObject.parseObject(signTemplatePushVo.getJsonData()).getString("fileName");
            if (StringUtils.isBlank(signatureTaskId)) {
                model.addAttribute("error", "估损单没有对应的签署任务，请联系管理员！");
                return "claimCaseObject/shareLink";
            }

            Map<String, Object> pushMap = new HashMap<>();
            Manager manager = managerService.selectByPrimaryKey(claimCaseObject.getAuditer());

            pushMap.put("psnAccount", StringUtils.isBlank(manager.getMobile()) ? manager.getTelephone() : manager.getMobile());
            pushMap.put("signatureTaskId", signatureTaskId);
            pushMap.put("claimCaseObjectId", claimCaseObjectId);
            pushMap.put("fileName", fileName);

            pushList.add(pushMap);
        }
        if (CollectionUtils.isNotEmpty(pushList) && pushList.size() > 1) {
            model.addAttribute("error", "存在多条模板，获取链接失败，请联系管理员！");
            return "claimCaseObject/shareLink";
        }
        String signLink = (String) amqpTemplate.convertSendAndReceive(QueueName.GET_SIGN_LINK, JsonTool.genByFastJson(pushList));

        logger.info("获取签署链接返回数据：" + signLink);

        List<Object> jsonList = new ArrayList<>();

        try {
            // 先尝试判断返回的是JSONObject还是JSONArray
            if (StringUtils.isBlank(signLink)) {
                model.addAttribute("error", "获取签署链接失败，返回数据为空");
                return "claimCaseObject/shareLink";
            }

            // 去除首尾空格并判断第一个字符
            String trimmedSignLink = signLink.trim();

            if (trimmedSignLink.startsWith("{")) {
                // 单个JSONObject，可能是错误信息
                JSONObject singleJsonObject = JSONObject.parseObject(signLink);
                if (!"0".equals(singleJsonObject.getString("ret"))) {
                    model.addAttribute("error", singleJsonObject.getString("msg"));
                    return "claimCaseObject/shareLink";
                }
                // 如果是成功的单个对象，也需要处理
                JSONObject data = singleJsonObject.getJSONObject("data").getJSONObject("data");
                Map<String, String> urlMap = new HashMap<>();
                urlMap.put("shortUrl", data.getString("shortUrl"));
                urlMap.put("fileName", singleJsonObject.getString("fileName"));
                urlMap.put("personName", singleJsonObject.getString("personName"));
                jsonList.add(urlMap);

            } else if (trimmedSignLink.startsWith("[")) {
                // JSONArray，正常的多个结果
                JSONArray jsonArray = JSONObject.parseArray(signLink);
                for (Object o : jsonArray) {
                    JSONObject jsonObject = JSONObject.parseObject(o.toString());
                    // 检查每个对象的返回状态
                    if (!"0".equals(jsonObject.getString("ret"))) {
                        model.addAttribute("error", jsonObject.getString("msg"));
                        return "claimCaseObject/shareLink";
                    }
                    JSONObject data = jsonObject.getJSONObject("data").getJSONObject("data");
                    Map<String, String> urlMap = new HashMap<>();
                    urlMap.put("shortUrl", data.getString("shortUrl"));
                    urlMap.put("fileName", jsonObject.getString("fileName"));
                    urlMap.put("personName", jsonObject.getString("personName"));
                    jsonList.add(urlMap);
                }
            } else {
                model.addAttribute("error", "返回数据格式异常，请联系管理员");
                return "claimCaseObject/shareLink";
            }

        } catch (Exception e) {
            logger.error("解析签署链接返回数据异常：" + signLink, e);
            model.addAttribute("error", "解析签署链接数据失败，请联系管理员");
            return "claimCaseObject/shareLink";
        }
        model.addAttribute("jsonList", jsonList);
        Map<String,Object> modifyMap = new HashMap<>();

        ClaimCaseLog claimCaseLog = new ClaimCaseLog();
        claimCaseLog.setId(Tool.uuid());
        claimCaseLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
        claimCaseLog.setClaimCaseObjectId(claimCaseObject.getId());
        claimCaseLog.setLevel(3);
        claimCaseLog.setSecurityClassification(100);
        claimCaseLog.setPosition(ShiroSessionUtil.getLoginRoleSession().get(0).getName());
        claimCaseLog.setType(ClaimCaseLogTypeEnum.内部操作估损清单.getCode());
        claimCaseLog.setStatus(claimCaseObject.getStatus());
        claimCaseLog.setDescription("签署链接获取成功");
        claimCaseLog.setCreator(userName + "-" + userId);
        claimCaseLog.setCreateTime(new Date());
        modifyMap.put("claimCaseLog",claimCaseLog);
        claimCaseObjectService.nextSignTask(modifyMap);


        return "claimCaseObject/shareLink";
    }

    /**
     * 保司复核放弃任务
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = {"stopTask", ""})
    public String stopTask(String id) {
        if (StringUtils.isBlank(id)) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }
        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
        if (claimCaseObject == null) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
        }

        int result = claimCaseObjectService.clearInsAudit(claimCaseObject);

        if (result == 0) {
            return JsonBizTool.genJson(ExRetEnum.ERROR_STOPTASK);
        }
        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    /**
     * 获取结果描述
     * @param claimCaseObjectReq
     * @return
     */
    public String getResultDescription(ClaimCaseObjectReq claimCaseObjectReq){


        StringBuilder firstSb = new StringBuilder();

        switch (claimCaseObjectReq.getStatus()) {
            case "BAX21":
                firstSb.append("客服采集暂存了此赔付对象信息：<br><br>");
                break;
            case "BAX22":
            case "BAX25":
                firstSb.append("客服采集提审了此赔付对象信息：<br><br>");
                break;
            case "BAX23":
                firstSb.append("估损复核通过了此赔付对象信息：<br><br>");
                break;
            case "BAX24":
                firstSb.append("估损复核驳回了此赔付对象信息：<br><br>");
                break;
            case "BAX26":
                firstSb.append("估损保司审核通过了此赔付对象信息：<br><br>");
                break;
            case "BAX27":
                firstSb.append("估损保司审核驳回了此赔付对象信息：<br><br>");
                break;
            case "BAX31":
                firstSb.append("客服理算暂存了此赔付对象信息：<br><br>");
                break;
            case "BAX32":
            case "BAX35":
                firstSb.append("客服理算提审了此赔付对象信息：<br><br>");
                break;
            case "BAX33":
                firstSb.append("理算初审通过了此赔付对象信息：<br><br>");
                break;
            case "BAX34":
                firstSb.append("理算初审驳回了此赔付对象信息：<br><br>");
                break;
            case "BAX36":
                firstSb.append("理算保司审核通过了此赔付对象信息：<br><br>");
                break;
            case "BAX37":
                firstSb.append("理算保司审核驳回了此赔付对象信息：<br><br>");
                break;
            default:
                firstSb.append("未知状态：<br><br>");
                break;
        }

        // object字段描述
        StringBuilder objectSb = new StringBuilder();

        // 列表描述
        StringBuilder listSb = new StringBuilder();

        // 列表描述
        StringBuilder bankListSb = new StringBuilder();


        for (Field declaredField : claimCaseObjectReq.getClass().getDeclaredFields()) {
            try {
                String declaredFieldName = declaredField.getName();
                declaredField.setAccessible(true);

                if("claimCaseObjectAssessmentList".equals(declaredFieldName)) {
                    List<ClaimCaseObjectAssessment> assessmentList = claimCaseObjectAssessmentService.findByClaimCaseObjectId(claimCaseObjectReq.getId());
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(assessmentList)) {
                        for (ClaimCaseObjectAssessment assessment : assessmentList) {
                            listSb.append(EstimateInventoryEnum.codeToName(assessment.getCode())+"项目：<br>");

                            for (Field assessField : assessment.getClass().getDeclaredFields()) {
                                assessField.setAccessible(true);
                                String assessFieldName = assessField.getName();
                                if (!"id".equals(assessFieldName) && !"claimCaseId".equals(assessFieldName)&& !"claimCaseObjectId".equals(assessFieldName)
                                        && !"code".equals(assessFieldName) && !"claimCaseNo".equals(assessFieldName) && !"type".equals(assessFieldName)
                                        && !"category".equals(assessFieldName) && !"createTime".equals(assessFieldName)) { // code不展示
                                    if (assessField.get(assessment) != null) {
                                        listSb.append(ObjectAssessmentChineseEnum.codeToName(assessFieldName) + "：" + assessField.get(assessment) + "<br>");
                                    }
                                }
                            }
                            listSb.append("<br>");
                        }
                    }
                } else if("claimCaseObjectPaymentList".equals(declaredFieldName)) {
                    List<ClaimCaseObjectPayment> paymentList = claimCaseObjectPaymentService.findByClaimCaseObjectId(claimCaseObjectReq.getId());
                    if (CollectionUtils.isNotEmpty(paymentList)) {
                        for (ClaimCaseObjectPayment assessment : paymentList) {
                            for (Field assessField : assessment.getClass().getDeclaredFields()) {
                                assessField.setAccessible(true);
                                String assessFieldName = assessField.getName();
                                if (!"未知".equals(ObjecPaymentChineseEnum.codeToName(assessFieldName))) {
                                    if (assessField.get(assessment) != null) {
                                        String value = assessField.get(assessment).toString();
                                        if ("payObjectType".equals(assessFieldName) && StringUtils.isNotBlank(value)) {
                                            value = "1".equals(value) ? "单位" : "个人";
                                        } else if ("bankInfoId".equals(assessFieldName) && StringUtils.isNotBlank(value)) {
                                            BankInfo bankInfo = bankInfoService.selectByPrimaryKey(value);
                                            value = bankInfo.getBankName();
                                        }
                                        bankListSb.append(ObjecPaymentChineseEnum.codeToName(assessFieldName) + "：" + value + "<br>");
                                    }
                                }
                            }
                            bankListSb.append("<br>");
                        }
                    }
                } else {
                    if (!"id".equals(declaredFieldName) && !"claimCaseId".equals(declaredFieldName) && !"auditer".equals(declaredFieldName)
                            && !"insAuditer".equals(declaredFieldName) && !"status".equals(declaredFieldName) && !"creator".equals(declaredFieldName)
                            && !"createTime".equals(declaredFieldName)) {
                        if (declaredField.get(claimCaseObjectReq) != null) {
                            // 时间转换
                            if ("firstRegistrationTime".equals(declaredFieldName) || "lossAssessmentTime".equals(declaredFieldName)) {
                                objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName)+"："+ DateUtils.format((Date)declaredField.get(claimCaseObjectReq))+"<br>");
                            } else if ("type".equals(declaredFieldName)) {
                                objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName)+"："+ ((Integer)declaredField.get(claimCaseObjectReq) == 1 ? "骑手": "三者")+"<br>");
                            } else if ("category".equals(declaredFieldName)) {
                                objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName)+"："+ ((Integer)declaredField.get(claimCaseObjectReq) == 1 ? "人伤": (Integer)declaredField.get(claimCaseObjectReq) == 2 ? "物损":"机动车")+"<br>");
                            } else if ("bankInfoId".equals(declaredFieldName) && StringUtils.isNotBlank(declaredField.get(claimCaseObjectReq).toString())) {
                                BankInfo bankInfo = bankInfoService.selectByPrimaryKey(declaredField.get(claimCaseObjectReq).toString());
                                objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName)+"："+ bankInfo.getBankName() +"<br>");
                            }  else {
                                objectSb.append(ObjectChineseEnum.codeToName(declaredFieldName)+"："+ declaredField.get(claimCaseObjectReq)+"<br>");
                            }
                        }
                    }
                }

            } catch (IllegalAccessException e) {
                return "-1";
            }
        }

        firstSb.append(objectSb);
        firstSb.append("<br/>");
        firstSb.append(listSb);
        firstSb.append("<br/>");
        firstSb.append(bankListSb);

        return firstSb.toString();
    }


    /**
     * 判断是否为新流程可操作人的案件
     * @param params
     * @return
     */
    @RequestMapping("checkIsNewCaseProcess")
    @ResponseBody
    public String checkIsNewCaseProcess(){
        try {
            String managerId = ShiroSessionUtil.getLoginSession().getId();
            if(StringUtils.isBlank(managerId)){
                return JsonBizTool.genJson("-1","请刷新页面重试");
            }

            String allowUserId = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "TAGGER_CAR_ALLOW_USER_ID");
            if(StringUtils.isNotBlank(allowUserId) && !allowUserId.contains(managerId)){
                return JsonBizTool.genJson("-1","任务已被其他人获取");
            }

            return JsonBizTool.genJson(ExRetEnum.SUCCESS);
        }catch (Exception e){
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.ERROR_SYSTEMERROR);
        }
    }

    /**
     *  协议退回
     * @param claimCaseObjectId
     * @param reason
     * @param model
     * @return
     */
    @RequestMapping("agreementWithdrawn")
    public String agreementWithdrawn(String claimCaseObjectId,String reason, Model model){
        if(StringUtils.isBlank(claimCaseObjectId)){
            model.addAttribute("error","缺少损失id，请刷新后重试！！");
            return "claimCaseObject/agreementWithdrawn";
        }

        model.addAttribute("id",claimCaseObjectId);
        return "claimCaseObject/agreementWithdrawn";

    }

    @RequestMapping(value = "agreementWithdrawnApi")
    @ResponseBody
    public String agreementWithdrawnApi(String claimCaseObjectId,String reason){
        try {
            Map<String,Object> mqMap = new HashMap<>();
            mqMap.put("claimCaseObjectId",claimCaseObjectId);
            mqMap.put("reason",reason);

            String returnJson = (String) amqpTemplate.convertSendAndReceive(QueueName.E_SIGN_AGREEMENT_WITHDRAWN, JsonTool.genByFastJson(mqMap));

            JSONObject jsonObject = JSONObject.parseObject(returnJson);
            String  ret = jsonObject.getString("ret");
            String  msg = jsonObject.getString("msg");

            return JsonBizTool.genJson(ret,msg);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.ERROR_SYSTEMERROR);
        }

    }


}
