package kd.beijingRoastDuck.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import kd.beijingRoastDuck.service.ClaimCaseObjectService;
import kd.beijingRoastDuck.service.ClaimCaseService;
import kd.beijingRoastDuck.service.SignTemplatePushLogService;
import kd.beijingRoastDuck.service.SignTemplateService;
import kd.beijingRoastDuck.util.ShiroSessionUtil;
import kd.beijingRoastDuck.vo.SignTemplatePushVo;
import kd.common.tool.AliOssToolV3;
import kd.common.tool.JsonBizTool;
import kd.entity.ClaimCase;
import kd.entity.ClaimCaseObject;
import kd.entity.SignTemplate;
import kd.entity.SignTemplatePushLog;
import kd.main.common.ClaimCaseObjectStatusEnum;
import kd.main.common.ExRetEnum;
import kd.main.common.QueueName;
import kd.main.elm.SignTemplateOperateLogMqVo;
import kd.main.support.signTask.SignPushTaskReq;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: hoomik
 * @description: 签署任务
 * @date: 2024/11/07 14:22
 * @param 
 * @return 
 */
@Controller
@RequestMapping(value = "/signTemplateController")
public class SignTemplateController {

    private final static Logger logger = LoggerFactory.getLogger(SignTemplateController.class.getName());
    
    @Autowired
    private SignTemplatePushLogService signTemplatePushLogService;
    
    @Autowired
    private ClaimCaseObjectService claimCaseObjectService;
    
    @Autowired
    private ClaimCaseService claimCaseService;
    
    @Autowired
    private SignTemplateService signTemplateService;

    @Autowired
    private AmqpTemplate amqpTemplate;

/**
 * @author: hoomik
 * @description: 签署任务校验
 * @date: 2024/11/07 14:23
 * @param pushLogId
 * @return 
 */
    @RequestMapping("signDetail4log")
    public String signDetail4Log(@RequestParam(name = "pushLogId") String pushLogId, @RequestParam(name = "isCanPush",defaultValue = "false") String isCanPush, Model model){
        SignTemplatePushLog signTemplatePushLog = signTemplatePushLogService.selectByPrimaryKey(pushLogId);
        if(signTemplatePushLog == null){
            model.addAttribute("error","推送签字任务日志不存在，请刷新后重试！！");
            return "claimCaseObject/startSignTask";
        }

        if("true".equals(isCanPush) && signTemplatePushLog.getStatus()!=0 && signTemplatePushLog.getStatus()!=3 && signTemplatePushLog.getStatus()!=5 && signTemplatePushLog.getStatus()!=6){
            model.addAttribute("error","推送签字任务日志状态有误，请刷新后重试！！");
            return "claimCaseObject/startSignTask";
        }

        ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(signTemplatePushLog.getClaimCaseObjectId());
        if(claimCaseObject == null){
            model.addAttribute("error","估损单缺少，请重新登录后重新发起签字任务！");
            return "claimCaseObject/startSignTask";
        }
        if(!Arrays.asList(ClaimCaseObjectStatusEnum.BAX38.getCode(), ClaimCaseObjectStatusEnum.BAX39.getCode(), ClaimCaseObjectStatusEnum.BAX41.getCode()).contains(claimCaseObject.getStatus())){
            model.addAttribute("error","当前估损单状态发生变更，请刷新后重试！");
            return "claimCaseObject/startSignTask";
        }

        SignTemplate signTemplate = signTemplateService.selectByPrimaryKey(signTemplatePushLog.getSignTemplateId());

        if(signTemplate == null){
            model.addAttribute("error","模板已删除，不能重新发起签字任务！");
            return "claimCaseObject/startSignTask";
        }

        ClaimCase claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseObject.getClaimCaseId());

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.clear();
        paramMap.put("baseUserId", claimCase.getBaseUserId());
        List<ClaimCase> claimCaseList = claimCaseService.findByTreatParam(paramMap);
        model.addAttribute("historyCaseSize", org.apache.commons.collections.CollectionUtils.isNotEmpty(claimCaseList) ? claimCaseList.size() : 0);

        model.addAttribute("claimCase", claimCase);
        model.addAttribute("claimCaseObjectId", claimCaseObject.getId());


        Map<String,Map<String,Object>> signTemplateShow = new LinkedHashMap<>();
        Map<String,Object> resultMap = new HashMap<>();

        String privateUrlOuter = AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, signTemplate.getTemplateObjectId(), 3600 * 1000 * 3);
        resultMap.put("imgUrl",privateUrlOuter);
        resultMap.put("templateName",signTemplate.getTemplateName());
        resultMap.put("templateTitle",signTemplate.getTemplateTitle());


        SignPushTaskReq signPushTaskReq = JSON.parseObject(signTemplatePushLog.getJsonData(), SignPushTaskReq.class);

        //组件数据组装
        Map<String, String> valueMap = signPushTaskReq.getComponents().stream().collect(Collectors.toMap(o -> o.getComponentKey(), o -> o.getComponentValue()));
        JSONArray jsonArray = new JSONArray();
        if(StringUtils.isNotBlank(signTemplate.getJsonData())){
            JSONArray fieldJson = JSONArray.parseArray(signTemplate.getJsonData());
            for(int i=0;i<fieldJson.size();i++){
                JSONObject jsonObject = fieldJson.getJSONObject(i);
                String field = jsonObject.getString("field");
                jsonObject.put("value",valueMap.get(field));
                jsonArray.add(jsonObject);
            }
        }
        resultMap.put("initValue",jsonArray);
        //签字人信息
        resultMap.put("personInfo",signPushTaskReq.getPersonInfo());

        signTemplateShow.put(signTemplatePushLog.getSignTemplateId(),resultMap);
        model.addAttribute("signTemplateShow",signTemplateShow);

        model.addAttribute("isCanPush",isCanPush);

        model.addAttribute("signTemplatePushLogId",signTemplatePushLog.getId());

        return "claimCaseObject/startSignTask";
    }
/**
 * @author: hoomik
 * @description: 签署任务关闭
 * @date: 2024/11/07 14:24
 * @param id
 * @return 
 */
    @RequestMapping("closePushLog")
    @ResponseBody
    public String closePushLog(String id){
        try {
            if(StringUtils.isBlank(id)){
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
            }
            SignTemplatePushLog signTemplatePushLogUpdate = new SignTemplatePushLog();
            signTemplatePushLogUpdate.setId(id);
            signTemplatePushLogUpdate.setStatus(-1);


            int i = signTemplatePushLogService.updateByPrimaryKeySelective(signTemplatePushLogUpdate);
        }catch (Exception e){
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.ERROR_SYSTEMERROR);
        }
        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }


    /**
     * @param claimCaseObjectId,operationType
     * @return
     * @author: YangHz
     * @description: 协议重发/重新发起前置搜索
     * @date: 2025/08/04 13:32
     */
    @RequestMapping("reSubmit")
    public String detailSignPushLog(@RequestParam(name = "claimCaseObjectId") String claimCaseObjectId, @RequestParam(name = "operationType") String operationType, Model model) {
        if (StringUtils.isBlank(claimCaseObjectId)) {
            model.addAttribute("error", "缺少损失id，请刷新后重试！！");
            return "claimCaseObject/startSignTask";
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("claimCaseObjectId", claimCaseObjectId);
        List<SignTemplatePushVo> signTemplatePushLogList = signTemplatePushLogService.findByParam(paramMap);
        model.addAttribute("signTemplatePushLogList", signTemplatePushLogList);

        // 筛选未关闭的pushLog
        if (CollectionUtils.isEmpty(signTemplatePushLogList)) {
            model.addAttribute("error", "未找到有效数据，请刷新后重试！！");
            return "claimCaseObject/startSignTask";
        }
        List<SignTemplatePushVo> signTemplatePushVos = signTemplatePushLogList.stream().filter(o -> o.getStatus() != -1 && o.getStatus() != 7).collect(Collectors.toList());

        if (signTemplatePushVos.size() > 1) {
            model.addAttribute("error", "数据异常:存在多个有效数据，请联系管理员!!");
            return "claimCaseObject/startSignTask";
        }

        SignTemplatePushVo signTemplatePushVo = signTemplatePushVos.get(0);
        model.addAttribute("operationType", operationType);
        return signDetail4Log(signTemplatePushVo.getId(), "true", model);
    }

    /**
     * 增加e签宝操作日志
     *
     * @param signTemplateOperateLog
     */
    @RequestMapping("addOperationLog")
    @ResponseBody
    public void addOperationLog(@RequestBody SignTemplateOperateLogMqVo signTemplateOperateLog) {
        String userId = ShiroSessionUtil.getLoginSession().getId();
        String userName = ShiroSessionUtil.getLoginSession().getRealName();
        signTemplateOperateLog.setCreator(userName + "-" + userId);

        logger.info("======创建添加e签宝操作日志MQ消息对象,{}=======", JSON.toJSONString(signTemplateOperateLog));
        // 直接发送MQ消息
        amqpTemplate.convertAndSend(QueueName.KD_SIGN_TEMPLATE_OPERATE_LOG_ADD, JSON.toJSONString(signTemplateOperateLog));
    }


}
