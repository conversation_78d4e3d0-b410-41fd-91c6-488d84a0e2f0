package kd.beijingRoastDuck.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import kd.beijingRoastDuck.service.*;
import kd.beijingRoastDuck.util.Tool;
import kd.common.context.RedisCommonKeyEnum;
import kd.common.tool.JsonBizTool;
import kd.common.tool.JsonTool;
import kd.common.tool.RedisTool3;
import kd.entity.ClaimCaseLog;
import kd.entity.ClaimCaseObject;
import kd.entity.SignTemplate;
import kd.entity.SignTemplatePushLog;
import kd.main.common.ClaimCaseLogTypeEnum;
import kd.main.common.ClaimCaseObjectStatusEnum;
import kd.main.common.ExRetEnum;
import kd.main.common.QueueName;
import kd.main.elm.SignTemplateOperateLogMqVo;
import kd.main.support.signTask.PersonInfo;
import kd.main.support.signTask.SignPushTaskReq;
import kd.main.util.HttpToolV3;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

/**
 * @author: hoomik
 * @description: 签字任务
 * @date: 2024/11/07 14:12
 * @param
 * @return
 */
@Controller
@RequestMapping("/api/signTemplateApi")
public class SignTemplateApi {

    private final static Logger logger = LoggerFactory.getLogger(SignTemplateApi.class);

    @Autowired
    private SignTemplateService signTemplateService;

    @Autowired
    private ClaimCaseObjectService claimCaseObjectService;

    @Autowired
    private SignTemplatePushLogService signTemplatePushLogService;

    @Autowired
    private ClaimCaseLogService claimCaseLogService;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private ESignService eSignService;
/**
 * @author: hoomik
 * @description: 签字推送任务
 * @date: 2024/11/07 14:13
 * @param json 组装好的推送数据
 * @return
 */
    @RequestMapping(value = "signPush")
    @ResponseBody
    public String signPush(@RequestBody String json) {
        logger.info(">>>>>>>>>方法/api/signTemplateApi/signPush，开始处理签字推送任务，json：{}", json);
        ClaimCaseObject claimCaseObject = null;
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String claimCaseObjectId = jsonObject.getString("claimCaseObjectId");
            SignPushTaskReq signPushTaskReq = JSON.parseObject(jsonObject.getString("signPushTaskReq"), SignPushTaskReq.class);
            String psnMobile = jsonObject.getString("psnMobile");
            if (StringUtils.isBlank(claimCaseObjectId)) {
                return JsonBizTool.genJson("-1", "估损对象id为空");
            }
            claimCaseObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);
            if (claimCaseObject == null) {
                return JsonBizTool.genJson("-1", "估损对象不存在");
            }
            if (StringUtils.isBlank(signPushTaskReq.getTemplateId())) {
                return JsonBizTool.genJson("-1", "请求数据中模板id不存在");
            }
            SignTemplate signTemplate = signTemplateService.selectByPrimaryKey(signPushTaskReq.getTemplateId());
            if (signTemplate == null) {
                return JsonBizTool.genJson("-1", "请求数据的签字模板不存在");
            }
            if (CollectionUtils.isEmpty(signPushTaskReq.getPersonInfo())) {
                return JsonBizTool.genJson("-1", "请求签字时至少要有一个签署人信息");
            }
            boolean isSignPerson = false;
            int index = 1;
            for (PersonInfo personInfo : signPushTaskReq.getPersonInfo()) {
                if (StringUtils.isAnyBlank(personInfo.getPersonName(), personInfo.getPersonIdNumber(), personInfo.getPersonIdType(), personInfo.getPersonPhone())) {
                    isSignPerson = true;
                    break;
                }
                personInfo.setSeqNo(index++);
            }
            if (isSignPerson) {
                return JsonBizTool.genJson("-1", "签署人信息不全");
            }

            String signCallBackUrl = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "SIGN_CALL_BACK_URL");
            if (StringUtils.isBlank(signCallBackUrl)) {
                return JsonBizTool.genJson("-1", "未配置签字回调地址，请联系管理员！！！");
            }
            String signPushUrl = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "SIGN_PUSH_URL");
            if (StringUtils.isBlank(signPushUrl)) {
                return JsonBizTool.genJson("-1", "未配置签字中台地址，请联系管理员！！！");
            }

            String insCode2EntCode = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "SIGN_ENT_NAME_4_INSCODE", claimCaseObject.getInsCode());
            if (StringUtils.isBlank(insCode2EntCode)) {
                insCode2EntCode = "KD";
            }

            Date date = new Date();
            String signTemplatePushLogId = Tool.uuid();

            //补充推送数据
            signPushTaskReq.setRequiredCheck("false");
            signPushTaskReq.setFileTitle(signTemplate.getTemplateTitle());
            signPushTaskReq.setFileName(signTemplate.getTemplateName());
            signPushTaskReq.setEntCode(insCode2EntCode);
            signPushTaskReq.seteTemplateId(signTemplate.geteTemplateId());
            signPushTaskReq.setCallBackUrl(signCallBackUrl + "?id=" + signTemplatePushLogId);
            signPushTaskReq.setPersonCount(signPushTaskReq.getPersonInfo().size());
            signPushTaskReq.setPsnMobile(psnMobile);

            //组装估损项推送签字日志
            SignTemplatePushLog signTemplatePushLog = new SignTemplatePushLog();
            signTemplatePushLog.setId(signTemplatePushLogId);
            signTemplatePushLog.setClaimCaseNo(claimCaseObject.getClaimCaseNo());
            signTemplatePushLog.setClaimCaseObjectId(claimCaseObject.getId());
            signTemplatePushLog.setCreateTime(date);
            signTemplatePushLog.setCreator(StringUtils.isBlank(jsonObject.getString("creator")) ? "-1" : jsonObject.getString("creator"));
            signTemplatePushLog.setJsonData(JSON.toJSONString(signPushTaskReq));
            signTemplatePushLog.setSignTemplateId(signTemplate.getId());
            //默认推送失败
            signTemplatePushLog.setStatus(0);

            //调用签字中台
            String responseStr = HttpToolV3.callApiByPostStr(true, signPushUrl, JSON.toJSONString(signPushTaskReq));
            logger.info(">>>>>>>>>方法/api/signTemplateApi/signPush，调用签字中台接口开始，json：{},推送地址{},返回结果", JSON.toJSONString(signPushTaskReq), signPushUrl, responseStr);
            JSONObject resultJsonObject = JSON.parseObject(responseStr);
            if ("0".equals(resultJsonObject.getString("ret"))) {
                signTemplatePushLog.setStatus(1);
            } else {
                signTemplatePushLog.setErrorMsg(responseStr);
            }

            //无论是否调用成功都进行数据入库
            signTemplatePushLogService.insertSelective(signTemplatePushLog);

        } catch (Exception e) {
            e.printStackTrace();
            if (claimCaseObject != null) {
                try {
                    ClaimCaseLog claimCaseLog = new ClaimCaseLog();
                    claimCaseLog.setId(Tool.uuid());
                    claimCaseLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
                    claimCaseLog.setClaimCaseObjectId(claimCaseObject.getId());
                    claimCaseLog.setLevel(1);
                    claimCaseLog.setSecurityClassification(190);
                    claimCaseLog.setType(ClaimCaseLogTypeEnum.系统操作记录.getCode());
                    claimCaseLog.setReqData(json);
                    claimCaseLog.setStatus(claimCaseObject.getStatus());
                    claimCaseLog.setDescription("签字任务发起异常");
                    claimCaseLog.setCreator("-1");
                    claimCaseLog.setCreateTime(new Date());
                    claimCaseLogService.insertSelective(claimCaseLog);
                } catch (Exception exception) {
                    e.printStackTrace();
                }
            }
            return JsonBizTool.genJson(ExRetEnum.ERROR_SYSTEMERROR);
        }
        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }



/**
 * @author: hoomik
 * @description: 接收签字任务返回 填充数据
 * @date: 2024/12/10 14:14
 * @param signCenterId,type 签字标识和类型（查看填充后数据，查看签字完成后数据）
 * @return
 */
    @RequestMapping(value = "signGetFileUrl")
    @ResponseBody
    public String signGetFileUrl(String signCenterId,String type) {
        logger.info(">>>>>>>>>方法/api/signTemplateApi/signGetFileUrl，开始获取签字中台文件URL,唯一标识：{}", signCenterId);
        try {
            if (StringUtils.isBlank(signCenterId)) {
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
            }


            String responseStr ="";
            if(type.equals("cktchsj")){

                String signGetFileUrl = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "SIGN_GET_FILE_URL");
                if (StringUtils.isBlank(signGetFileUrl)) {
                    return JsonBizTool.genJson("-1", "未配置签字中台获取文件地址，请联系管理员！！！");
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("taskId",signCenterId);
                responseStr = HttpToolV3.callApiByPostStr(true, signGetFileUrl, jsonObject.toJSONString());
                logger.info(">>>>>>>>>方法/api/eSignApi/findFillFile，获取签字中台文件URL,唯一标识：{},推送地址{},返回结果", signCenterId, signGetFileUrl, responseStr);
            }else {
                String signGetFileUrl = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "SIGN_GET_SIGN_URL");
                if (StringUtils.isBlank(signGetFileUrl)) {
                    return JsonBizTool.genJson("-1", "未配置签字中台获取文件地址，请联系管理员！！！");
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("taskId",signCenterId);
                responseStr = HttpToolV3.callApiByPostStr(true, signGetFileUrl, jsonObject.toJSONString());
                logger.info(">>>>>>>>>方法/api/eSignApi/findSignFile，获取签字中台文件URL,唯一标识：{},推送地址{},返回结果", signCenterId, signGetFileUrl, responseStr);
            }

            //调用签字中台获取文件url

            try {
                return responseStr;
            }catch (Exception e){
                e.printStackTrace();
                return JsonBizTool.genJson("-1","签字中台返回结果有问题，请联系管理员");
            }

        } catch (Exception e) {
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.ERROR_SYSTEMERROR);
        }

    }

    /**
     * 获取签署链接
     * @param json
     * @return
     */
    @RequestMapping(value = "getSigningLink")
    @ResponseBody
    public String getSigningLink(@RequestBody String json) {
        ClaimCaseObject claimCaseObject = null;
        logger.info(">>>>>>>>>方法/api/signTemplateApi/getSigningLink，开始获取签署链接，json：{}", json);
        try {
            JSONArray objects = JSONObject.parseArray(json);
            List<Object> resultList = new ArrayList<>();

            for (Object object : objects) {
                JSONObject jsonObject = JSONObject.parseObject(object.toString());
                String claimCaseObjectId = jsonObject.getString("claimCaseObjectId");
                String fileName = jsonObject.getString("fileName");
                if (StringUtils.isBlank(claimCaseObjectId)) {
                    return JsonBizTool.genJson("-1", "估损对象id为空");
                }
                claimCaseObject = claimCaseObjectService.selectByPrimaryKey(claimCaseObjectId);
                if (claimCaseObject == null) {
                    return JsonBizTool.genJson("-1", "估损对象不存在");
                }

                String signPushUrl = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "GET_SIGN_LINK");
                if (StringUtils.isBlank(signPushUrl)) {
                    return JsonBizTool.genJson("-1", "未配置签字中台地址，请联系管理员！！！");
                }

                //调用签字中台
                String responseStr = HttpToolV3.callApiByPostStr(true, signPushUrl, object.toString());
                logger.info(">>>>>>>>>方法/api/signTemplateApi/getSigningLink，调用签字中台接口开始，json：{},推送地址{},返回结果{}", object.toString(), signPushUrl, responseStr);

                JSONArray jsonArray = JSONObject.parseArray(responseStr);
                for (Object responseJson : jsonArray) {
                    JSONObject resultJsonObject = JSON.parseObject(responseJson.toString());
                    resultJsonObject.put("fileName", fileName);
                    if ("0".equals(resultJsonObject.getString("ret"))) {
                        resultList.add(resultJsonObject);
                    } else {
                        return JsonBizTool.genJson("-1", "中台获取链接失败");
                    }
                }
            }

            System.out.println(JsonTool.genByFastJson(resultList));

            return JsonTool.genByFastJson(resultList);
        } catch (Exception e) {
            e.printStackTrace();
            try {
                ClaimCaseLog claimCaseLog = new ClaimCaseLog();
                claimCaseLog.setId(Tool.uuid());
                claimCaseLog.setClaimCaseId(claimCaseObject.getClaimCaseId());
                claimCaseLog.setClaimCaseObjectId(claimCaseObject.getId());
                claimCaseLog.setLevel(1);
                claimCaseLog.setSecurityClassification(190);
                claimCaseLog.setType(ClaimCaseLogTypeEnum.系统操作记录.getCode());
                claimCaseLog.setReqData(json);
                claimCaseLog.setStatus(claimCaseObject.getStatus());
                claimCaseLog.setDescription("获取签署链接失败");
                claimCaseLog.setCreator("-1");
                claimCaseLog.setCreateTime(new Date());
                claimCaseLogService.insertSelective(claimCaseLog);
            } catch (Exception exception) {
                e.printStackTrace();
            }
            return JsonBizTool.genJson(ExRetEnum.ERROR_SYSTEMERROR);
        }
    }

    /**
     * 协议撤回
     * @param json
     * @return
     */
    @RequestMapping(value = "agreementWithdrawn")
    @ResponseBody
    public String agreementWithdrawn(@RequestBody String json){
        logger.info(">>>>>>>>>方法/api/signTemplateApi/agreementWithdrawn，开始协议撤回，json：{}", json);
        String claimCaseNo = null;
        String signatureTaskId = null;
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String id = jsonObject.getString("claimCaseObjectId");
            String reason = jsonObject.getString("reason");

            ClaimCaseObject claimCaseObject = claimCaseObjectService.selectByPrimaryKey(id);
            if (claimCaseObject == null){
                return JsonBizTool.genJson("-1","案件对象不存在");
            }
            claimCaseNo = claimCaseObject != null ? claimCaseObject.getClaimCaseNo() : "";

            String status = "-1,7";
            //获取签署id 查询签字推送日志排除状态为-1(关闭)的和7(签署已撤销)
            List<SignTemplatePushLog> signTemplatePushLogList = signTemplatePushLogService.findByClaimCaseObjectIdAndNoStatus(claimCaseObject.getId(),status);
            if (signTemplatePushLogList.isEmpty() || signTemplatePushLogList.size() > 1){
                return JsonBizTool.genJson("-1","签字推送日志表异常");
            }
            SignTemplatePushLog signTemplatePushLog = signTemplatePushLogList.get(0);
            //协议过期不允许撤回
            if (8 == signTemplatePushLog.getStatus()){
                return JsonBizTool.genJson("-1","协议过期不允许撤回");
            }
            if (StringUtils.isBlank(signTemplatePushLog.getCallBackJson())){
                return JsonBizTool.genJson("-1","签署id不能为空");
            }

            JSONObject jsonObject1 = JSONObject.parseObject(signTemplatePushLog.getCallBackJson());
            signatureTaskId = jsonObject1.getString("fillUnique");

            Map<String,Object> pushMap = new HashMap<>();
            pushMap.put("signatureTaskId",signatureTaskId);
            pushMap.put("reason",reason);
            pushMap.put("claimCaseObjectId",claimCaseObject.getId());

            String signPushUrl = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "AGREEMENT_WITHDRAWN");
            if (StringUtils.isBlank(signPushUrl)) {
                return JsonBizTool.genJson("-1", "未配置签字中台地址，请联系管理员！！！");
            }

            //调用签字中台
            String responseStr = HttpToolV3.callApiByPostStr(true, signPushUrl, JsonTool.genByFastJson(pushMap));
            logger.info(">>>>>>>>>方法/api/signTemplateApi/agreementWithdrawn，调用签字中台接口开始，json：{},推送地址{},返回结果{}", pushMap, signPushUrl, responseStr);
            //请求e签宝成功
            ClaimCaseObject renewalClaimCaseObject = new ClaimCaseObject();
            renewalClaimCaseObject.setId(claimCaseObject.getId());
            renewalClaimCaseObject.setStatus(ClaimCaseObjectStatusEnum.BAX42.getCode());
            renewalClaimCaseObject.setModifyTime(new Date());
            claimCaseObjectService.updateByPrimaryKeySelective(renewalClaimCaseObject);


            JSONObject resultJsonObject = JSON.parseObject(responseStr);

            String ret = resultJsonObject.getString("code");
            String msg = resultJsonObject.getString("message");

            String description = null;
            if ("0".equals(ret)) {
                description = "协议撤回成功";
            } else {
                description = StringUtils.isNotBlank(msg) ? msg: "协议撤回失败";
            }


            SignTemplateOperateLogMqVo mqVo = new SignTemplateOperateLogMqVo();
            mqVo.setClaimCaseNo(claimCaseNo);
            mqVo.setSignatureTaskId(signatureTaskId);
            mqVo.setOperateType("协议撤回");
            mqVo.setDescription(description);
            mqVo.setCreator("");
            //添加操作日志
            amqpTemplate.convertAndSend(QueueName.KD_SIGN_TEMPLATE_OPERATE_LOG_ADD, JSON.toJSONString(mqVo));

            ClaimCaseObject updateClaimCaseObject = new ClaimCaseObject();
            if ("0".equals(ret)) {

                int i = eSignService.agreementWithdrawnData(claimCaseObject.getId());

                return JsonBizTool.genJson("0",  "协议撤回成功");
            } else {
                updateClaimCaseObject.setId(claimCaseObject.getId());
                updateClaimCaseObject.setStatus(ClaimCaseObjectStatusEnum.BAX43.getCode());
                updateClaimCaseObject.setModifyTime(new Date());
                claimCaseObjectService.updateByPrimaryKeySelective(updateClaimCaseObject);

                return JsonBizTool.genJson("-1", StringUtils.isNotBlank(msg) ? msg: "协议撤回失败");
            }

        }catch (Exception e){

            claimCaseNo = claimCaseNo != null ? claimCaseNo : "";
            SignTemplateOperateLogMqVo mqVo = new SignTemplateOperateLogMqVo();
            mqVo.setClaimCaseNo(claimCaseNo);
            mqVo.setSignatureTaskId(signatureTaskId);
            mqVo.setOperateType("协议撤回");
            mqVo.setDescription("系统异常");
            mqVo.setCreator("");
            //添加操作日志
            amqpTemplate.convertAndSend(QueueName.KD_SIGN_TEMPLATE_OPERATE_LOG_ADD, JSON.toJSONString(mqVo));

            e.printStackTrace();
            return JsonBizTool.genJson("-1","中台返回结果有问题，请联系管理员");
        }
    }

}
