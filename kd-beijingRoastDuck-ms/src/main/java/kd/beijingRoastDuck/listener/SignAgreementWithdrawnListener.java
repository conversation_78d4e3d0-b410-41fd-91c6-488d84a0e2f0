package kd.beijingRoastDuck.listener;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import kd.beijingRoastDuck.api.SignTemplateApi;
import kd.beijingRoastDuck.event.SpringContextHolder;
import kd.main.common.QueueName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@RabbitListener(queues = QueueName.E_SIGN_AGREEMENT_WITHDRAWN)
public class SignAgreementWithdrawnListener {

    private static final Logger logger = LoggerFactory.getLogger(SignAgreementWithdrawnListener.class.getName());

    @RabbitHandler
    public String pushListener(String json, Message msg, Channel channel) throws IOException {
        logger.info("-------->队列{}开始协议撤回，json{}，", QueueName.E_SIGN_AGREEMENT_WITHDRAWN, json);
        String repStr = "";
        try {
            repStr = SpringContextHolder.getBean(SignTemplateApi.class).agreementWithdrawn(json);

            System.out.println(repStr);

            JSONObject jsonObject = JSONObject.parseObject(repStr);
            if ("0".equals(jsonObject.getString("ret"))){
                logger.info("-------->队列{}协议撤回成功，json：{}，返回结果：{}", QueueName.E_SIGN_AGREEMENT_WITHDRAWN, json, repStr);
            }else{
                logger.info("-------->队列{}协议撤回失败，json：{}，返回结果：{}", QueueName.E_SIGN_AGREEMENT_WITHDRAWN, json, repStr);

            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("-------->队列{}系统异常，json：{}，error：{}", QueueName.E_SIGN_AGREEMENT_WITHDRAWN, json, e.getMessage());
        } finally {
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
        }
        return repStr;
    }
}
