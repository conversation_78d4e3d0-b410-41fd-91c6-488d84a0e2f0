<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <title>理算处理详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <!-- 引入公共CSS -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>

    <script src="${ctx}/js/idCardCheck.js" type="text/javascript"></script>

    <script type="text/javascript">

        var viewer = null;

        var rowNum = 0;


        $(function () {

            /*图片展示工具*/
            (function () {
                viewer = new Viewer({
                    activeId: null
                });
            })();


            //可拖动基本信息、查看日志
            const container = document.getElementById('floating-container');
            const boxes = {
                box1: document.getElementById('box1'),
                box2: document.getElementById('box2'),
                box3: document.getElementById('box3')
            };

            const controlButtons = document.querySelectorAll('.control-btn');
            let activeBox = boxes.box1;
            let isDragging = false;
            let offsetX, offsetY;

            // 切换活动浮框
            function setActiveBox(box) {
                activeBox.classList.remove('box-active');
                activeBox = box;
                activeBox.classList.add('box-active');
            }

            // 控制面板按钮事件
            controlButtons.forEach(btn => {
                btn.addEventListener('click', function () {
                    setActiveBox(boxes[this.dataset.target]);
                });
            });

            // 容器拖拽事件
            container.addEventListener('mousedown', function (e) {
                // 只有当点击的是容器或浮框时才触发拖拽
                if (e.target === container || e.target.classList.contains('draggable-box')) {
                    isDragging = true;
                    offsetX = e.clientX - container.getBoundingClientRect().left;
                    offsetY = e.clientY - container.getBoundingClientRect().top;

                    container.style.transition = 'none';
                    Array.from(container.children).forEach(box => {
                        box.classList.add('dragging');
                    });

                    e.preventDefault();
                }
            });

            // 鼠标移动事件
            document.addEventListener('mousemove', function (e) {
                if (!isDragging) return;

                let newLeft = e.clientX - offsetX;
                let newTop = e.clientY - offsetY;

                // 限制位置（防止移出屏幕）
                const maxLeft = window.innerWidth - container.offsetWidth;
                const maxTop = window.innerHeight - container.offsetHeight;

                newLeft = Math.max(0, Math.min(newLeft, maxLeft));
                newTop = Math.max(0, Math.min(newTop, maxTop));

                container.style.left = newLeft + 'px';
                container.style.top = newTop + 'px';
                container.style.right = 'auto';
            });

            // 鼠标释放事件
            document.addEventListener('mouseup', function (e) {
                if (!isDragging) return;
                isDragging = false;

                container.style.transition = 'all 0.3s ease';
                Array.from(container.children).forEach(box => {
                    box.classList.remove('dragging');
                });

                // 判断吸附到左侧还是右侧
                const containerRect = container.getBoundingClientRect();
                const screenCenter = window.innerWidth / 2;
                const containerCenter = containerRect.left + containerRect.width / 2;

                if (containerCenter < screenCenter) {
                    container.style.left = '20px';
                    container.style.right = 'auto';
                } else {
                    container.style.left = 'auto';
                    container.style.right = '20px';
                }
            });

            // 窗口大小变化时重新定位
            window.addEventListener('resize', function () {
                const containerRect = container.getBoundingClientRect();

                if (parseInt(container.style.left) > 0 || container.style.left === '') {
                    container.style.left = '20px';
                    container.style.right = 'auto';
                } else {
                    container.style.left = 'auto';
                    container.style.right = '20px';
                }

                const maxTop = window.innerHeight - container.offsetHeight;
                const currentTop = parseInt(container.style.top || containerRect.top);
                container.style.top = Math.max(0, Math.min(currentTop, maxTop)) + 'px';
            });

            // 点击浮框切换活动状态
            Object.values(boxes).forEach(box => {
                box.addEventListener('click', function () {
                    setActiveBox(this);
                });
            });

            /*点击缩略图*/
            $('body').on('click', '.thumbnail-img', function () {
                if ($(this).hasClass('selected-thumbnail-img')) {
                    return;
                }
                let attrId = $(this).data('fileid');
                $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
                $(this).addClass('selected-thumbnail-img');
                viewer.showImageByFileId(attrId);
                let imgNumber = $(this).data('img-number');
                let offset = imgNumber * 95 - (1050 - 95) / 2;
                $('#thumbnail').scrollTop(offset);
                $(".viewer-canvas").show();
            });

            /*A/D切换影像*/
            $('body').on('click', '.viewer-list > li', function () {
                let attchId = $(this).find('img').attr('alt');
                $('#thumbnail-' + attchId).trigger('click');
                $(".viewer-canvas").show();
            });


            // 为所有折叠菜单添加点击事件
            $('.nav-link[data-toggle="collapse"]').on('click', function (e) {
                e.preventDefault();
                e.stopPropagation(); // 防止事件冒泡到父元素
                var target = $(this).data('target');
                $(target).toggleClass('in');

                // 检查是否使用的是Bootstrap 4+，如果是则使用'show'类
                if ($(target).hasClass('show')) {
                    $(target).removeClass('show');
                } else {
                    $(target).addClass('show');
                }

                var isExpanded = $(target).hasClass('in') || $(target).hasClass('show');
                $(this).attr('aria-expanded', isExpanded);
                $(this).find('i.glyphicon-chevron-right').css('transform', isExpanded ? 'rotate(90deg)' : '');
            });
            // 添加缩略图叉号点击事件
            $('body').on('click', '.thumbnail-close', function (e) {
                e.preventDefault();
                e.stopPropagation();
                var fileId = $(this).data('fileid');
                // 这里可以添加删除图片的逻辑
                console.log('Delete image with fileId: ' + fileId);
                $("#imgArea" + fileId).remove();
                $(".viewer-canvas").hide();
                // 例如：$(this).closest('li').remove();
            });

            // 菜单项点击事件
            $('.nav-list li ul li a').on('click', function (e) {
                e.preventDefault();
                // 这里可以添加点击菜单项后的处理逻辑
                var category = $(this).text().trim();
                console.log('Selected category: ' + category);
                // 可以添加加载对应分类图片的逻辑
            });

            // 初始化支付对象信息
            let bankInfoMap = JSON.parse('${bankInfoMap}');
            var bankInfo = "";
            <#list objectPaymentList as payment>
            rowNum = rowNum + 1;
            var delPaymentDiv = "<span rowNum = " + rowNum + ">收款账户信息" + rowNum + "</span><img src=\"/images/shanchu.svg\" class=\"del-svg-img\" onclick=\"delPaymentRow(this)\">";
            reloadbankInfo("${payment.id}");
            bankInfo = bankInfoMap["${payment.bankInfoId}"];
            $("#select2-totalBankSelect${payment.id}-container .select2-selection__placeholder").html("${payment.bankName}").attr("title", "${payment.bankName}");      // 初始化总行名称
            $("#select2-bankSubbranchSelect${payment.id}-container .select2-selection__placeholder").html(bankInfo.bankName ?? "").attr("title", bankInfo.bankName);      // 初始化支行名称
            $("#bankName${payment.id}").val("${payment.bankName}");
            $("#bankInfoId${payment.id}").val("${payment.bankInfoId}");
            $(".payment-${payment.id}").find(".btn-del-payment").append(delPaymentDiv);
            </#list>

            // 增加校验规则
            $("#paymentCollectArea").on('change', "input[validName='billCollection']", check);
            $(".table-responsive").on('change', "input[validName='billCollection']", check);

            window.addEventListener('scroll', function() {
                const box = document.getElementById('stickyBox');
                if (window.scrollY > 150) { // 滚动超过200px时添加额外样式
                    box.classList.add('fixed-style');
                    $("#stickyBox").next().css("margin-top", "530px");
                } else {
                    box.classList.remove('fixed-style');
                    $("#stickyBox").next().css("margin-top", "0px");
                }

            });
        });


        function addPaymentRow() {
            rowNum = rowNum + 1;
            $("#paymentCollectArea").append(
                '<div class="row payment-' + rowNum + ' paymentCollectAreaRow" data-index="' + rowNum + '">\n' +
                '                                           <input type="hidden" id="claimCaseObjectPaymentId" class="form-control" value="">\n' +
                '                                            <div class="row flex-align-center btn-del-payment" style="padding-left: 4.5%;">\n' +
                '                                                <span rowNum="' + rowNum + '">收款账户信息' + rowNum + '</span>\n' +
                '                                                <img src="/images/shanchu.svg" class="del-svg-img" onclick="delPaymentRow(this)">\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4">\n' +
                '                                                <div class="col-sm-4 text-right">账户性质*</div>\n' +
                '                                                <div class="col-sm-8 flex-align-center">\n' +
                '                                                    <input type="radio" name="payObjectType' + rowNum + '" value="2" id="payObjectPerson' + rowNum + '">\n' +
                '                                                    <label for="payObjectPerson' + rowNum + '">自然人（个人）</label>\n' +
                '                                                    <input type="radio" name="payObjectType' + rowNum + '" value="1" id="payObjectEnt' + rowNum + '">\n' +
                '                                                    <label for="payObjectEnt' + rowNum + '">非自然人（企业）</label>\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4" style="margin: 10px 0px;">\n' +
                '                                                <div class="col-sm-4 text-right">收款人姓名*</div>\n' +
                '                                                <div class="col-sm-8">\n' +
                '                                                    <input validName="billCollection" id="paymentName" type="text"\n' +
                '                                                           class="form-control" data-valid="isEmpty"\n' +
                '                                                           value=""/>\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4">\n' +
                '                                                <div class="col-sm-4 text-right">收款人证件类型*</div>\n' +
                '                                                <div class="col-sm-8">\n' +
                '                                                    <select validName="billCollection"\n' +
                '                                                            class="kp-select2 inputStyle form-control"\n' +
                '                                                            data-valid="none" id="idType">\n' +
                '                                                        <option value="身份证">\n' +
                '                                                            身份证\n' +
                '                                                        </option>\n' +
                '                                                        <option value="其他">\n' +
                '                                                            其他\n' +
                '                                                        </option>\n' +
                '                                                    </select>\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4">\n' +
                '                                                <div class="col-sm-4 text-right">收款人证件号码*</div>\n' +
                '                                                <div class="col-sm-8 ">\n' +
                '                                                    <input validName="billCollection" id="idNum" type="text"\n' +
                '                                                           class="form-control" data-valid="isEmpty"/>\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4">\n' +
                '                                                <div class="col-sm-4 text-right">证件有效期*</div>\n' +
                '                                                <div class="col-sm-8 flex-align-center">\n' +
                '                                                    <div class="col-sm-9 clear-padding" >\n' +
                '                                                        <input validName="billCollection" type="date"\n' +
                '                                                               id="idNumStartDate" class="form-control" value=""\n' +
                '                                                               data-valid="none"/>\n' +
                '                                                    </div>\n' +
                '                                                    <span style="margin: 0px 10px;">-</span>\n' +
                '                                                    <div class="col-sm-9 clear-padding" >\n' +
                '                                                        <select validName="billCollection" id="idNumEndDate"\n' +
                '                                                                class="kp-select2 inputStyle form-control"\n' +
                '                                                                data-valid="none" value="">\n' +
                '                                                            <option value="">请选择</option>\n' +
                '                                                            <option value="5">5年</option>\n' +
                '                                                            <option value="10">10年</option>\n' +
                '                                                            <option value="20">20年</option>\n' +
                '                                                            <option value="-1">长期有效</option>\n' +
                '                                                        </select>\n' +
                '                                                    </div>\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4">\n' +
                '                                                <div class="col-sm-4 text-right">收款人联系电话*</div>\n' +
                '                                                <div class="col-sm-8">\n' +
                '                                                    <input validName="billCollection" id="mobile" type="text"\n' +
                '                                                           class="form-control" data-valid="isEmpty"/>\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4">\n' +
                '                                                <div class="col-sm-4 text-right">收款人银行名称*</div>\n' +
                '                                                <div class="col-sm-8 ">\n' +
                '                                                    <select id="totalBankSelect' + rowNum + '" name="totalBankSelect' + rowNum + '" \n' +
                '                                                       class="js-data-example-ajax" >\n' +
                '                                                    </select>\n' +
                '                                                    <input id="bankName' + rowNum + '" name="bankName' + rowNum + '" type="hidden">\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4">\n' +
                '                                                <div class="col-sm-4 text-right">收款人银行支行*</div>\n' +
                '                                                <div class="col-sm-8">\n' +
                '                                                    <select id="bankSubbranchSelect' + rowNum + '" name="bankSubbranchSelect' + rowNum + '" \n' +
                '                                                       class="js-data-example-ajax" >\n' +
                '                                                    </select>\n' +
                '                                                    <input id="bankInfoId' + rowNum + '" name="bankInfoId' + rowNum + '" type="hidden">\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4">\n' +
                '                                                <div class="col-sm-4 text-right">收款人开户名*</div>\n' +
                '                                                <div class="col-sm-8">\n' +
                '                                                    <input validName="billCollection" id="bankAccount" type="text"\n' +
                '                                                           class="form-control" data-valid="isEmpty"/>\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4">\n' +
                '                                                <div class="col-sm-4 text-right">收款人银行账号*</div>\n' +
                '                                                <div class="col-sm-8 ">\n' +
                '                                                    <input validName="billCollection" id="bankCard" type="text"\n' +
                '                                                           class="form-control" data-valid="isEmpty"/>\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                            <div class="col-sm-4">\n' +
                '                                                <div class="col-sm-4 text-right">应付损失金额 *</div>\n' +
                '                                                <div class="col-sm-8">\n' +
                '                                                    <input validName="billCollection" id="payAmount" type="text"\n' +
                '                                                           class="form-control" data-valid="isEmpty isNumberAndDecimalPoint"/>\n' +
                '                                                </div>\n' +
                '                                            </div>\n' +
                '                                        </div>');
            reloadbankInfo(rowNum);
            for (let i = 1; i <= rowNum; i++) {
                // 为选择框添加change事件处理器
                $(".idNumSelect" + i).on('change', function () {
                    calculateEndDate($(".idNumStartDate" + i).val(), $(".idNumSelect" + i).val(), "idCardEndDateSpan" + i);
                });

                // 为开始日期输入框添加change事件处理器
                $(".idNumStartDate" + i).on('change', function () {
                    calculateEndDate($(".idNumStartDate" + i).val(), $(".idNumSelect" + i).val(), "idCardEndDateSpan" + i);
                });
            }
        }

        function calculateEndDate(idNumStartDate, idNumSelectVal, spanId) {
            if (idNumSelectVal && idNumSelectVal != -1) {
                let date = new Date(idNumStartDate);
                let year = date.getFullYear();
                date.setFullYear(year + parseInt(idNumSelectVal));
                let idNumEndDateContent = date.toISOString().substring(0, 10).replace(/-/g, '/');
                $("#" + spanId).text(idNumEndDateContent);
            } else {
                $("#" + spanId).text("");
            }
        }


        // 银行信息加载
        function reloadbankInfo(rowNum) {
            //查询支行信息
            $("#bankSubbranchSelect" + rowNum).select2({
                ajax: {
                    type: 'POST',
                    url: "${ctx}/insuranceCaseController/getBankInfo",
                    dataType: 'json',
                    delay: 500,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            totalBankId: $('#totalBankSelect' + rowNum).val(),
                            paramMsg: params.term,
                            insCode: '${claimCase.insCode}'
                        };
                    },
                    processResults: function (data) {
                        console.log(data);
                        return {
                            results: data.bankList,
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                },
                width: '100%',
                placeholder: "请输入支行名称查询...",
                minimumInputLength: 2,
                minimumResultsForSearch: Infinity, // 隐藏搜索框
                templateResult: function (data) {
                    if (data.id != undefined) {
                        return "<div>" + data.bankName + "(" + data.provinceName + "-" + data.cityName + ")" + "</div>";
                    }
                    return "";
                },
                templateSelection: function (data) {
                    if (data.id != "") {
                        $("#bankInfoId" + rowNum).val(data.id);
                        let bankText = data.bankName + "(" + data.provinceName + "-" + data.cityName + ")";
                        return "<span title='" + bankText + "'>" + bankText + "</span>";
                    }
                    return "请输入支行名称查询...";
                }
            });


            //查询总行信息
            $("#totalBankSelect" + rowNum).select2({
                ajax: {
                    type: 'POST',
                    url: "${ctx}/insuranceCaseController/getTotalBankInfo",
                    dataType: 'json',
                    delay: 500,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            paramMsg: params.term,
                            insCode: '${claimCase.insCode}'
                        };
                    },
                    processResults: function (data) {
                        console.log(data);
                        return {
                            results: data.bankList,
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                },
                width: '100%',
                placeholder: "请输入总行名称查询...",
                minimumInputLength: 2,
                minimumResultsForSearch: Infinity, // 隐藏搜索框
                templateResult: function (data) {
                    if (data.id != undefined) {
                        return "<div>" + data.bankName + "</div>";
                    }
                    return "";
                },
                templateSelection: function (data) {
                    if (data.id != "") {
                        $("#bankName" + rowNum).val(data.bankName);
                        $('#bankSubbranchSelect' + rowNum).prop('disabled', false);
                        $("#bankInfoId" + rowNum).val("");
                        $("#bankSubbranchSelect" + rowNum).select2("val", "");
                        $("#select2-bankSubbranchSelect-container .select2-selection__placeholder").html("");
                        return "<span>" + data.bankName + "</span>";
                    }
                    $("#bankName" + rowNum).val("");
                    $("#bankInfoId" + rowNum).val("");
                    $("#bankSubbranchSelect" + rowNum).select2("val", "");
                    $('#bankSubbranchSelect' + rowNum).prop('disabled', true);
                    return "请输入总行名称查询...";
                }
            });
        }

        function delPaymentRow(obj) {
            $(obj).parent().parent().remove();
        }


        var scrollTop;        // 定义滚动高度


        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        jQuery(document).ready(function () {
            App.init();
        });

        /**
         * 转换年月日
         * @param timestamp
         * @returns {string}
         */
        function getDateYYYYMMDD(timestamp) {
            let date = new Date();
            if (timestamp) {
                date = new Date(timestamp);
            }
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            month = month < 10 ? '0' + month : month;
            day = day < 10 ? '0' + day : day;
            let dateStr = year + '-' + month + '-' + day;
            return dateStr;
        }

        let check = function () {
            var _this = this;
            var val = _this.value;
            var valid = _this.getAttribute('data-valid').trim();
            valid = valid.split(" ");
            for (let check of valid) {
                if (!checkService[check](val)) {
                    $(_this).val("");
                    // 带遮罩的弹出
                    // layer.msg("输入格式不对", {icon: 2,time: 2000,shade: [0, 'rgba(0,0,0,0)']});
                    if (check == "isEmpty") {
                        layer.tips("值不能为空", $(_this), {time: 1500, shade: [0.0001, '#000']});
                    } else {
                        layer.tips("输入格式不对", $(_this), {time: 1500, shade: [0.0001, '#000']});
                    }
                    return;
                }
            }

            $(this).attr("title", val);

        }
        //校验规则
        const checkService = {
            // 不校验
            none: function () {
                return true;
            },

            //非空校验
            isEmpty: function (str) {
                let result = true;

                if (str == null || str == "") {
                    result = false;
                }
                if (str == "0") {
                    result = true;
                }

                return result;
            },

            //身份证校验
            isIdCard: function (str) {
                const idCardCheck = new IdCardCheck();
                if (idCardCheck.checkIdCard(str)) {
                    return false;
                }
                return true;
            },

            // 只能输入数字[0-9]
            isDigits: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                return reg.test(str);
            },
            //百分比0-100
            isRate: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                let result = false;
                if (reg.test(str) && (str >= 0 && str <= 100)) {
                    result = true;
                }
                return result;
            },

            // 匹配english
            isEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[A-Za-z]+$/;
                return reg.test(str);
            },

            // 匹配integer(包含正负)
            isInteger: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[-\+]?\d+$/;
                return reg.test(str);
            },

            // 匹配汉字
            isChinese: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5]+$/;
                return reg.test(str);
            },

            // 匹配中文(双字节字符,包括汉字和符号)
            isChineseChar: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u0391-\uFFE5]+$/;
                return reg.test(str);
            },

            //匹配中英文
            isChineseAndEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5a-zA-Z]+$/;
                return reg.test(str);
            },

            // 匹配URL
            isUrl: function (str) {
                if (str == null || str == "") return true;
                var reg = /^http:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\’:+!]*([^<>\"])*$/;
                return reg.test(str);
            },

            // 字符验证，只能包含中文、英文、数字、下划线、空格。
            stringCheck: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[a-zA-Z0-9\u4e00-\u9fa5_ ,.，。]+$/;
                return reg.test(str);
            },

            //字符长度校验（最长64位）
            stringLengthCheck: function (str, length) {
                if (str == null || str == "") return true;
                length = length || 64;
                if (str.length > length) return false;
                return true;
            },
            //IP格式验证
            isIP: function (str) {
                if (str == null || str == "") return true;
                var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                return reg.test(str);
            },
            //YYYYMMDD格式验证
            isTime: function (str) {
                if (str == null || str == "") return true;
                var reg = /^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229))$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPoint: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPointNotDefault: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //select2--->是否
            isTrueFalse: function (str) {

                return true;
            },
            //伤残等级
            isDisabilityLevel: function () {
                return true;
            },
            //伤残等级
            isDeadSurplusQuota: function () {
                return true;
            }
        };

        var errorMsg = "";      // 错误描述

        // 组装数据
        function assembleData(status) {

            errorMsg = "";      // 清空错误描述

            $("button[name='submitBtn']").attr('disabled', '1');

            var paymentVerifyAmount = new BigDecimal("0");      // 总应付损失金额
            var sumVerifyAmount = new BigDecimal("0");          // 总合计

            let objectPaymentList = [];
            $.each($("div.paymentCollectAreaRow"), function (index, obj) {
                let num = $(this).attr("data-index");
                let id = $(this).find("#claimCaseObjectPaymentId").val().trim();
                let payObjectType = $(this).find("input[name='payObjectType" + num + "']").filter(':checked').val();   // 账户性质
                let name = $(this).find("#paymentName").val().trim();                                       // 收款人姓名
                let idType = $(this).find("#idType").val();                                                 // 收款人证件类型
                let idNum = $(this).find("#idNum").val().trim();                                            // 收款人证件号码
                let idNumStartDate = $(this).find("#idNumStartDate").val();                                 // 证件有效期起期
                // 证件有效期止期
                let idNumEndDateStr = $(this).find("#idNumEndDate").val();
                let idNumEndDate;
                if (idNumStartDate && idNumEndDateStr) {
                    if (idNumEndDateStr != -1) {
                        let date = new Date(idNumStartDate);
                        let year = date.getFullYear();
                        date.setFullYear(year + parseInt(idNumEndDateStr));
                        idNumEndDate = date.toISOString().substring(0, 10);
                    }
                }
                let mobile = $(this).find("#mobile").val().trim();                                          // 收款人联系电话
                let bankName = $(this).find("#bankName" + num).val();                                         // 收款人银行名称
                let bankInfoId = $(this).find("#bankInfoId" + num).val();                                     // 收款人银行支行
                let bankAccount = $(this).find("#bankAccount").val().trim();                                // 收款人开户名
                let bankCard = $(this).find("#bankCard").val().trim();                                      // 收款人银行账号
                let payAmount = $(this).find("#payAmount").val().trim();                                    // 应付损失金额
                if (payAmount) {
                    paymentVerifyAmount = paymentVerifyAmount.add(new BigDecimal(payAmount)).setScale(2, MathContext.ROUND_HALF_UP);
                }

                objectPaymentList.push({
                    "id": id,
                    "name": name,
                    "idNum": idNum,
                    "idType": idType,
                    "mobile": mobile,
                    "payObjectType": payObjectType,
                    "bankAccount": bankAccount,
                    "bankCard": bankCard,
                    "bankName": bankName,
                    "bankInfoId": bankInfoId,
                    "payAmount": payAmount,
                    "idNumStartDate": idNumStartDate,
                    "idNumEndDate": idNumEndDate,
                    "isLt": idNumEndDateStr
                });
            });

            var insuranceClaimAssessment = {
                id: $("input[name='assessmentId']").val(),                                         // ID
                lossNo: "${claimCaseObject.id}",                                                   // 赔付项ID
                assessmentType: "理算",                                                            // 类型（定损、初审、核损、理算）
                totalPartsAmount: $("input[name='totalPartsAmount']").val().trim(),                // 合计配件金额
                partsManagementFee: $("input[name='partsManagementFee']").val().trim(),            // 配件管理费金额
                totalResidualValue: $("input[name='totalResidualValue']").val().trim(),            // 合计残值金额
                totalOutOfPocket: $("input[name='totalOutOfPocket']").val().trim(),                // 合计自付金额
                totalLaborAmount: $("input[name='totalLaborAmount']").val().trim(),                // 合计工时金额
                totalSuppliesAmount: $("input[name='totalSuppliesAmount']").val().trim(),          // 合计辅料金额
                totalExternalRepair: $("input[name='totalExternalRepair']").val().trim(),          // 合计外修金额
                totalRescueFee: $("input[name='totalRescueFee']").val().trim(),                    // 合计施救费用金额
                totalExcludingRescue: $("input[name='totalExcludingRescue']").val().trim(),        // 总合计
            };

            if (!insuranceClaimAssessment.totalPartsAmount) {
                errorMsg += "合计配件金额不能为空！</br>";
            } else {
                sumVerifyAmount = sumVerifyAmount.add(new BigDecimal(insuranceClaimAssessment.totalPartsAmount)).setScale(2, MathContext.ROUND_HALF_UP);
            }
            if (!insuranceClaimAssessment.partsManagementFee) {
                errorMsg += "配件管理费金额不能为空！</br>";
            } else {
                sumVerifyAmount = sumVerifyAmount.add(new BigDecimal(insuranceClaimAssessment.partsManagementFee)).setScale(2, MathContext.ROUND_HALF_UP);
            }
            if (!insuranceClaimAssessment.totalResidualValue) {
                errorMsg += "合计残值金额不能为空！</br>";
            } else {
                sumVerifyAmount = sumVerifyAmount.subtract(new BigDecimal(insuranceClaimAssessment.totalResidualValue)).setScale(2, MathContext.ROUND_HALF_UP);
            }
            if (!insuranceClaimAssessment.totalOutOfPocket) {
                errorMsg += "合计自付金额不能为空！</br>";
            } else {
                sumVerifyAmount = sumVerifyAmount.subtract(new BigDecimal(insuranceClaimAssessment.totalOutOfPocket)).setScale(2, MathContext.ROUND_HALF_UP);
            }
            if (!insuranceClaimAssessment.totalLaborAmount) {
                errorMsg += "合计工时金额不能为空！</br>";
            } else {
                sumVerifyAmount = sumVerifyAmount.add(new BigDecimal(insuranceClaimAssessment.totalLaborAmount)).setScale(2, MathContext.ROUND_HALF_UP);
            }
            if (!insuranceClaimAssessment.totalSuppliesAmount) {
                errorMsg += "合计辅料金额不能为空！</br>";
            } else {
                sumVerifyAmount = sumVerifyAmount.add(new BigDecimal(insuranceClaimAssessment.totalSuppliesAmount)).setScale(2, MathContext.ROUND_HALF_UP);
            }
            if (!insuranceClaimAssessment.totalExternalRepair) {
                errorMsg += "合计外修金额不能为空！</br>";
            } else {
                sumVerifyAmount = sumVerifyAmount.add(new BigDecimal(insuranceClaimAssessment.totalExternalRepair)).setScale(2, MathContext.ROUND_HALF_UP);
            }
            if (!insuranceClaimAssessment.totalRescueFee) {
                errorMsg += "合计施救费用金额不能为空！</br>";
            } else {
                sumVerifyAmount = sumVerifyAmount.add(new BigDecimal(insuranceClaimAssessment.totalRescueFee)).setScale(2, MathContext.ROUND_HALF_UP);
            }
            if (!insuranceClaimAssessment.totalExcludingRescue) {
                errorMsg += " 总合计不能为空！</br>";
            }

            var claimCaseObject = {
                id: "${claimCaseObject.id}",
                insuranceClaimAssessment: insuranceClaimAssessment,
                claimCaseObjectPaymentList: objectPaymentList,
                status: status,
                verifyAmout: $("input[name='totalExcludingRescue']").val().trim()
            };

            var payAmountSum = new BigDecimal("0");
            let isValidity = '${isValidity}';
            for (let payment of objectPaymentList) {
                if (!payment.name) {
                    errorMsg += "领款人不能为空！</br>";
                }
                if (!payment.idType) {
                    errorMsg += "证件类型不能为空！</br>";
                }
                if (!payment.idNum) {
                    errorMsg += "证件号码不能为空！</br>";
                } else if (payment.idType == "身份证" && !checkService["isIdCard"](payment.idNum)) {
                    errorMsg += "身份证件号码错误！</br>";
                }
                if (!payment.mobile) {
                    errorMsg += "联系电话不能为空！</br>";
                }
                if (!payment.payObjectType) {
                    errorMsg += "账号性质不能为空！</br>";
                }
                if (!payment.bankAccount) {
                    errorMsg += "开户名不能为空！</br>";
                }
                if (!payment.bankCard) {
                    errorMsg += "银行账号不能为空！</br>";
                } else if (payment.bankCard.includes(' ')) {
                    errorMsg += "银行账号不能有空格！</br>";
                }
                if (!payment.bankName) {
                    errorMsg += "银行名称不能为空！</br>";
                }
                if (!payment.bankInfoId) {
                    errorMsg += "银行支行不能为空！</br>";
                }
                if (!payment.payAmount) {
                    errorMsg += "划款金额不能为空！</br>";
                } else {
                    payAmountSum = payAmountSum.add(new BigDecimal(payment.payAmount));
                }
                if (isValidity) {
                    if (!payment.idNumStartDate) {
                        errorMsg += "证件有效期开始日期不能为空！</br>";
                    }
                    if (!payment.idNumEndDate && payment.isLt != -1) {
                        errorMsg += "证件有效期不能为空！</br>";
                    } else {
                        let endDate = new Date(payment.idNumEndDate);
                        let today = new Date();
                        if (endDate < today) {
                            errorMsg += "证件已过期！</br>";
                        }
                    }
                }
            }

            if (!errorMsg) {
                // 根据产品沟通，去除该项校验
                /*if (eval(insuranceClaimAssessment.totalExcludingRescue) != eval(sumVerifyAmount)) {
                    errorMsg += " 总合计与各项金额不一致！</br>";
                }*/
                if (eval(insuranceClaimAssessment.totalExcludingRescue) != eval(paymentVerifyAmount)) {
                    errorMsg += " 总合计与总赔付金额不一致！</br>";
                }
                var lossAssessment = $("#lossAssessment").attr("data-lossAssessment");
                if (!lossAssessment) {
                    errorMsg += " 定损总合计数据异常！</br>";
                } else if (eval(insuranceClaimAssessment.totalExcludingRescue) > eval(lossAssessment)) {
                    errorMsg += " 理算总合计不能大于定损总合计！</br>";
                }
            }
            scrollTop = calculationScrollTop();


            return claimCaseObject;
        }

        // 保存
        function saveData(status) {
            // 收集定损记录数据
            var claimCaseObject = assembleData(status);
            if (errorMsg) {
                layer.msg(errorMsg, {icon: 2, time: 3000, offset: scrollTop}, function () {
                    $("button[name='submitBtn']").removeAttr("disabled");
                });
                return;
            }
            $("button[name='submitBtn']").removeAttr("disabled");
            layer.prompt({
                title: '请输入意见备注',
                formType: 2, // 0-文本输入框（默认），1-密码框，2-多行文本框
                value: '', // 默认内容
                maxlength: 200, // 最大输入长度
                area: ['740px', '490px'],
                btn: ['确定', '取消'],
                offset: "" + "",
                yes: function (index, elem) {
                    let remark = elem.find('.layui-layer-input').val();

                    claimCaseObject['logRemark'] = remark;
                    $.ajax({
                        url: "${ctx}/claimCaseObjectController/carVerifyObjectSubmit",
                        type: 'POST',
                        data: JSON.stringify(claimCaseObject),
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg(result.msg, {
                                    icon: 1,
                                    time: 2000,
                                    shade: [0.0001, '#000'],
                                    offset: scrollTop
                                }, function () {
                                    window.close();
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 2000,
                                    shade: [0.0001, '#000'],
                                    offset: scrollTop
                                }, function () {
                                    $("button[name='submitBtn']").removeAttr("disabled");
                                });
                            }

                        },
                        error: function (data) {
                            $("button[name='submitBtn']").removeAttr("disabled");
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    })
                }
            });
        }

        // 提审
        function submitData(status) {
            let claimCaseObject = assembleData(status);
            if (errorMsg) {
                layer.msg(errorMsg, {icon: 2, time: 3000, offset: scrollTop}, function () {
                    $("button[name='submitBtn']").removeAttr("disabled");
                });
                return;
            }
            if (claimCaseObject) {
                claimCaseObject['logRemark'] = "提交理算审核成功";
                $.ajax({
                    url: "${ctx}/claimCaseObjectController/carVerifyObjectSubmit",
                    type: 'POST',
                    data: JSON.stringify(claimCaseObject),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            //跳转确认页面
                            window.location.href = "${ctx}/claimCaseObjectController/carVerifySubmitConfirmPage?reportNo=${claimCaseObject.claimCaseNo}&insCode=${claimCaseObject.insCode}&status=" + result.status;
                        } else {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 2000,
                                shade: [0.0001, '#000'],
                                offset: scrollTop
                            }, function () {
                                $("button[name='submitBtn']").removeAttr("disabled");
                            });
                        }

                    },
                    error: function (data) {
                        $("button[name='submitBtn']").removeAttr("disabled");
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                })
            }
        }

        // 查看基本信息
        function showBasicInformation(claimCaseId, claimCaseObjectId) {
            layer.open({
                title: "查看基本信息",
                type: 2,
                offset: 'auto',
                area: ['1200px', '700px'],
                fixed: false,
                closeBtn: 1,
                maxmin: true,
                content: "${ctx}/insuranceNuclearAudit/showBasicInformation?claimCaseId=" + claimCaseId + "&claimCaseObjectId=" + claimCaseObjectId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        // 查看日志
        function showDetailLog(claimCaseObjectId) {
            layer.open({
                title: "查看日志",
                type: 2,
                offset: 'auto',
                area: ['1200px', '700px'],
                fixed: false,
                closeBtn: 1,
                maxmin: true,
                content: "${ctx}/insuranceNuclearAudit/showDetailLog?claimCaseObjectId=" + claimCaseObjectId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }


        // 历史案件
        function getHistroyCaseInfo(baseUserId) {
            if (baseUserId == '') {
                layer.msg("暂无信息！！！", {icon: 2, time: 3000});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看历史案件',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistroyCaseInfo?baseUserId=" + baseUserId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

    </script>
    <style>

        body {
            font-size: 12px;
            margin: 0;
            padding: 0;
        }

        /* 基本信息、查看日志可以进行拖拽样式 */
        #floating-container {
            position: fixed;
            right: 20px;
            top: 55%;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .draggable-box {
            width: 60px;
            height: 60px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 6px !important;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            user-select: none;
            border: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 12px;
            color: #666;
            text-align: center;
            line-height: 1.3;
            padding: 13px;
            box-sizing: border-box;
            margin-bottom: 8px;
            cursor: move;
            transition: all 0.3s ease; /* 添加过渡效果 */

        }

        /* 鼠标悬停效果 - 边框变亮 */
        .draggable-box:hover {
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); /* 内阴影 */
            transform: scale(1.1);
        }

        /* 选中状态 - 边框变亮且更明显 */
        .box-active {
            background-color: #ffffff;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); /* 内阴影 */
            transform: translateY(1px); /* 模拟按下 */
        }


        .container {
            display: flex;
            flex-direction: column;
            gap: 5px; /* 设置紧凑的间距 */
        }

        .compact {
            margin: 5px;
            padding: 5px;
        }

        .floating-button {
            text-align: center;
            font-size: 15px;
            padding: 2px 5px;
            color: gray;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 5px;
        }


        .floating-button:hover {
            transform: scale(1.1);
        }


        .receipt-account {
            border: 1px solid #eee;
            padding: 20px;
            border-radius: 4px;
            width: 800px;
        }

        .receipt-account h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .form-item {
            display: grid;
            /* 一行等分为3列 */
            grid-template-columns: repeat(3, 1fr);
            gap: 20px; /* 列之间的间距 */
        }

        .field {
            display: flex;
            align-items: center;
        }

        .field label {
            width: 100px;
            text-align: right;
            margin-right: 10px;
            color: #666;
        }

        .radio-group {
            display: flex;
            align-items: center;
        }

        .radio-group span {
            margin-right: 15px;
        }

        @media (min-width: 992px) {
            .page-content-wrapper .page-content {
                margin-left: 0px !important;
            }
        }

        /* 修复页面滚动问题 */
        body {
            /*padding-top: 70px !important; !* 为固定头部留出空间 *!*/
            overflow-y: auto !important; /* 确保垂直滚动 */
            height: auto !important;
        }

        .page-container {
            min-height: 100vh;
            overflow-y: auto;
        }

        .page-content-wrapper {
            overflow-y: auto;
            min-height: calc(100vh - 70px);
        }

        .page-content {
            padding-bottom: 50px; /* 底部留出空间 */
            overflow-y: auto;
        }

        .detail-form {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .detail-form .form-group {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .detail-form .control-label {
            font-weight: bold;
            color: #333;
            text-align: right;
            padding-right: 10px;
            white-space: nowrap;
            min-width: 120px;
            width: 120px;
            flex: 0 0 120px;
        }

        .detail-form .form-control-static {
            padding: 8px 12px;
            margin-bottom: 0;
            min-height: 32px;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            background-color: #f9f9f9;
            color: #555;
            flex: 1;
            word-break: break-all;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .section-title {
            border-left: 4px solid #3598dc;
            padding-left: 10px;
            margin: 20px 0 15px 0;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .amount-text {
            color: #e74c3c;
            font-weight: bold;
            font-size: 16px;
        }

        .page-header {
            background: #34495e;
            border-bottom: 1px solid #2c3e50;
        }

        .page-header .logo-default {
            color: white !important;
            font-size: 18px !important;
        }

        /* 费用表格样式 */
        .cost-table {
            margin-top: 10px;
        }

        .cost-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }

        .cost-table td {
            vertical-align: middle !important;
        }

        .cost-table .cost-type-col {
            text-align: left;
        }

        .cost-table .amount {
            color: #000000;
            font-weight: bold;
        }

        /* 备注记录样式 */
        .remark-item {
            border-bottom: 1px solid #eee;
            padding: 15px 0;
        }

        .remark-item:last-child {
            border-bottom: none;
        }

        .remark-type {
            font-weight: bold;
            color: #007bff;
        }

        .remark-meta {
            color: #6c757d;
            font-size: 0.9em;
        }

        .remark-content {
            line-height: 1.6;
            color: #495057;
        }

        /* 确保portlet和其他组件不影响滚动 */
        .portlet {
            overflow: visible !important;
            height: auto !important;
            position: static;
            margin-bottom: 20px !important;
            display: block !important;
            visibility: visible !important;
        }

        .portlet .portlet-title {
            overflow: visible !important;
            height: auto !important;
            position: static !important;
            display: block !important;
            visibility: visible !important;
        }

        /* 理算处理按钮样式 */
        .nuclear-loss-btn {
            background-color: #cf3636 !important;
            border-color: #000000 !important;
            color: black !important;
            float: right;
            margin-top: 0px;
            margin-left: 15px;
            display: inline-block;
            font-size: 16px !important;
            padding: 8px 16px !important;
        }

        .nuclear-loss-btn:hover {
            background-color: #000000 !important;
            border-color: #000000 !important;
            color: white !important;
        }

        /* 按钮样式 */
        .btn {
            min-width: 80px;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
        }

        /* 确保按钮容器可见 */
        .text-right {
            display: block !important;
            visibility: visible !important;
            overflow: visible !important;
            height: auto !important;
        }

        .page-sidebar {
            position: static !important;
            overflow: visible !important;
            height: auto !important;
        }

        .page-footer {
            position: static !important;
            overflow: visible !important;
            height: auto !important;
        }

        /* 面包屑导航样式 */
        .page-breadcrumb {
            padding: 0;
            margin: 0 0 25px 0;
            background-color: transparent;
            border-radius: 0;
        }

        .page-breadcrumb > li + li:before {
            content: ">";
            padding: 0 5px;
            color: #777;
        }

        /* 卡片样式优化 */

        .portlet .portlet-title {
            padding: 15px 20px;
            min-height: 48px;
            border-bottom: 1px solid #eee;
            position: relative;
        }

        .portlet .portlet-title .caption {
            float: left;
            display: inline-block;
            font-size: 18px;
            line-height: 18px;
        }

        .portlet .portlet-body {
            padding: 2px;
        }

        .portlet.light .portlet-body {
            padding-top: 1px;
        }

        /* 确保按钮区域可见 */
        .portlet .portlet-body {
            min-height: 50px;
        }

        .btn {
            margin-right: 10px;
            min-width: 80px;
        }

        .btn:last-child {
            margin-right: 0;
        }

        /* 强制显示按钮容器 */
        .text-right {
            display: block !important;
            visibility: visible !important;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }


        .row {
            margin: 0px 0px;
        }


        textarea[class='layui-layer-input'] {
            outline: 0 !important;
            width: 700px !important;
            height: 350px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        button[name='actionBtn'] {
            color: white;
        }

        select {
            padding: 0px 12px !important;
        }

        div[name='dataCollectAreaRow'] input {
            padding: 0px 5px;
        }

        #thumbnail p {
            margin: 10px 0px 5px -15px;
            font-size: 10px;
            text-align: left;
        }

        .viewer-canvas h2 {
            z-index: 9999999;
            position: relative;
            left: 20px;
            font-weight: bold;
        }

        /* 侧边栏样式 */
        .sidebar-nav {
            width: 100%;
            padding: 10px 0;
            background-color: #f5f5f5;
            border-radius: 4px;
        }

        .nav-list {
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
        }

        .nav-item {
            border-bottom: 1px solid #e0e0e0;
        }

        .nav-link {
            display: block;
            padding: 8px 15px;
            color: #333;
            text-decoration: none;
        }

        .nav-link:hover {
            background-color: #e9e9e9;
        }

        .nav-link i {
            margin-right: 8px;
            transition: transform 0.3s ease;
        }

        .nav.collapse {
            display: none;
            padding-left: 20px;
        }

        .nav.collapse.in {
            display: block;
        }

        /* 展开状态的箭头旋转 */
        .nav-link[aria-expanded="true"] i.glyphicon-chevron-right {
            transform: rotate(90deg);
        }

        /* 数量标签样式 */
        .nav-link span.badge {
            margin-left: 5px;
            background-color: #777;
        }

        .flex-align-center {
            display: flex;
            align-items: center;
        }

        label {
            margin: 0px 0px 0px 5px !important;
        }

        input[name='payObjectType'] {
            margin-left: 0px !important;
        }

        .payment-itme .row > .col-sm-4 {
            display: flex !important;
            align-items: center;
            min-height: 29px !important;
            margin: 10px 0px;
        }

        .payment-itme .row .col-sm-4 .col-sm-8 {
            padding-left: 0px !important;
        }

        .payment-itme > .row {
            margin-bottom: 25px;
        }

        .addPayment {
            width: 250px;
        }

        .btn-del-payment {
            color: red;
        }

        .del-svg-img {
            width: 15px;
            height: 25px;
        }

        td input {
            width: 100% !important;
            border: 0px !important;
            text-align: center !important;
            padding: 0px !important;
            text-align: right !important;
        }

        .tr-content > :not(:first-child) {
            text-align: right !important;
        }

        /* 滚动时添加的样式（可选） */
        .fixed-style {
            width: 100%;
            position: fixed;
            border-radius: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 999;
            top: 0px;
            left: 0px;
            margin-bottom: 0px !important;
        }

    </style>
</head>
<body class="page-content-white">

<!-- 页面容器 -->
<div class="page-container">
    <!-- 页面内容包装器 -->
    <div class="page-content-wrapper">
        <!-- 页面内容 -->
        <div class="page-content" style="margin-left: 0;">
            <!-- 面包屑导航 -->
            <div class="page-bar">
                <ul class="page-breadcrumb">
                    <li>
                        <a href="${ctx}/settlementAuditController/settlementAuditList">理算处理详情</a>
                    </li>
                    <li>任务详情</li>
                </ul>
            </div>

            <!-- 案件信息和快捷操作栏 -->
            <div id="floating-container">
                <div>
                    <div id="box1" class="draggable-box box-active"
                         onclick="showBasicInformation('${claimCase.id}', '${claimCaseObject.id}')">
                        基本信息
                    </div>
                </div>
                <div>
                    <div id="box2" class="draggable-box box-active" onclick="showDetailLog('${claimCaseObject.id}')">
                        查看日志
                    </div>
                </div>
                <div>
                    <div id="box3" class="draggable-box box-active"
                         onclick="getHistroyCaseInfo('${claimCase.baseUserId!''}')">
                        历史案件（${historyCaseSize}）
                    </div>
                </div>

            </div>

            <!-- 理算处理费用合计 -->
            <div class="row">
                <div class="portlet light bordered" id="stickyBox" >
                    <div class="row">
                        <div class="row flex-align-center">
                            <div class="col-md-6">
                                <div class="row ">
                                    <div class="col-md-6">
                                        <div class="bold">
                                            报案号：${claimCase.claimCaseNo!''}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="bold">
                                            立案号：${claimCase.insuranceCaseNo!''}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="pull-right">
                                    <button type="button" name="submitBtn" class="btn btn-default"
                                            onclick="saveData('BAX31')"
                                            style="color: #f5f5f5;background-color: #afa7a7;border-color: #fff;">
                                        保存
                                    </button>
                                    <button type="button" name="submitBtn" class="btn btn-primary"
                                            onclick="submitData(<#if claimCaseObject.status == 'BAX37'>'BAX35'<#else>'BAX32'</#if>)">
                                        提交理算审核
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="portlet light" style="margin-bottom: 0px !important;">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="icon-bar-chart font-dark"></i>
                                    <span class="caption-subject font-dark sbold uppercase">损失费用合计</span>
                                </div>
                            </div>
                            <div class="portlet-body">

                                <#if insuranceClaimAssessmentMap?? && insuranceClaimAssessmentMap.keySet()?size >
                                    <div class="table-responsive">
                                        <table class="table table-bordered cost-table text-center" style="margin: 0px;">
                                            <thead>
                                            <tr>
                                                <th width="10%">类型</th>
                                                <th width="10%">合计配件金额</th>
                                                <th width="10%">配件管理费金额</th>
                                                <th width="10%">合计残值金额</th>
                                                <th width="10%">合计自付金额</th>
                                                <th width="10%">合计工时金额</th>
                                                <th width="10%">合计辅料金额</th>
                                                <th width="10%">合计外修金额</th>
                                                <th width="10%">合计施救费用金额</th>
                                                <th width="15%">总合计</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <#list assessmentTypeMap.keySet() as key>
                                                <#if insuranceClaimAssessmentMap?contains(key) || assessmentTypeMap.get(key) == "理算">
                                                    <tr class="tr-content">

                                                        <td>${assessmentTypeMap.get(key)}</td>
                                                        <#if insuranceClaimAssessmentMap?contains(key)>
                                                            <#assign assessment = insuranceClaimAssessmentMap.get(key)>
                                                            <#if assessmentTypeMap.get(key) == "理算">
                                                                <input type="hidden" name="assessmentId"
                                                                       value="${assessment.id}">
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalPartsAmount"
                                                                           value="${assessment.totalPartsAmount}">
                                                                </td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="partsManagementFee"
                                                                           value="${assessment.partsManagementFee}">
                                                                </td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalResidualValue"
                                                                           value="${assessment.totalResidualValue}">
                                                                </td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalOutOfPocket"
                                                                           value="${assessment.totalOutOfPocket}">
                                                                </td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalLaborAmount"
                                                                           value="${assessment.totalLaborAmount}">
                                                                </td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalSuppliesAmount"
                                                                           value="${assessment.totalSuppliesAmount}">
                                                                </td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalExternalRepair"
                                                                           value="${assessment.totalExternalRepair}">
                                                                </td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalRescueFee"
                                                                           value="${assessment.totalRescueFee}">
                                                                </td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalExcludingRescue"
                                                                           value="${assessment.totalExcludingRescue}">
                                                                </td>
                                                            <#else>
                                                                <td>${assessment.totalPartsAmount}</td>
                                                                <td>${assessment.partsManagementFee}</td>
                                                                <td>${assessment.totalResidualValue}</td>
                                                                <td>${assessment.totalOutOfPocket}</td>
                                                                <td>${assessment.totalLaborAmount}</td>
                                                                <td>${assessment.totalSuppliesAmount}</td>
                                                                <td>${assessment.totalExternalRepair}</td>
                                                                <td>${assessment.totalRescueFee}</td>
                                                                <td <#if assessmentTypeMap.get(key) == "定损">id="lossAssessment" data-lossAssessment="${assessment.totalExcludingRescue}"</#if>>${assessment.totalExcludingRescue}</td>
                                                            </#if>

                                                        <#else >
                                                            <#if assessmentTypeMap.get(key) == "理算">
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalPartsAmount"></td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="partsManagementFee"></td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalResidualValue"></td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalOutOfPocket"></td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalLaborAmount"></td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalSuppliesAmount"></td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalExternalRepair"></td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalRescueFee"></td>
                                                                <td><input class="form-control"
                                                                           validName="billCollection"
                                                                           data-valid="isEmpty isNumberAndDecimalPoint"
                                                                           placeholder="请输入" type="text"
                                                                           name="totalExcludingRescue"></td>
                                                            </#if>
                                                        </#if>
                                                    </tr>
                                                </#if>
                                            </#list>
                                            </tbody>
                                        </table>
                                    </div>
                                <#else>
                                    <div class="alert alert-info">
                                        <i class="fa fa-info-circle"></i> 暂无费用明细数据
                                    </div>
                                </#if>
                            </div>
                        </div>
                    </div>

                </div>
                <#--银行账户信息-->
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-bar-chart font-dark"></i>
                            <span class="caption-subject font-dark sbold uppercase">收款账户信息</span>
                        </div>
                    </div>
                    <div class="payment-itme row" id="paymentCollectArea">
                        <#list objectPaymentList as payment>
                            <div class="row payment-${payment.id} paymentCollectAreaRow"
                                 data-index="${payment.id}">
                                <input type="hidden" id="claimCaseObjectPaymentId" class="form-control"
                                       value="${payment.id}">
                                <div class="row btn-del-payment flex-align-center" style="padding-left: 4.5%;">
                                </div>
                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">账户性质*</div>
                                    <div class="col-sm-8 flex-align-center">
                                        <input type="radio" name="payObjectType${payment.id}"
                                               id="payObjectPerson${payment.id}" value="2"
                                               <#if payment.payObjectType == "2">checked</#if> >
                                        <label for="payObjectPerson${payment.id}">自然人（个人）</label>
                                        <input type="radio" name="payObjectType${payment.id}"
                                               id="payObjectEnt${payment.id}" value="1"
                                               <#if payment.payObjectType == "1">checked</#if>>
                                        <label for="payObjectEnt${payment.id}">非自然人（企业）</label>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">收款人姓名*</div>
                                    <div class="col-sm-8">
                                        <input validName="billCollection" id="paymentName" type="text"
                                               class="form-control" data-valid="isEmpty"
                                               value="${payment.name}"/>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">收款人证件类型*</div>
                                    <div class="col-sm-8">
                                        <select validName="billCollection"
                                                class="kp-select2 inputStyle form-control"
                                                data-valid="none" id="idType">
                                            <option value="身份证" <#if payment.idType == "身份证">selected</#if>>
                                                身份证
                                            </option>
                                            <option value="其他" <#if payment.idType == "其他">selected</#if>>其他
                                            </option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">收款人证件号码*</div>
                                    <div class="col-sm-8 ">
                                        <input validName="billCollection" id="idNum" type="text"
                                               class="form-control" data-valid="isEmpty"
                                               value="${payment.idNum}"/>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">证件有效期*</div>
                                    <div class="col-sm-8 flex-align-center">
                                        <div class="col-sm-9 clear-padding">
                                            <input validName="billCollection" type="date"
                                                   id="idNumStartDate" class="form-control"
                                                   value="${(payment.idNumStartDate?string["yyyy-MM-dd"])!''}"
                                                   data-valid="none"/>
                                        </div>
                                        <span style="margin: 0px 10px;">-${!payment.remark}</span>
                                        <div class="col-sm-9 clear-padding">
                                            <select validName="billCollection"
                                                    class="kp-select2 inputStyle form-control"
                                                    data-valid="none" id="idNumEndDate">
                                                <option value="">请选择</option>
                                                <option value="5" <#if payment.remark == "5">selected</#if>>5年
                                                </option>
                                                <option value="10" <#if payment.remark == "10">selected</#if>>
                                                    10年
                                                </option>
                                                <option value="20" <#if payment.remark == "20">selected</#if>>
                                                    20年
                                                </option>
                                                <option value="-1" <#if payment.remark == "-1">selected</#if>>
                                                    长期有效
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">收款人联系电话*</div>
                                    <div class="col-sm-8">
                                        <input validName="billCollection" id="mobile" type="text"
                                               class="form-control" data-valid="isEmpty"
                                               value="${payment.mobile}"/>
                                    </div>
                                </div>

                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">收款人银行名称*</div>
                                    <div class="col-sm-8 ">
                                        <select id="totalBankSelect${payment.id}"
                                                name="totalBankSelect${payment.id}"
                                                class="js-data-example-ajax">
                                        </select>
                                        <input id="bankName${payment.id}" name="bankName${payment.id}"
                                               type="hidden" value="${payment.bankName}">
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">收款人银行支行*</div>
                                    <div class="col-sm-8">
                                        <select id="bankSubbranchSelect${payment.id}"
                                                name="bankSubbranchSelect${payment.id}"
                                                class="js-data-example-ajax">
                                        </select>
                                        <input id="bankInfoId${payment.id}" name="bankInfoId${payment.id}"
                                               type="hidden" value="${payment.bankInfoId}">
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">收款人开户名*</div>
                                    <div class="col-sm-8">
                                        <input validName="billCollection" id="bankAccount" type="text"
                                               class="form-control" data-valid="isEmpty"
                                               value="${payment.bankAccount}"/>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">收款人银行账号*</div>
                                    <div class="col-sm-8 ">
                                        <input validName="billCollection" id="bankCard" type="text"
                                               class="form-control" data-valid="isEmpty"
                                               value="${payment.bankCard}"/>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="col-sm-4 text-right">应付损失金额 *</div>
                                    <div class="col-sm-8">
                                        <input validName="billCollection" id="payAmount" type="text"
                                               class="form-control" data-valid="isEmpty isNumberAndDecimalPoint"
                                               value="${payment.payAmount}"/>
                                    </div>
                                </div>
                            </div>
                        </#list>
                    </div>
                    <div class="row text-center margin-top-20">
                        <button class="addPayment btn green" onclick="addPaymentRow()">+添加收款账户信息</button>
                    </div>
                </div>
                <!-- 影像资料 -->
                <div class="row" style="margin: 0px -15px">
                    <div class="col-md-12">
                        <div class="portlet light bordered">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="icon-bar-chart font-dark"></i>
                                    <span class="caption-subject font-dark sbold uppercase">影像资料</span>
                                </div>
                            </div>

                            <div class="portlet-body">
                                <#include "/attach/attachComponent.html">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 备注意见 -->
                <div id="remakrArea" class="row" style="display: none">
                    <div class="col-md-12">
                        <div class="portlet light bordered">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="icon-speech font-dark"></i>
                                    <span class="caption-subject font-dark sbold uppercase">备注意见</span>
                                </div>
                            </div>
                            <div class="portlet-body">

                                <#if remarkList?? && (remarkList?size > 0)>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                            <tr>
                                                <th width="15%" class="text-center">操作类型</th>
                                                <th width="15%" class="text-center">金额</th>
                                                <th width="15%" class="text-center">操作人</th>
                                                <th width="20%" class="text-center">操作时间</th>
                                                <th width="35%" class="text-center">备注内容</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <#list remarkList as remark>
                                                <tr>
                                                    <td class="text-center">${remark.operationType!''}</td>
                                                    <td class="text-right">
                                                        <#if remark.amount??>
                                                            ${remark.amount?string("0.00")}
                                                        <#else>
                                                            -
                                                        </#if>
                                                    </td>
                                                    <td class="text-center">${remark.operator!''}</td>
                                                    <td class="text-center">
                                                        <#if remark.operationTime??>
                                                            ${remark.operationTime?string("yyyy-MM-dd HH:mm:ss")}
                                                        <#else>
                                                            -
                                                        </#if>
                                                    </td>
                                                    <td class="text-left">${remark.remarkContent!''}</td>
                                                </tr>
                                            </#list>
                                            </tbody>
                                        </table>
                                    </div>
                                <#else>
                                    <div class="alert alert-info">
                                        <i class="fa fa-info-circle"></i> 暂无备注意见数据
                                    </div>
                                </#if>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>


</body>
</html>