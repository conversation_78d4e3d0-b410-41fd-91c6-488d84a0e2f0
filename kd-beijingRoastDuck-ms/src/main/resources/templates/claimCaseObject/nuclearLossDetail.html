<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>核损详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <meta content="" name="description" />
    <meta content="" name="author" />

    <!-- 引入公共CSS -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/css/components.min.css" rel="stylesheet" id="style_components" type="text/css" />
    <link href="${ctx}/css/custom.css" rel="stylesheet" type="text/css" />

    <style>
        /* 修复页面滚动问题 */
        body {
            overflow-y: auto !important; /* 确保垂直滚动 */
            height: auto !important;
        }

        #floating-container {
            position: fixed;
            right: 20px;
            top: 55%;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .draggable-box {
            width: 60px;
            height: 60px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 6px !important;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            user-select: none;
            border: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 12px;
            color: #666;
            text-align: center;
            line-height: 1.3;
            padding: 13px;
            box-sizing: border-box;
            margin-bottom: 8px;
            cursor: move;
            transition: all 0.3s ease; /* 添加过渡效果 */

        }

        /* 鼠标悬停效果 - 边框变亮 */
        .draggable-box:hover {
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); /* 内阴影 */
            transform: scale(1.1);
        }

        /* 选中状态 - 边框变亮且更明显 */
        .box-active {
            background-color: #ffffff;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); /* 内阴影 */
            transform: translateY(1px); /* 模拟按下 */
        }

        .page-container {
            min-height: 100vh;
            overflow-y: auto;
        }

        .page-content-wrapper {
            overflow-y: auto;
            min-height: calc(100vh - 70px);
        }

        .page-content {
            padding-bottom: 50px; /* 底部留出空间 */
            overflow-y: auto;
        }

        .detail-form {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .detail-form .form-group {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        .detail-form .control-label {
            font-weight: bold;
            color: #333;
            text-align: right;
            padding-right: 10px;
            white-space: nowrap;
            min-width: 120px;
            width: 120px;
            flex: 0 0 120px;
        }
        .detail-form .form-control-static {
            padding: 8px 12px;
            margin-bottom: 0;
            min-height: 32px;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            background-color: #f9f9f9;
            color: #555;
            flex: 1;
            word-break: break-all;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .section-title {
            border-left: 4px solid #3598dc;
            padding-left: 10px;
            margin: 20px 0 15px 0;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .amount-text {
            color: #e74c3c;
            font-weight: bold;
            font-size: 16px;
        }
        .page-header {
            background: #34495e;
            border-bottom: 1px solid #2c3e50;
        }
        .page-header .logo-default {
            color: white !important;
            font-size: 18px !important;
        }
        .btn-group {
            margin-bottom: 20px;
        }
        .error-message {
            color: #e74c3c;
            font-size: 16px;
            text-align: center;
            padding: 40px 0;
        }
        /* 核损按钮样式 */
        .nuclear-loss-btn {
            background-color: #cf3636 !important;
            border-color: #000000 !important;
            color: black !important;
            float: right;
            margin-top: 0px;
            margin-left: 15px;
            display: inline-block;
            font-size: 16px !important;
            padding: 8px 16px !important;
        }
        .nuclear-loss-btn:hover {
            background-color: #000000 !important;
            border-color: #000000 !important;
            color: white !important;
        }
        /* 费用表格样式 */
        .cost-table {
            margin-top: 10px;
        }
        .cost-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }
        .cost-table td {
            text-align: right;
            vertical-align: middle;
        }
        .cost-table .amount {
            color: #000000;
            font-weight: bold;
        }
        /* 自适应响应式布局 */
        .detail-form .row {
            margin-left: -8px;
            margin-right: -8px;
            margin-bottom: 8px;
        }
        .detail-form .row .col-md-4 {
            padding-left: 8px;
            padding-right: 8px;
        }

        /* 面包屑导航样式 */
        .page-breadcrumb {
            padding: 0;
            margin: 0 0 25px 0;
            background-color: transparent;
            border-radius: 0;
        }
        .page-breadcrumb > li + li:before {
            content: ">";
            padding: 0 5px;
            color: #777;
        }

        /* 页面标题样式 */
        .page-title {
            margin: 0 0 25px 0;
            padding: 0;
        }
        .page-title h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
            font-weight: 300;
        }

        /* 卡片样式优化 */
        .portlet {
            margin-bottom: 25px;
        }
        .portlet .portlet-title {
            padding: 15px 20px;
            min-height: 48px;
            border-bottom: 1px solid #eee;
            position: relative;
        }
        .portlet .portlet-title .caption {
            float: left;
            display: inline-block;
            font-size: 18px;
            line-height: 18px;
        }
        .portlet .portlet-body {
            padding: 20px;
        }

        /* 备注意见样式 */
        .remark-item {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fff;
        }
        .remark-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }
        .remark-type {
            font-weight: bold;
            color: #007bff;
        }
        .remark-meta {
            color: #6c757d;
            font-size: 0.9em;
        }
        .remark-content {
            line-height: 1.6;
            color: #495057;
        }

        /* 确保portlet和其他组件不影响滚动 */
        .portlet {
            overflow: visible !important;
            height: auto !important;
            position: static !important;
            margin-bottom: 20px !important;
            display: block !important;
            visibility: visible !important;
        }

        .portlet .portlet-title {
            overflow: visible !important;
            height: auto !important;
            position: static !important;
            display: block !important;
            visibility: visible !important;
        }

        .portlet .portlet-body {
            overflow: visible !important;
            height: auto !important;
            position: static !important;
            min-height: 50px;
            display: block !important;
            visibility: visible !important;
        }

        /* 确保表格可以正常显示 */
        .table-responsive {
            overflow-x: auto;
            overflow-y: visible !important;
        }

        .table {
            overflow: visible !important;
        }

        /* 确保按钮区域可见 */
        .btn {
            margin-right: 10px;
            min-width: 80px;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 1000 !important;
        }

        /* 确保按钮容器可见 */
        .text-right {
            display: block !important;
            visibility: visible !important;
            overflow: visible !important;
            height: auto !important;
        }

        /* 确保操作按钮区域完全可见 */
        .row:last-child {
            display: block !important;
            visibility: visible !important;
            margin-bottom: 10px !important;
        }

        /* 重置所有可能影响滚动的框架样式 */
        .page-header {
            position: static !important;
            overflow: visible !important;
            height: auto !important;
        }

        .page-sidebar {
            position: static !important;
            overflow: visible !important;
            height: auto !important;
        }

        .page-footer {
            position: static !important;
            overflow: visible !important;
            height: auto !important;
        }

        .page-header {
            background: #34495e;
            border-bottom: 1px solid #2c3e50;
        }
        .page-header .logo-default {
            color: white !important;
            font-size: 18px !important;
        }

        /* 面包屑导航样式 */
        .page-breadcrumb {
            padding: 0;
            margin: 0 0 25px 0;
            background-color: transparent;
            border-radius: 0;
        }
        .page-breadcrumb > li + li:before {
            content: ">";
            padding: 0 5px;
            color: #777;
        }

        /* 卡片样式优化 */
        .portlet {
            margin-bottom: 25px;
        }
        .portlet .portlet-title {
            padding: 15px 20px;
            min-height: 48px;
            border-bottom: 1px solid #eee;
            position: relative;
        }
        .portlet .portlet-title .caption {
            float: left;
            display: inline-block;
            font-size: 18px;
            line-height: 18px;
        }
        .portlet .portlet-body {
            padding: 20px;
        }

        /* 确保按钮区域可见 */
        .portlet .portlet-body {
            min-height: 50px;
        }

        .btn {
            margin-right: 10px;
            min-width: 80px;
        }

        .btn:last-child {
            margin-right: 0;
        }

        /* 强制显示按钮容器 */
        .text-right {
            display: block !important;
            visibility: visible !important;
        }

        .floating-button {
            position: fixed;
            right: 0.04rem;
            bottom: 2.45rem;
            width: 0.4rem;
            height: 0.4rem;
            color: black;
            border-radius: 50%;
            text-align: center;
            line-height: 0.4rem;
            font-size: 0.08rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s;
        }

        .floating-button:hover {
            transform: scale(1.1);
        }

        .floating-button2 {
            position: fixed;
            right: 0.04rem;
            bottom: 2rem;
            width: 0.4rem;
            height: 0.4rem;
            color: black;
            border-radius: 50%;
            text-align: center;
            line-height: 0.4rem;
            font-size: 0.08rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s;
        }

        .floating-button2:hover {
            transform: scale(1.1);
        }

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        .clear-padding-left {
            padding-left: 0px;
        }

        .clear-padding-right {
            padding-right: 0px;
        }

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid black;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .has-error {
            border-color: #ce0d0d !important;
            color: red !important;
        }

        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }

        .imageListChoose {
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }

        .collectionCompleted {
            background-color: #E8F2FF;
        }

        .collectionButton {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }

        .collectionButtonClick {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }

        .collectionData:hover {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }

        .middleTable {
            border: 4px solid #1767ff !important;
        }

        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .error {
            color: #ce0d0d !important;
        }

        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            width: 650px;
        }

        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thClass {
            text-align: center;
            color: #fff;
        }

        .typeSelect {
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        .collection-thumbnail-img {
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .ticket-information {
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea {
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }

        .selected-dataDisplayArea {
            background-color: #91cae9;
        }

        .submit-dataDisplayArea {
            border: 3px solid #44db69 !important;
        }

        .change-dataDisplayArea {
            border: 3px solid red !important;
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .dataDisplayArea-head {
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }

        .dataDisplayArea-head-left {
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }

        .dataDisplayArea-head-right {
            text-align: right;
            padding: 0px !important;
        }

        .dataDisplayArea-body-left {
            opacity: 0.7;
        }

        .dataDisplayArea-body-right {
            text-align: right;
        }

        .row {
            margin: 0px 0px;
        }

        .subject-name {
            font-size: 20px;
        }

        .estimateInventoryDataBlue {
            background-color: #1676ff;
        }

        .line-center {
            display: inline-block;
            background-color: blue;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            color: white;
            text-align: center;
            line-height: 30px;
            font-size: 18px;
            font-weight: bold;
        }

        .icon-plus {
            font-size: 30px;
            line-height: 23px;
        }

        .icon-plus:hover {
            cursor: pointer;
        }

        .dataDisplayArea-head-img {
            float: right;
            width: 30px;
            height: 25px;
            margin: 10px 20px 10px 0px;
        }

        .dataDisplayArea-head-img:hover {
            cursor: pointer;
        }

        .form-control {
            height: 28px !important;
        }

        .label-right {
            font-size: 13px;
            text-align: right;
        }

        .align-item-center {
            display: flex;
            align-items: center;

        }

        .col-sm-1 {
            text-align: center;
        }

        textarea[class='layui-layer-input'] {
            outline: 0 !important;
            width: 700px !important;
            height: 350px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        button[name='actionBtn'] {
            color: white;
        }

        select {
            padding: 0px 12px !important;
        }

        div[name='dataCollectAreaRow'] input {
            padding: 0px 5px;
        }


        #firstRegistrationTime[readonly]{
            background-color: white;
        }

        #lossAssessmentTime[readonly]{
            background-color: white;
        }

        #thumbnail p {
            margin: 10px 0px 5px -15px;
            font-size: 10px;
            text-align: left;
        }

        .viewer-canvas h2 {
            z-index: 9999999;
            position: relative;
            left: 20px;
            font-weight: bold;
        }

        /* 侧边栏样式 */
        .sidebar-nav {
            width: 100%;
            padding: 10px 0;
            background-color: #f5f5f5;
            border-radius: 4px;
        }

        .nav-list {
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
        }

        .nav-item {
            border-bottom: 1px solid #e0e0e0;
        }

        .nav-link {
            display: block;
            padding: 8px 15px;
            color: #333;
            text-decoration: none;
        }

        .nav-link:hover {
            background-color: #e9e9e9;
        }

        .nav-link i {
            margin-right: 8px;
            transition: transform 0.3s ease;
        }

        .nav.collapse {
            display: none;
            padding-left: 20px;
        }

        .nav.collapse.in {
            display: block;
        }

        /* 展开状态的箭头旋转 */
        .nav-link[aria-expanded="true"] i.glyphicon-chevron-right {
            transform: rotate(90deg);
        }

        /* 数量标签样式 */
        .nav-link span.badge {
            margin-left: 5px;
            background-color: #777;
        }

        @media (min-width: 992px) {
            .page-content-wrapper .page-content {
                margin-left: 0px!important;
            }
        }

        .center-box {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            justify-content: center;
            align-items: center;

            /* 添加粘性定位 */
            position: sticky;
            top: 0; /* 固定在顶部 */
            z-index: 1000;

            /* 过渡动画 */
            transition: all 0.3s ease;
        }

        /* 滚动时添加的样式（可选） */
        .fixed-style {
            width: 100%;
            position: fixed;
            border-radius: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="page-header-fixed page-content-white">

    <!-- 页面容器 -->
    <div class="page-container" style="margin-top: 0px">
        <!-- 页面内容包装器 -->
        <div class="page-content-wrapper">
            <!-- 页面内容 -->
            <div class="page-content" style="margin-left: 0;">
                <!-- 面包屑导航 -->
                <div class="page-bar">
                    <ul class="page-breadcrumb">
                        <li>
                            <a href="${ctx}/nuclearLossTask/toNuclearLossTaskManage">核损管理</a>
                        </li>
                        <li>核损详情</li>
                    </ul>
                </div>

                <!-- 案件信息和快捷操作栏 -->
                <div class="row center-box" id="stickyBox" style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group" style="margin-bottom: 0;">
                                        <label class="control-label" style="font-weight: bold;">报案号:</label>
                                        <span style=" font-size: 14px;">${claimCase.claimCaseNo!''}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group" style="margin-bottom: 0;">
                                        <label class="control-label" style="font-weight: bold;">立案号:</label>
                                        <span style="margin-left: 10px; font-size: 14px;">${claimCase.insuranceCaseNo!''}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="pull-right">
                                <button type="button" class="btn btn-default btn-sm red" onclick="nuclearDamage()">
                                    核损
                                </button>
                                <button type="button" class="btn btn-default btn-sm yellow" onclick="seeLossAssessment()">
                                    查看核损信息
                                </button>
                                <button type="button" class="btn btn-default btn-sm" onclick="caseObjectBack('BAX24')"style="color: #f5f5f5;background-color: #afa7a7;border-color: #fff;">
                                    退回
                                </button>
                                <button type="button" class="btn btn-default btn-sm blue" onclick="caseObjectPass('BAX25')">
                                    通过
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 核损费用合计 -->
                    <div class="row">
                        <div class="">
                            <div class="portlet light bordered">
                                <div class="portlet-title">
                                    <div class="caption">
                                        <i class="icon-bar-chart font-dark"></i>
                                        <span class="caption-subject font-dark sbold uppercase">核损费用合计</span>
                                    </div>
                                </div>
                                <div class="portlet-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered cost-table">
                                            <thead>
                                            <tr>
                                                <th width="5%">类型</th>
                                                <th width="10%">合计配件金额</th>
                                                <th width="11%">配件管理费金额</th>
                                                <th width="10%">合计残值金额</th>
                                                <th width="9%">合计自付金额</th>
                                                <th width="9%">合计工时金额</th>
                                                <th width="10%">合计辅料金额</th>
                                                <th width="10%">合计外修金额</th>
                                                <th width="12%">合计施救费用金额</th>
                                                <th width="15%">总合计</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <#list costList as cost>
                                            <tr>
                                                <td class="amount" style="text-align: center;">${cost.assessmentType!cost.assessmentType!''}</td>
                                                <td class="amount"><#if cost.totalPartsAmount??>${cost.totalPartsAmount?string("0.00")}<#else>0.00</#if></td>
                                                <td class="amount"><#if cost.partsManagementFee??>${cost.partsManagementFee?string("0.00")}<#else>0.00</#if></td>
                                                <td class="amount"><#if cost.totalResidualValue??>${cost.totalResidualValue?string("0.00")}<#else>0.00</#if></td>
                                                <td class="amount"><#if cost.totalOutOfPocket??>${cost.totalOutOfPocket?string("0.00")}<#else>0.00</#if></td>
                                                <td class="amount"><#if cost.totalLaborAmount??>${cost.totalLaborAmount?string("0.00")}<#else>0.00</#if></td>
                                                <td class="amount"><#if cost.totalSuppliesAmount??>${cost.totalSuppliesAmount?string("0.00")}<#else>0.00</#if></td>
                                                <td class="amount"><#if cost.totalExternalRepair??>${cost.totalExternalRepair?string("0.00")}<#else>0.00</#if></td>
                                                <td class="amount"><#if cost.totalRescueFee??>${cost.totalRescueFee?string("0.00")}<#else>0.00</#if></td>
                                                <td class="amount" style="color: red"><#if cost.totalExcludingRescue??>${cost.totalExcludingRescue?string("0.00")}<#else>0.00</#if></td>
                                            </tr>
                                            </#list>
                                            </tbody>
                                        </table>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>

                <!-- 备注意见 -->
                <div class="row">
                    <div class="">
                        <div class="portlet light bordered">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="icon-speech font-dark"></i>
                                    <span class="caption-subject font-dark sbold uppercase">影像资料</span>
                                </div>
                            </div>
                            <div class="portlet-body">
                                <#include "/attach/attachComponent.html">

                            </div>
                        </div>
                    </div>
                </div>

                <!-- 错误信息显示 -->
                <#if error??>
                <div class="error-message">
                    <i class="fa fa-exclamation-triangle"></i> <span>${error}</span>
                </div>
                </#if>

            <!-- 拖拽浮窗 -->
            <div id="floating-container">
                <div id="box1" class="draggable-box box-active" onclick="showBasicInformation('${claimCase.id!''}', '${claimCaseObject.id!''}')">
                    基本信息
                </div>
                <div id="box2" class="draggable-box" onclick="showDetailLog('${claimCaseObject.id!''}')">
                    查看日志
                </div>
            </div>
        </div>
    </div>

    <!-- 引入公共JS -->
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>

    <script src="${ctx}/js/idCardCheck.js" type="text/javascript"></script>

    <script type="text/javascript">
        jQuery(document).ready(function() {
            App.init();
        });

        window.addEventListener('scroll', function() {
            const box = document.getElementById('stickyBox');
            if (window.scrollY > 200) { // 滚动超过200px时添加额外样式
                box.classList.add('fixed-style');
            } else {
                box.classList.remove('fixed-style');
            }
        });


        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        // 核损
        function nuclearDamage() {
            let claimCaseObject = assembleData(null);

            $.ajax({
                url: '${ctx}/nuclearLossTaskController/callLossAssessment',
                type: 'POST',
                data: JSON.stringify(claimCaseObject),
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0")  {
                        $("button[name='actionBtn']").removeAttr("disabled");
                        window.open(result.url);
                    }else {
                        $("button[name='actionBtn']").removeAttr("disabled");
                        alert(data.msg);
                    }
                },
                error: function (data) {
                    $("button[name='actionBtn']").removeAttr("disabled");
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        // 查看核損信息
        function seeLossAssessment(){
            let claimCaseObject = assembleData(status);

            $.ajax({
                url: '${ctx}/claimCaseObject4CarController/seeLossAssessment',
                type: 'POST',
                data: JSON.stringify(claimCaseObject),
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0")  {
                        $("button[name='actionBtn']").removeAttr("disabled");
                        window.open(result.url);
                    }else {
                        $("button[name='actionBtn']").removeAttr("disabled");
                        alert(data.msg);
                    }
                },
                error: function (data) {
                    $("button[name='actionBtn']").removeAttr("disabled");
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        // 退回
        function caseObjectBack(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                layer.prompt({
                    title: '请输入意见备注',
                    formType: 2, // 0-文本输入框（默认），1-密码框，2-多行文本框
                    value: '', // 默认内容
                    maxlength: 100, // 最大输入长度
                    area: ['750px', '500px'],
                    btn: ['确定', '取消'],
                    yes: function (index, elem) {
                        var value = elem.find('.layui-layer-input').val().trim();
                        claimCaseObject["logRemark"] = value;
                        if(value==null || value==""){
                            layer.msg('请输入退回意见');
                            layer.close(index);
                        }else{
                            $.ajax({
                                url: "${ctx}/nuclearLossTaskController/nuclearLossBackOrPass",
                                type: 'POST',
                                data: JSON.stringify(claimCaseObject),
                                async: true,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    if (result.ret == "0") {
                                        layer.msg(result.msg || '操作成功', {
                                            icon: 1,
                                            offset: ['450px', ''],
                                            time: 2000,
                                            shade: [0.0001, '#000']
                                        }, function () {
                                            // 提交成功后返回任务列表页面
                                            window.close();
                                            // window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=2&status=3&checkCondition=gs";
                                        });
                                    } else {
                                        layer.msg(result.msg || '操作失败', {
                                            icon: 2,
                                            offset: ['450px', ''],
                                            time: 2000,
                                            shade: [0.0001, '#000']
                                        });
                                    }

                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                    console.log(result.msg);
                                    layer.msg('网络请求失败，请稍后重试', {
                                        icon: 2,
                                        offset: ['450px', ''],
                                        time: 2000
                                    });
                                }

                            });
                        }



                    }
                })
            }
        }

        //通过
        function caseObjectPass(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                claimCaseObject["reason"] = "初审通过";
                console.log(JSON.stringify(claimCaseObject));
                $.ajax({
                    url: "${ctx}/nuclearLossTaskController/nuclearLossBackOrPass",
                    type: 'POST',
                    data: JSON.stringify(claimCaseObject),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {

                            window.location.href = '${ctx}/nuclearLossTaskController/submitConfirmPage?claimCaseObjectId=${claimCaseObject.id}&comeFrom=${comeFrom}&status=' + result.status;
                        } else {
                            layer.msg(result.msg || '操作失败', {
                                icon: 2,
                                offset: ['450px', ''],
                                time: 2000,
                                shade: [0.0001, '#000']
                            });
                        }

                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        console.log(result.msg);
                        layer.msg('网络请求失败，请稍后重试', {
                            icon: 2,
                            offset: ['450px', ''],
                            time: 2000
                        });
                    }

                });
            }
        }
        //可拖动基本信息、查看日志
        const container = document.getElementById('floating-container');
        const boxes = {
            box1: document.getElementById('box1'),
            box2: document.getElementById('box2')
        };

        const controlButtons = document.querySelectorAll('.control-btn');
        let activeBox = boxes.box1;
        let isDragging = false;
        let offsetX, offsetY;

        // 切换活动浮框
        function setActiveBox(box) {
            activeBox.classList.remove('box-active');
            activeBox = box;
            activeBox.classList.add('box-active');
        }

        // 控制面板按钮事件
        controlButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                setActiveBox(boxes[this.dataset.target]);
            });
        });

        // 容器拖拽事件
        container.addEventListener('mousedown', function(e) {
            // 只有当点击的是容器或浮框时才触发拖拽
            if (e.target === container || e.target.classList.contains('draggable-box')) {
                isDragging = true;
                offsetX = e.clientX - container.getBoundingClientRect().left;
                offsetY = e.clientY - container.getBoundingClientRect().top;

                container.style.transition = 'none';
                Array.from(container.children).forEach(box => {
                    box.classList.add('dragging');
                });

                e.preventDefault();
            }
        });

        // 鼠标移动事件
        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;

            let newLeft = e.clientX - offsetX;
            let newTop = e.clientY - offsetY;

            // 限制位置（防止移出屏幕）
            const maxLeft = window.innerWidth - container.offsetWidth;
            const maxTop = window.innerHeight - container.offsetHeight;

            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));

            container.style.left = newLeft + 'px';
            container.style.top = newTop + 'px';
            container.style.right = 'auto';
        });

        // 鼠标释放事件
        document.addEventListener('mouseup', function(e) {
            if (!isDragging) return;
            isDragging = false;

            container.style.transition = 'all 0.3s ease';
            Array.from(container.children).forEach(box => {
                box.classList.remove('dragging');
            });

            // 判断吸附到左侧还是右侧
            const containerRect = container.getBoundingClientRect();
            const screenCenter = window.innerWidth / 2;
            const containerCenter = containerRect.left + containerRect.width / 2;

            if (containerCenter < screenCenter) {
                container.style.left = '20px';
                container.style.right = 'auto';
            } else {
                container.style.left = 'auto';
                container.style.right = '20px';
            }
        });

        // 窗口大小变化时重新定位
        window.addEventListener('resize', function() {
            const containerRect = container.getBoundingClientRect();

            if (parseInt(container.style.left) > 0 || container.style.left === '') {
                container.style.left = '20px';
                container.style.right = 'auto';
            } else {
                container.style.left = 'auto';
                container.style.right = '20px';
            }

            const maxTop = window.innerHeight - container.offsetHeight;
            const currentTop = parseInt(container.style.top || containerRect.top);
            container.style.top = Math.max(0, Math.min(currentTop, maxTop)) + 'px';
        });

        // 点击浮框切换活动状态
        Object.values(boxes).forEach(box => {
            box.addEventListener('click', function() {
                setActiveBox(this);
            });
        });

        // 查看历史案件
        function viewHistoryCases(taskId) {
            alert('历史案件功能开发中，敬请期待！');
            // TODO: 实现历史案件查看功能
        }

        // 导出核损单
        function exportNuclearReport(taskId) {
            alert('导出核损单功能开发中，敬请期待！');
            // TODO: 实现导出核损单功能
        }

        // 查看基本信息
        function showBasicInformation(claimCaseId, claimCaseObjectId){
            layer.open({
                title: "查看基本信息",
                type: 2,
                offset: 'auto',
                area: ['1200px', '700px'],
                fixed: false,
                closeBtn: 1,
                maxmin: true,
                content: "${ctx}/insuranceNuclearAudit/showBasicInformation?claimCaseId=" + claimCaseId + "&claimCaseObjectId=" + claimCaseObjectId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        // 查看日志
        function showDetailLog(claimCaseObjectId){
            layer.open({
                type: 2,
                title: '查看日志',
                content: "${ctx}/insuranceNuclearAudit/showDetailLog4Ins?claimCaseObjectId=" + claimCaseObjectId,
                area: ['1200px', '400px'],
                maxmin: true, // 启用最大化按钮
                scrollbar: false, // 禁用layer自带的滚动条
                success: function(layero, index) {
                    console.log('弹框当前处于状态');
                    layer.iframeAuto(index);
                },
                full: function(layero) {
                    console.log('弹框当前处于最大化状态');
                    // 最大化时添加全屏类
                    var iframe = $(layero).find('iframe');
                    try {
                        var iframeDoc = iframe[0].contentDocument || iframe[0].contentWindow.document;
                        $(iframeDoc).find('.table-responsive').css({
                            'max-height': 'none',
                            'overflow-y': 'visible'
                        });
                        $(iframeDoc).find('tbody tr').show();

                    } catch(e) {
                        console.log('样式注入错误:', e);
                    }
                },
                restore: function(layero) {
                    console.log('弹框当前处于恢复状态');
                    // 恢复时移除全屏类
                    var iframe = $(layero).find('iframe');
                    try {
                        var iframeDoc = iframe[0].contentDocument || iframe[0].contentWindow.document;
                        $(iframeDoc).find('.table-responsive').css({
                            'max-height': 'calc(6 * 40px + 41px)',
                            'overflow-y': 'auto'
                        });
                    } catch(e) {
                        console.log('样式注入错误:', e);
                    }
                }
            });
        }

        // 组装数据
        function assembleData(status) {


            $("button[name='actionBtn']").attr('disabled','1');


            let category = "${claimCaseObject.category}";

            let isError = false;
            let errorMsg = "";


            let object = {
                id: "${claimCaseObject.id}",
                claimCaseId: "${claimCaseObject.claimCaseId}",
                claimCaseNo: "${claimCaseObject.claimCaseNo}",
                type: "${claimCaseObject.type}",
                category: "${claimCaseObject.category}",
                status: status
            };


            if(isError){
                layer.msg(errorMsg, {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }

            console.log(JSON.stringify(object));

            return object;
        }

        let isCostDataLoading = false;
        let feeInfoJsonStr ;
        let isInitFeeInfo = true;
        let costRefreshTimer = null;

        $(function (){
            stopCostAutoRefresh();
            findFeeInfo()
            // 每隔5秒查询损失费用合计信息
            costRefreshTimer = setInterval(function () {
                findFeeInfo()
            }, 3000)
            console.log("costRefreshTimer=" + costRefreshTimer)
        })

        // 停止自动刷新
        function stopCostAutoRefresh() {
            if (costRefreshTimer) {
                console.log("停止循环......")
                clearInterval(costRefreshTimer);
                costRefreshTimer = null;
            }
        }

        function showCostError(msg) {
            layer.msg(msg || '网络请求失败，请稍后重试！', {
                icon: 2,
                offset: ['450px', ''],
                time: 2000
            });
        }

        //损失费用合计查询方法 Ajax获取费用数据
        function findFeeInfo() {
            // 可以添加加载指示器
            $.ajax({
                url: '${ctx}/insuranceNuclearAudit/findCostDataByLossNo?claimCaseObjectId=${claimCaseObject.id}', // 替换为实际接口地址
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function(data) {
                    let resData = JSON.stringify(data)
                    if(feeInfoJsonStr != resData){
                        feeInfoJsonStr = resData
                        updateCostTable(JSON.parse(resData));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('获取费用数据失败:', error);
                    stopCostAutoRefresh();
                    showCostError('获取费用数据失败，请稍后重试');
                },
                complete: function() {
                    isCostDataLoading = false;

                }
            });
        }

        // 更新费用表格
        function updateCostTable(costList) {
            const $container = $('.table-responsive');
            let flag = 0;
            $container.html('');
            let tableDiv = $('<table class="table table-bordered cost-table"></table>')
            tableDiv.append('<thead><tr><th width="8%">类型</th><th width="10%">合计配件金额</th><th width="10%">配件管理费金额</th><th width="10%">合计残值金额</th><th width="10%">合计自付金额</th><th width="10%">合计工时金额</th><th width="10%">合计辅料金额</th><th width="10%">合计外修金额</th><th width="10%">合计施救费用金额</th><th width="15%">总合计</th></tr></thead>');
            let tbodyDiv = $('<tbody></tbody>')
            if (costList && costList.length > 0) {
                for(let cost of costList){
                    tbodyDiv.append('<tr><td class="amount" style="text-align: center;">'+cost.assessmentType
                        +'</td><td class="amount">'+(parseFloat(cost.totalPartsAmount) || 0).toFixed(2)
                        +'</td><td class="amount">'+(parseFloat(cost.partsManagementFee) || 0).toFixed(2)
                        +'</td><td class="amount">'+(parseFloat(cost.totalResidualValue) || 0).toFixed(2)
                        +'</td><td class="amount">'+(parseFloat(cost.totalOutOfPocket) || 0).toFixed(2)
                        +'</td><td class="amount">'+(parseFloat(cost.totalLaborAmount) || 0).toFixed(2)
                        +'</td><td class="amount">'+(parseFloat(cost.totalSuppliesAmount) || 0).toFixed(2)
                        +'</td><td class="amount">'+(parseFloat(cost.totalExternalRepair) || 0).toFixed(2)
                        +'</td><td class="amount">'+(parseFloat(cost.totalRescueFee) || 0).toFixed(2)
                        +'</td><td class="amount" style="font-weight: bold; color: #e74c3c;"> '+(parseFloat(cost.totalExcludingRescue) || 0).toFixed(2)+'</td></tr>')
                    if(cost.assessmentType === '初审') {
                        flag = flag + 1;
                    }
                }

            }
            if (flag == 0) {
                tbodyDiv.append('<tr><td class="amount" style="text-align: center;">初审</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount" style="font-weight: bold; color: #e74c3c;">0.00</td></tr>')
            }
            tableDiv.append(tbodyDiv)
            $container.append(tableDiv);

            if(isInitFeeInfo){
                isInitFeeInfo = false
            }else {
/*                layer.msg('损失费用合计已变更', {
                    icon: 1,
                    offset: ['200px', ''],
                    time: 2000
                });*/
            }
        }

    </script>
</body>
</html>