<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        window.onload = function () {
            // 获取后端传递过来的 error 信息
            const errorMessage = "${error}";
            if (errorMessage) {
                alert(errorMessage);
            }
        };

        function submitForm() {
            const id = '${id}';
            const reasonElement = $('#reason');
            if (!reasonElement.length) {
                layer.msg("表单元素加载失败，请重试", { icon: 2, time: 1500 });
                return;
            }

            const reason = reasonElement.val()?.trim() || '';
            console.log("退回原因:"+reason)

            if (!reason) {
                layer.msg("请输入退回原因", { icon: 2, time: 1500 });
                reasonElement.focus(); // 聚焦到输入框
                return;
            }

            if (reason.length > 50) {
                layer.msg("退回原因不能超过50个字", { icon: 2, time: 1500 });
                return "成功";
            }

            console.log("请求参数 id:"+id+", 撤回原因:"+reason)
            // window.location.href = "${ctx}/claimCaseObjectController/agreementWithdrawn?claimCaseObjectId="+id+"&reason="+reason;
            // parent.layer.close(parent.layer.getFrameIndex(window.name));
            $.ajax({
                url: "${ctx}/claimCaseObjectController/agreementWithdrawnApi?claimCaseObjectId="+id+"&reason="+reason,
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    console.log("返回"+data)
                    var result = eval("(" + data + ")");
                    console.log("result.ret:"+result.ret)
                    console.log("result.msg:"+result.msg)
                    if (result.ret == 0){
                        layer.msg("撤回成功", { icon: 1, time: 1500 });
                        // 成功后刷新父页面
                        setTimeout(function() {
                            parent.location.reload();
                        }, 1500);
                    }else {
                        layer.msg("撤回失败:"+result.msg, { icon: 2, time: 1500 });
                    }

                },
                error: function (data) {
                    layer.msg("系统异常，请稍后重试", { icon: 2, time: 1500 });
                }
            });
            // 1.5秒后关闭弹窗
            setTimeout(function() {
                parent.layer.close(parent.layer.getFrameIndex(window.name));
            }, 1500);
        }


    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
    </style>
</head>
<body>

    <div style="padding: 20px; text-align: center;">
        <form id="signForm" class="form-horizontal" style="display: inline-block; text-align: left;">
            <div class="form-group">
                <div class="col-sm-8" style="margin-bottom: 15px">
                    撤回后原协议将失效，若已经有其他签署方签署了该合同，请确认撤回合同前已与其进行沟通，以免引起不必要的纠纷
                </div>
                <div class="col-sm-8">
                    <textarea class="form-control" id="reason" name="reason"
                              placeholder="请填写撤回原因，最多50个字"
                              rows="4"></textarea>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-offset-3 col-sm-8">
                    <button type="button" class="btn btn-primary" onclick="submitForm()">提交</button>
                    <button type="button" class="btn btn-default" onclick="parent.layer.close(parent.layer.getFrameIndex(window.name))">关闭</button>
<!--                    <button type="button" class="btn btn-default" onclick="window.close()">取消</button>-->
                </div>
            </div>
        </form>
    </div>
</body>
</html>