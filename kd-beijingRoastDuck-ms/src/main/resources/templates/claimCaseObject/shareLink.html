<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>分享链接</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/js/jquery.qrcode.js" type="text/javascript"></script>

    <!-- 预加载图标 -->
    <img id="alipayIcon" src="${ctx}/images/alipay-icon.png" style="display: none;" />
    <img id="wechatIcon" src="${ctx}/images/wechat-icon.png" style="display: none;" />

    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            font-family: "Open Sans", sans-serif;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            margin-bottom: 15px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .content-wrapper {
            padding: 0 15px 15px;
            max-width: 500px;
            margin: 0 auto;
        }

        .person-tabs {
            display: flex;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }

        .person-tab {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            min-width: 80px;
            text-align: center;
        }

        .person-tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background-color: #f8f9ff;
        }

        .person-tab:hover {
            color: #007bff;
            background-color: #f8f9ff;
        }

        .content-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
        }

        .section-title {
            font-size: 14px;
            color: #495057;
            margin-bottom: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .section-title i {
            color: #007bff;
        }

        .link-input-group {
            display: flex;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            transition: border-color 0.3s ease;
        }

        .link-input-group:focus-within {
            border-color: #007bff;
            box-shadow: 0 0 0 0.1rem rgba(0,123,255,.15);
        }

        .link-input {
            flex: 1;
            padding: 8px 12px;
            border: none;
            outline: none;
            font-size: 13px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
        }

        .copy-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .copy-btn:hover {
            background: #0056b3;
        }

        .qr-section {
            text-align: center;
        }

        .template-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #495057;
            padding: 8px 15px;
            background: linear-gradient(135deg, #f8f9ff, #e3f2fd);
            border-radius: 6px;
            border-left: 3px solid #007bff;
        }

        .qr-container {
            display: inline-block;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
        }

        .qr-support-text {
            font-size: 12px;
            color: #6c757d;
            margin: 10px 0 8px 0;
        }

        .qr-support-apps {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .support-app {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .support-app-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .support-app-icon img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .support-app-name {
            font-size: 11px;
            color: #6c757d;
        }

        .qr-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: nowrap;
        }

        .qr-btn {
            padding: 10px 20px;
            border: 1px solid #007bff;
            background-color: white;
            color: #007bff;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
            min-width: 110px;
            justify-content: center;
            flex: 1;
            max-width: 140px;
        }

        .qr-btn:hover {
            background-color: #007bff;
            color: white;
            text-decoration: none;
        }

        .qr-btn.primary {
            background: #007bff;
            color: white;
        }

        .qr-btn.primary:hover {
            background: #0056b3;
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .empty-state i {
            font-size: 64px;
            color: #dee2e6;
            margin-bottom: 20px;
        }

        .empty-state p {
            font-size: 16px;
            margin: 0;
        }

        .error-state {
            text-align: center;
            padding: 40px 20px;
            color: #dc3545;
            background: #fff5f5;
            border: 1px solid #f5c6cb;
            border-radius: 12px;
            margin: 20px 0;
        }

        .error-state i {
            font-size: 48px;
            margin-bottom: 15px;
        }

        /* Toast 提示样式 */
        .toast {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .toast.success {
            background-color: #28a745;
        }

        .toast.error {
            background-color: #dc3545;
        }

        .toast.show {
            opacity: 1;
        }
        
        @media (max-width: 576px) {
            .share-content {
                width: 95%;
                margin: 10px;
            }
            
            .person-tabs {
                flex-wrap: wrap;
            }
            
            .person-tab {
                padding: 8px 15px;
                font-size: 13px;
            }
            
            .qr-actions {
                gap: 10px;
            }

            .qr-btn {
                padding: 8px 15px;
                font-size: 13px;
                min-width: 90px;
            }
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
<#--    <div class="page-header">-->
<#--        <h1 class="page-title">-->
<#--            <i class="fa fa-share-alt"></i> 分享链接-->
<#--        </h1>-->
<#--    </div>-->

    <div class="content-wrapper">
        <!-- 错误信息显示 -->
        <#if error?exists>
            <div class="error-state">
                <i class="fa fa-exclamation-triangle"></i>
                <p>${error}</p>
            </div>
        <#elseif jsonList?exists && jsonList?size gt 0>
            <!-- 签署人标签页 -->
            <div class="person-tabs" id="personTabs">
                <#list jsonList as item>
                    <div class="person-tab <#if item_index == 0>active</#if>"
                         onclick="switchPerson(${item_index})"
                         data-index="${item_index}">
                        <i class="fa fa-user"></i> ${item.personName!'签署人' + (item_index + 1)}
                    </div>
                </#list>
            </div>

            <!-- 链接分享区域 -->
            <div class="content-card">
                <div class="section-title">
                    <i class="fa fa-link"></i> 链接分享
                </div>
                <div class="link-input-group">
                    <input type="text" class="link-input" id="linkInput" readonly>
                    <button class="copy-btn" onclick="copyLink()">
                        <i class="fa fa-copy"></i> 复制链接
                    </button>
                </div>
            </div>

            <!-- 二维码分享区域 -->
            <div class="content-card qr-section">
                <div class="section-title">
                    <i class="fa fa-qrcode"></i> 二维码分享
                </div>
                <div class="template-name" id="templateName"></div>
                <div class="qr-container">
                    <div id="qrcode"></div>
                </div>
                <div class="qr-support-text">-支持以下应用扫码-</div>
                <div class="qr-support-apps">
                    <div class="support-app">
                        <div class="support-app-icon">
                            <img src="${ctx}/images/alipay-icon.png" alt="支付宝" />
                        </div>
                        <div class="support-app-name">支付宝</div>
                    </div>
                    <div class="support-app">
                        <div class="support-app-icon">
                            <img src="${ctx}/images/wechat-icon.png" alt="微信" />
                        </div>
                        <div class="support-app-name">微信</div>
                    </div>
                </div>
                <div class="qr-actions">
                    <button class="qr-btn" onclick="copyQRCode()">
                        <i class="fa fa-copy"></i> 复制二维码
                    </button>
                    <button class="qr-btn primary" onclick="downloadQRCode()">
                        <i class="fa fa-download"></i> 下载二维码
                    </button>
                </div>
            </div>
        <#else>
            <div class="empty-state">
                <i class="fa fa-info-circle"></i>
                <p>暂无分享数据</p>
            </div>
        </#if>
    </div>

    <script type="text/javascript">
        // 数据存储
        var shareData = [
            <#if jsonList?exists>
                <#list jsonList as item>
                {
                    shortUrl: "${item.shortUrl!''}",
                    fileName: "${item.fileName!''}",
                    personName: "${item.personName!''}"
                }<#if item_has_next>,</#if>
                </#list>
            </#if>
        ];
        
        var currentPersonIndex = 0;
        var currentQRCanvas = null;

        // Toast 提示函数
        function showToast(message, type) {
            // 移除已存在的toast
            $('.toast').remove();

            // 创建新的toast
            var toast = $('<div class="toast ' + type + '">' + message + '</div>');
            $('body').append(toast);

            // 显示toast
            setTimeout(function() {
                toast.addClass('show');
            }, 10);

            // 1秒后隐藏并移除toast
            setTimeout(function() {
                toast.removeClass('show');
                setTimeout(function() {
                    toast.remove();
                }, 300);
            }, 1000);
        }

        // 添加roundRect方法支持（兼容性处理）
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }

        // 初始化页面
        $(document).ready(function() {
            if (shareData.length > 0) {
                switchPerson(0);
            }
        });
        
        // 切换签署人
        function switchPerson(index) {
            if (index < 0 || index >= shareData.length) return;
            
            currentPersonIndex = index;
            var data = shareData[index];
            
            // 更新标签页状态
            $('.person-tab').removeClass('active');
            $('.person-tab[data-index="' + index + '"]').addClass('active');
            
            // 更新链接输入框
            $('#linkInput').val(data.shortUrl);
            
            // 更新模板名称
            $('#templateName').text(data.fileName);
            
            // 生成二维码
            generateQRCode(data.shortUrl);
        }
        
        // 生成二维码
        function generateQRCode(url) {
            if (!url) return;

            // 清空之前的二维码
            $('#qrcode').empty();

            // 生成新的二维码
            $('#qrcode').qrcode({
                render: 'canvas',
                width: 180,
                height: 180,
                text: url,
                background: '#ffffff',
                foreground: '#000000'
            });

            // 保存canvas引用
            currentQRCanvas = $('#qrcode canvas')[0];
        }

        // 绘制支付宝图标（使用真实PNG图片）
        function drawAlipayIcon(ctx, x, y, size, callback) {
            var alipayImg = document.getElementById('alipayIcon');
            if (alipayImg && alipayImg.complete) {
                ctx.drawImage(alipayImg, x, y, size, size);
                if (callback) callback();
            } else {
                // 如果图片未加载完成，等待加载
                alipayImg.onload = function() {
                    ctx.drawImage(alipayImg, x, y, size, size);
                    if (callback) callback();
                };
            }
        }

        // 绘制微信图标（使用真实PNG图片）
        function drawWechatIcon(ctx, x, y, size, callback) {
            var wechatImg = document.getElementById('wechatIcon');
            if (wechatImg && wechatImg.complete) {
                ctx.drawImage(wechatImg, x, y, size, size);
                if (callback) callback();
            } else {
                // 如果图片未加载完成，等待加载
                wechatImg.onload = function() {
                    ctx.drawImage(wechatImg, x, y, size, size);
                    if (callback) callback();
                };
            }
        }

        // 生成带支持应用信息的二维码图片（异步版本）
        function generateQRCodeWithApps(canvas, callback) {
            if (!canvas) {
                if (callback) callback(null);
                return;
            }

            // 创建新的canvas，包含二维码和支持应用信息
            var newCanvas = document.createElement('canvas');
            var ctx = newCanvas.getContext('2d');

            // 设置新canvas尺寸 - 增加高度和宽度
            newCanvas.width = 280;
            newCanvas.height = 400;

            // 白色背景
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, newCanvas.width, newCanvas.height);

            // 绘制边框
            ctx.strokeStyle = '#e9ecef';
            ctx.lineWidth = 2;
            ctx.strokeRect(1, 1, newCanvas.width - 2, newCanvas.height - 2);

            // 绘制原二维码 - 居中显示
            var qrSize = 200;
            var qrX = (newCanvas.width - qrSize) / 2;
            var qrY = 30;
            ctx.drawImage(canvas, qrX, qrY, qrSize, qrSize);

            // 添加分隔线
            ctx.strokeStyle = '#e9ecef';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(40, qrY + qrSize + 20);
            ctx.lineTo(newCanvas.width - 40, qrY + qrSize + 20);
            ctx.stroke();

            // 添加支持应用文字
            ctx.fillStyle = '#6c757d';
            ctx.font = '14px "Microsoft YaHei", Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('支持以下应用扫码', newCanvas.width / 2, qrY + qrSize + 45);

            // 计算图标位置 - 居中排列
            var iconSize = 32;  // 减小图标尺寸 (48 * 2/3 = 32)
            var iconSpacing = 60;
            var totalWidth = iconSize * 2 + iconSpacing;
            var startX = (newCanvas.width - totalWidth) / 2;
            var iconY = qrY + qrSize + 80;  // 保持图标位置

            // 添加应用名称
            ctx.fillStyle = '#495057';
            ctx.font = '14px "Microsoft YaHei", Arial, sans-serif';  // 增加字体大小
            ctx.textAlign = 'center';
            ctx.fillText('支付宝', startX + iconSize/2, iconY + iconSize + 25);  // 增加文字位置
            ctx.fillText('微信', startX + iconSize + iconSpacing + iconSize/2, iconY + iconSize + 25);

            // 计数器，确保两个图标都绘制完成后再回调
            var iconsLoaded = 0;
            var totalIcons = 2;

            function checkComplete() {
                iconsLoaded++;
                if (iconsLoaded >= totalIcons && callback) {
                    callback(newCanvas);
                }
            }

            // 绘制支付宝图标
            drawAlipayIcon(ctx, startX, iconY, iconSize, checkComplete);

            // 绘制微信图标
            drawWechatIcon(ctx, startX + iconSize + iconSpacing, iconY, iconSize, checkComplete);
        }
        
        // 复制链接功能
        function copyLink() {
            var linkInput = document.getElementById('linkInput');
            if (!linkInput.value) {
                showToast('没有可复制的链接', 'error');
                return;
            }

            // 创建临时input元素
            var tempInput = document.createElement('input');
            tempInput.value = linkInput.value;
            document.body.appendChild(tempInput);

            // 选中并复制
            tempInput.select();
            tempInput.setSelectionRange(0, 99999);

            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    showToast('链接已复制到剪贴板', 'success');
                } else {
                    showToast('复制失败，请手动复制', 'error');
                }
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }

            // 移除临时元素
            document.body.removeChild(tempInput);
        }
        
        // 复制二维码功能
        function copyQRCode() {
            if (!currentQRCanvas) {
                showToast('二维码未生成', 'error');
                return;
            }

            try {
                // 生成带支持应用信息的二维码（异步）
                generateQRCodeWithApps(currentQRCanvas, function(enhancedCanvas) {
                    if (!enhancedCanvas) {
                        showToast('生成二维码失败', 'error');
                        return;
                    }

                    // 将canvas转换为blob
                    enhancedCanvas.toBlob(function(blob) {
                        if (navigator.clipboard && navigator.clipboard.write) {
                            // 使用现代Clipboard API
                            var item = new ClipboardItem({'image/png': blob});
                            navigator.clipboard.write([item]).then(function() {
                                showToast('二维码已复制到剪贴板', 'success');
                            }).catch(function(err) {
                                showToast('复制失败：' + err.message, 'error');
                            });
                        } else {
                            showToast('您的浏览器不支持图片复制功能', 'error');
                        }
                    });
                });
            } catch (err) {
                showToast('复制失败：' + err.message, 'error');
            }
        }
        
        // 下载二维码功能
        function downloadQRCode() {
            if (!currentQRCanvas) {
                showToast('二维码未生成', 'error');
                return;
            }

            try {
                // 生成带支持应用信息的二维码（异步）
                generateQRCodeWithApps(currentQRCanvas, function(enhancedCanvas) {
                    if (!enhancedCanvas) {
                        showToast('生成二维码失败', 'error');
                        return;
                    }

                    // 将canvas转换为dataURL
                    var dataURL = enhancedCanvas.toDataURL('image/png');

                    // 创建下载链接
                    var link = document.createElement('a');
                    link.download = shareData[currentPersonIndex].personName + '_' + shareData[currentPersonIndex].fileName + '.png';
                    link.href = dataURL;

                    // 触发下载
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    showToast('二维码下载成功', 'success');
                });
            } catch (err) {
                showToast('下载失败：' + err.message, 'error');
            }
        }
        
        // 页面自适应高度
        $(document).ready(function() {
            try {
                parent.iFrameHeight();
            } catch (e) {
                // 忽略错误
            }
        });
    </script>
</body>
</html>
