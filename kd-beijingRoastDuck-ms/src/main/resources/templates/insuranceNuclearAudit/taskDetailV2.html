<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>保司核损审核详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <meta content="" name="description" />
    <meta content="" name="author" />
    
    <!-- 引入公共CSS -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/metronic/global/plugins/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/metronic/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/metronic/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/metronic/global/css/components.min.css" rel="stylesheet" id="style_components" type="text/css" />
    <link href="${ctx}/metronic/layouts/layout/css/layout.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/metronic/layouts/layout/css/themes/darkblue.css" rel="stylesheet" type="text/css" id="style_color" />
    <link href="${ctx}/css/custom.css" rel="stylesheet" type="text/css" />

    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js" type="text/javascript"></script>

    <style>
        .center-box {
            background-color: white;
            border: 1px solid #ccc;
            justify-content: center;
            align-items: center;

            /* 添加粘性定位 */
            position: sticky;
            top: 0; /* 固定在顶部 */
            z-index: 1000;

            /* 过渡动画 */
            transition: all 0.3s ease;
        }

        /* 滚动时添加的样式（可选） */
        .fixed-style {
            width: 100%;
            position: fixed;
            border-radius: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        #floating-container {
            position: fixed;
            right: 20px !important;
            /*left: auto !important;*/
            top: 50%;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .draggable-box {
            width: 60px;
            height: 60px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 6px !important;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            user-select: none;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            font-size: 12px;
            color: #666;
            text-align: center;
            line-height: 1.3;
            padding: 13px;
            box-sizing: border-box;
            margin-bottom: 8px;
            cursor: move;
            transition: all 0.3s ease; /* 添加过渡效果 */

        }

        /* 鼠标悬停效果 - 边框变亮 */
        .draggable-box:hover {
            /*box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); !* 内阴影 *!*/
            transform: scale(1.1);
        }

        /* 选中状态 - 边框变亮且更明显 */
        .box-active {
            background-color: #ffffff;
            /*box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); !* 内阴影 *!*/
            transform: translateY(1px); /* 模拟按下 */
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .row {
            margin: 0px 0px;
        }

        textarea[class='layui-layer-input'] {
            outline: 0 !important;
            width: 700px !important;
            height: 350px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        button[name='actionBtn'] {
            color: white;
        }

        select {
            padding: 0px 12px !important;
        }

        div[name='dataCollectAreaRow'] input {
            padding: 0px 5px;
        }

        #thumbnail p {
            margin: 10px 0px 5px -15px;
            font-size: 10px;
            text-align: left;
        }

        .viewer-canvas h2 {
            z-index: 9999999;
            position: relative;
            left: 20px;
            font-weight: bold;
        }

        .nav-link i {
            margin-right: 8px;
            transition: transform 0.3s ease;
        }

        /* 展开状态的箭头旋转 */
        .nav-link[aria-expanded="true"] i.glyphicon-chevron-right {
            transform: rotate(90deg);
        }

        /* 数量标签样式 */
        .nav-link span.badge {
            margin-left: 5px;
            background-color: #777;
        }

        @media (min-width: 992px) {
            .page-content-wrapper .page-content {
                margin-left: 0px!important;
            }
        }

        /* 修复页面滚动问题 */
        body {
            /*padding-top: 70px !important; !* 为固定头部留出空间 *!*/
            overflow-y: auto !important; /* 确保垂直滚动 */
            height: auto !important;
        }
        
        .page-container {
            min-height: 100vh;
            overflow-y: auto;
        }
        
        .page-content-wrapper {
            overflow-y: auto;
            min-height: calc(100vh - 70px);
        }
        
        .page-content {
            padding-bottom: 50px; /* 底部留出空间 */
            overflow-y: auto;
        }
        
        /* 费用表格样式 */
        .cost-table {
            margin-top: 10px;
        }
        .cost-table th {
            background-color: #ffffff;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }
        .cost-table td {
            text-align: right;
            vertical-align: middle;
        }
        .cost-table .cost-type-col {
            text-align: center;
        }
        .cost-table .amount {
            color: #000000;
            font-weight: bold;
        }

        /* 确保portlet和其他组件不影响滚动 */
        .portlet {
            overflow: visible !important;
            height: auto !important;
            position: static !important;
            margin-bottom: 0px !important;
            display: block !important;
            visibility: visible !important;
        }
        
        .portlet .portlet-title {
            overflow: visible !important;
            height: auto !important;
            position: static !important;
            display: block !important;
            visibility: visible !important;
        }

        .portlet.light2 {
            padding: 0px 10px 0px;
            background-color: #fff;
        }
        
        /* 按钮样式 */
        .btn {
            min-width: 80px;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 1000 !important;
        }
        
        /* 确保操作按钮区域完全可见 */
        .row:last-child {
            display: block !important;
            visibility: visible !important;
            /*margin-bottom: 50px !important;*/
        }
        
        /* 面包屑导航样式 */
        .page-breadcrumb {
            padding: 0;
            margin: 0 0 25px 0;
            background-color: transparent;
            border-radius: 0;
        }
        .page-breadcrumb > li + li:before {
            content: ">";
            padding: 0 5px;
            color: #777;
        }
        
        /* 卡片样式优化 */
        .portlet {
            margin-bottom: 25px;
        }
        .portlet .portlet-title {
            padding: 15px 20px;
            min-height: 48px;
            border-bottom: 1px solid #eee;
            position: relative;
        }
        .portlet .portlet-title .caption {
            float: left;
            display: inline-block;
            font-size: 16px;
            line-height: 16px;
        }
        .portlet .portlet-body {
            padding: 0px;
        }
        
        /* 确保按钮区域可见 */
        .portlet .portlet-body {
            min-height: 50px;
        }
        
        .btn {
            margin-right: 10px;
            min-width: 80px;
        }
        
        .btn:last-child {
            margin-right: 0;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .row {
            margin: 0px 0px;
        }

        button[name='actionBtn'] {
            color: white;
        }

        select {
            padding: 0px 12px !important;
        }

        div[name='dataCollectAreaRow'] input {
            padding: 0px 5px;
        }

        #thumbnail p {
            margin: 10px 0px 5px -15px;
            font-size: 10px;
            text-align: left;
        }

        .viewer-canvas h2 {
            z-index: 9999999;
            position: relative;
            left: 20px;
            font-weight: bold;
        }

        /* 侧边栏样式 */

        .nav-link i {
            margin-right: 8px;
            transition: transform 0.3s ease;
        }

        /* 展开状态的箭头旋转 */
        .nav-link[aria-expanded="true"] i.glyphicon-chevron-right {
            transform: rotate(90deg);
        }

        /* 数量标签样式 */
        .nav-link span.badge {
            margin-left: 5px;
            background-color: #777;
        }

        .col-md-12:not(.col-md-12-no-padding) {
            padding: 0;
        }

    </style>
</head>
<body class="page-content-white">

    <!-- 页面容器 -->
    <div class="page-container">
        <!-- 页面内容包装器 -->
        <div class="page-content-wrapper">
            <!-- 页面内容 -->
            <div class="page-content" style="margin-left: 0;">
                <!-- 面包屑导航 -->
                <div class="page-bar">
                    <ul class="page-breadcrumb">
                        <li>
                            <a href="${ctx}/insuranceNuclearAudit/insuranceNuclearAuditTask">保司核损审核</a>
                        </li>
                        <li>任务详情</li>
                    </ul>
                </div>
                
                <!-- 案件信息和快捷操作栏 -->
                <div class="row center-box" id="stickyBox"  style="margin-top: 5px;margin-bottom: 5px; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">
                    <div class="col-md-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group" style="margin-bottom: 0;">
                                    <label class="control-label" style="font-weight: bold;">报案号:</label>
                                    <span style="margin-left: 10px; font-size: 14px;">${claimCase.claimCaseNo!''}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group" style="margin-bottom: 0;">
                                    <label class="control-label" style="font-weight: bold;">立案号:</label>
                                    <span style="margin-left: 10px; font-size: 14px;">${claimCase.insuranceCaseNo!''}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="pull-right">
                            <button type="button" class="btn btn-sm red" onclick="startNuclearAuditFromCost()">
                                核损
                            </button>
                            <button type="button" class="btn btn-sm yellow-lemon" onclick="viewNuclearAuditData()">
                                查看核损信息
                            </button>
                            <#if comeFrom == 3>
                                <#if claimCaseObject.status == "BAX25">
                                    <button type="button" class="btn btn-default" onclick="submitBack('BAX27')"style="color: #f5f5f5;background-color: #afa7a7;border-color: #fff;">
                                        退回
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="submitAudit('BAX26')">
                                        通过
                                    </button>
                                </#if>
                                <#if claimCaseObject.status == "BAX35">
                                    <button type="button" class="btn btn-default" onclick="submitBack('BAX37')"style="color: #f5f5f5;background-color: #afa7a7;border-color: #fff;">
                                        退回
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="submitAudit('BAX36')">
                                        通过
                                    </button>
                                </#if>
                            </#if>
                        </div>
                    </div>

                    <!-- 核损费用合计 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="portlet light2">
                                <div class="portlet-title">
                                    <div class="caption">
                                        <i class="icon-bar-chart font-dark"></i>
                                        <span class="caption-subject font-dark sbold uppercase" style="font-size: 16px;">损失费用合计</span>
                                    </div>
                                </div>
                                <div class="portlet-body">
                                    <div class="table-responsive">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 影像资料 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="portlet light bordered">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="icon-picture font-dark"></i>
                                    <span class="caption-subject font-dark sbold uppercase">影像资料</span>
                                </div>
                            </div>
                            <div class="portlet-body">
                                <#include "/attach/attachComponent.html">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 拖拽浮窗 -->
                <div id="floating-container">
                    <div id="box1" class="draggable-box box-active" onclick="showBasicInformation('${claimCase.id!''}', '${claimCaseObject.id!''}')">
                        基本信息
                    </div>
                    <div id="box2" class="draggable-box" onclick="showDetailLog('${claimCaseObject.id!''}')">
                        查看日志
                    </div>
                </div>
                <input type="hidden" name="claimCaseObjectId" id="claimCaseObjectId" value="${claimCaseObject.id!''}">
            </div>
        </div>
    </div>

    <script type="text/javascript">
        jQuery(document).ready(function() {
            App.init();
        });

        let isCostDataLoading = false;
        let feeInfoJsonStr ;
        let isInitFeeInfo = true;
        let costRefreshTimer = null;

        $(function (){
            stopCostAutoRefresh();
            findFeeInfo()
            // 每隔3秒查询损失费用合计信息
            costRefreshTimer = setInterval(function () {
                findFeeInfo()
            },3000)
            console.log("costRefreshTimer=" + costRefreshTimer)
        })

        // 停止自动刷新
        function stopCostAutoRefresh() {
            if (costRefreshTimer) {
                console.log("停止循环......")
                clearInterval(costRefreshTimer);
                costRefreshTimer = null;
            }
        }

        function showCostError(msg) {
            layer.msg(msg || '网络请求失败，请稍后重试！', {
                icon: 2,
                offset: ['450px', ''],
                time: 2000
            });
        }

        //损失费用合计查询方法 Ajax获取费用数据
        function findFeeInfo() {
            // 可以添加加载指示器
            $.ajax({
                url: '${ctx}/insuranceNuclearAudit/findCostDataByLossNo?claimCaseObjectId=${claimCaseObject.id}', // 替换为实际接口地址
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function(data) {
                    let resData = JSON.stringify(data)
                    if(feeInfoJsonStr != resData){
                        feeInfoJsonStr = resData
                        updateCostTable(JSON.parse(resData));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('获取费用数据失败:', error);
                    stopCostAutoRefresh();
                    showCostError('获取费用数据失败，请稍后重试');
                },
                complete: function() {
                    isCostDataLoading = false;
                }
            });
        }

        // 更新费用表格
        function updateCostTable(costList) {
            const $container = $('.table-responsive');
            let flag = 0;
            $container.html('');
            let tableDiv = $('<table class="table table-bordered cost-table"></table>')
            tableDiv.append('<thead><tr><th width="8%">类型</th><th width="10%">合计配件金额</th><th width="10%">配件管理费金额</th><th width="10%">合计残值金额</th><th width="10%">合计自付金额</th><th width="10%">合计工时金额</th><th width="10%">合计辅料金额</th><th width="10%">合计外修金额</th><th width="10%">合计施救费用金额</th><th width="15%">总合计</th></tr></thead>');
            let tbodyDiv = $('<tbody></tbody>')
            if (costList && costList.length > 0) {
                for(let cost of costList){
                    tbodyDiv.append('<tr><td class="cost-type-col">'+cost.assessmentType
                    +'</td><td class="amount">'+(parseFloat(cost.totalPartsAmount) || 0).toFixed(2)
                    +'</td><td class="amount">'+(parseFloat(cost.partsManagementFee) || 0).toFixed(2)
                    +'</td><td class="amount">'+(parseFloat(cost.totalResidualValue) || 0).toFixed(2)
                    +'</td><td class="amount">'+(parseFloat(cost.totalOutOfPocket) || 0).toFixed(2)
                    +'</td><td class="amount">'+(parseFloat(cost.totalLaborAmount) || 0).toFixed(2)
                    +'</td><td class="amount">'+(parseFloat(cost.totalSuppliesAmount) || 0).toFixed(2)
                    +'</td><td class="amount">'+(parseFloat(cost.totalExternalRepair) || 0).toFixed(2)
                    +'</td><td class="amount">'+(parseFloat(cost.totalRescueFee) || 0).toFixed(2)
                    +'</td><td class="amount" style="font-weight: bold; color: #e74c3c;"> '+(parseFloat(cost.totalExcludingRescue) || 0).toFixed(2)+'</td></tr>')

                    if(cost.assessmentType === '复审') {
                        flag = flag + 1;
                    }
                }

            }
            if (flag == 0) {
                tbodyDiv.append('<tr><td class="cost-type-col">复审</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount" style="font-weight: bold; color: #e74c3c;">0.00</td></tr>')
            }
            tableDiv.append(tbodyDiv)
            $container.append(tableDiv);

            if(isInitFeeInfo){
                isInitFeeInfo = false
            }else {
                console.log('损失费用合计已变更');
                /*layer.msg('损失费用合计已变更', {
                    icon: 1,
                    offset: ['200px', ''],
                    time: 2000
                });*/
            }
        }

		// 查看基本信息
        function showBasicInformation(claimCaseId, claimCaseObjectId){
            layer.open({
                title: "查看基本信息",
                type: 2,
                area: ['1200px', '700px'],
                offset: ['300px', ''],
                fixed: false,
                closeBtn: 1,
                maxmin: true,
                content: "${ctx}/insuranceNuclearAudit/showBasicInformation?claimCaseId=" + claimCaseId + "&claimCaseObjectId=" + claimCaseObjectId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

		// 查看日志（保司侧）
        function showDetailLog(claimCaseObjectId){
            layer.open({
                title: '查看日志',
                type: 2,
                area: ['1200px', '400px'],
                offset: ['300px', ''],
                fixed: false,
                closeBtn: 1,
                maxmin: true, // 启用最大化按钮
                content: '${ctx}/insuranceNuclearAudit/showDetailLog4Ins?claimCaseObjectId=' + claimCaseObjectId,
                success: function(layero, index) {
                    console.log('弹框当前处于状态');
                    layer.iframeAuto(index);
                },
                full: function(layero) {
                    console.log('弹框当前处于最大化状态');
                    // 最大化时添加全屏类
                    var iframe = $(layero).find('iframe');
                    try {
                        var iframeDoc = iframe[0].contentDocument || iframe[0].contentWindow.document;
                        $(iframeDoc).find('.table-responsive').css({
                            'max-height': 'none',
                            'overflow-y': 'visible'
                        });
                        $(iframeDoc).find('tbody tr').show();

                    } catch(e) {
                        console.log('样式注入错误:', e);
                    }
                },
                restore: function(layero) {
                    console.log('弹框当前处于恢复状态');
                    // 恢复时移除全屏类
                    var iframe = $(layero).find('iframe');
                    try {
                        var iframeDoc = iframe[0].contentDocument || iframe[0].contentWindow.document;
                        $(iframeDoc).find('.table-responsive').css({
                            'max-height': 'calc(6 * 40px + 41px)',
                            'overflow-y': 'auto'
                        });
                    } catch(e) {
                        console.log('样式注入错误:', e);
                    }
                }
            });
        }

        // 开始核损审核
        function startNuclearAuditFromCost() {
            let claimCaseObject = assembleData2(status);
            $.ajax({
                url: '${ctx}/insuranceNuclearAudit/startNuclearAuditFromCost',
                type: 'POST',
                data: JSON.stringify(claimCaseObject),
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    console.log(result);
                    if (result.ret == "0")  {
                        window.open(result.url);
                    }else {
                        layer.msg(result.msg || '网络请求失败，请稍后重试！', {
                            icon: 2,
                            offset: ['450px', ''],
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    console.log(result);
                    layer.msg(result.msg || '网络请求失败，请稍后重试！', {
                        icon: 2,
                        offset: ['450px', ''],
                        time: 2000
                    });
                }
            });
        }

        // 查看核损信息
        function viewNuclearAuditData() {
            let claimCaseObject = assembleData2(status);
            $.ajax({
                url: '${ctx}/insuranceNuclearAudit/viewNuclearAuditData',
                type: 'POST',
                data: JSON.stringify(claimCaseObject),
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    console.log(result);
                    if (result.ret == "0")  {
                        window.open(result.url);
                    }else {
                        console.log(result.msg);
                        layer.msg(result.msg || '网络请求失败，请稍后重试！', {
                            icon: 2,
                            offset: ['450px', ''],
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    console.log(result);
                    layer.msg(result.msg || '网络请求失败，请稍后重试！', {
                        icon: 2,
                        offset: ['450px', ''],
                        time: 2000
                    });
                }
            });
        }

        // 组装数据
        function assembleData2(status) {

            let isError = false;
            let errorMsg = "";

            $("button[name='actionBtn']").attr('disabled','1');

            let claimCaseObjectId = $("#claimCaseObjectId").val().trim();
            if (!claimCaseObjectId) {
                isError = true;
                errorMsg += "赔付对象不能为空！</br>";
            }


            let object = {};
            object["id"] = "${claimCaseObject.id}";
            object["claimCaseId"] = "${claimCaseObject.claimCaseId}";
            object["claimCaseNo"] = "${claimCaseObject.claimCaseNo}";
            object["type"] = "${claimCaseObject.type}";
            object["category"] = "${claimCaseObject.category}";
            object["comeFrom"] = "${comeFrom}";
            object["checkCondition"] = "${checkCondition}";
            object["litigation"] = $('input[name="litigation"]:checked').val();
            object["subrogation"] = $('input[name="subrogation"]:checked').val();
            object["paymentMethod"] = $('#paymentMethod').val();
            object["status"] = status;

            if(isError){
                layer.msg(errorMsg, {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }

            console.log(JSON.stringify(object));

            return object;
        }

        // 返回核损任务管理页面
        function submitBack(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                layer.prompt({
                    title: '意见备注：',
                    formType: 2, // 0-文本输入框（默认），1-密码框，2-多行文本框
                    value: '', // 默认内容
                    maxlength: 100, // 最大输入长度
                    offset: ['280px', ''],
                    area: ['auto', 'auto'], // 自定义宽高
                    btn: ['确定', '取消'],
                    yes: function (index, elem) {
                        var value = elem.find('.layui-layer-input').val().trim();
                        if (value === '') {
                            layer.msg('请输入意见备注', {
                                icon: 2,
                                offset: ['450px', ''],
                                time: 2000
                            });
                            return false; // 阻止关闭
                        }
                        claimCaseObject["reason"] = value;
                        // 关闭弹框
                        layer.close(index);

                        layer.confirm('确定需要回退吗？', {
                            icon: 3,
                            title: '提示',
                            btn: ['确定', '取消'],
                        }, function (index) {
                            $.ajax({
                                url: "${ctx}/claimCaseObjectController/auditRejectCar",
                                type: 'POST',
                                data: JSON.stringify(claimCaseObject),
                                async: true,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    if (result.ret == "0") {
                                        layer.msg(result.msg || '回退成功', {
                                            icon: 1,
                                            offset: ['450px', ''],
                                            time: 2000,
                                            shade: [0.0001, '#000']
                                        }, function () {
                                            window.close();
                                        });
                                    } else {
                                        layer.msg(result.msg || '回退失败', {
                                            icon: 2,
                                            offset: ['450px', ''],
                                            time: 2000,
                                            shade: [0.0001, '#000']
                                        });
                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                    console.log(result);
                                    layer.msg('网络请求失败，请稍后重试', {
                                        icon: 2,
                                        offset: ['450px', ''],
                                        time: 2000
                                    });
                                }
                            });

                            layer.close(index);

                        });

                    }
                });
            }
        }

        // 提交审核
        function submitAudit(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                $.ajax({
                    url: "${ctx}/claimCaseObjectController/auditPassCar",
                    type: 'POST',
                    data: JSON.stringify(claimCaseObject),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg || '保司核损操作成功', {
                                icon: 1,
                                offset: ['450px', ''],
                                time: 2000,
                                shade: [0.0001, '#000']
                            }, function () {
                                // 提交成功后返回任务列表页面
                                window.location.href = "${ctx}/insuranceNuclearAudit/submitConfirmPage?reportNo=${claimCase.claimCaseNo}&insCode=${claimCase.insCode}&checkCondition=${checkCondition}";
                            });
                        } else {
                            layer.msg(result.msg || '保司核损操作失败', {
                                icon: 2,
                                offset: ['450px', ''],
                                time: 2000,
                                shade: [0.0001, '#000']
                            });
                        }

                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        console.log(result.msg);
                        layer.msg('网络请求失败，请稍后重试', {
                            icon: 2,
                            offset: ['450px', ''],
                            time: 2000
                        });
                    }
                });

            }

        }

        // 组装数据
        function assembleData(status) {
            let isError = false;
            let errorMsg = "";

            let object = {
                id: "${claimCaseObject.id}",
                claimCaseId: "${claimCaseObject.claimCaseId}",
                claimCaseNo: "${claimCaseObject.claimCaseNo}",
                type: "${claimCaseObject.type}",
                category: "${claimCaseObject.category}",
                status: status,
                assessmentType: "${assessmentType}"
            };


            if(isError){
                layer.msg(errorMsg, {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }

            console.log(JSON.stringify(object));

            return object;
        }

        const container = document.getElementById('floating-container');
        const boxes = {
            box1: document.getElementById('box1'),
            box2: document.getElementById('box2')
        };

        const controlButtons = document.querySelectorAll('.control-btn');
        let activeBox = boxes.box1;
        let isDragging = false;
        let offsetX, offsetY;

        // 切换活动浮框
        function setActiveBox(box) {
            activeBox.classList.remove('box-active');
            activeBox = box;
            activeBox.classList.add('box-active');
        }

        // 控制面板按钮事件
        controlButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                setActiveBox(boxes[this.dataset.target]);
            });
        });

        // 容器拖拽事件
        container.addEventListener('mousedown', function(e) {
            // 只有当点击的是容器或浮框时才触发拖拽
            if (e.target === container || e.target.classList.contains('draggable-box')) {
                isDragging = true;
                offsetX = e.clientX - container.getBoundingClientRect().left;
                offsetY = e.clientY - container.getBoundingClientRect().top;

                container.style.transition = 'none';
                Array.from(container.children).forEach(box => {
                    box.classList.add('dragging');
                });

                e.preventDefault();
            }
        });

        // 鼠标移动事件
        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;

            let newLeft = e.clientX - offsetX;
            let newTop = e.clientY - offsetY;

            // 限制位置（防止移出屏幕）
            const maxLeft = window.innerWidth - container.offsetWidth;
            const maxTop = window.innerHeight - container.offsetHeight;

            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));

            container.style.left = newLeft + 'px';
            container.style.top = newTop + 'px';
            container.style.right = 'auto';
        });

        // 鼠标释放事件
        document.addEventListener('mouseup', function(e) {
            if (!isDragging) return;
            isDragging = false;

            container.style.transition = 'all 0.3s ease';
            Array.from(container.children).forEach(box => {
                box.classList.remove('dragging');
            });

            // 判断吸附到左侧还是右侧
            const containerRect = container.getBoundingClientRect();
            const screenCenter = window.innerWidth / 2;
            const containerCenter = containerRect.left + containerRect.width / 2;

            if (containerCenter < screenCenter) {
                container.style.left = '20px';
                container.style.right = 'auto';
            } else {
                container.style.left = 'auto';
                container.style.right = '20px';
            }
        });

        // 窗口大小变化时重新定位
        window.addEventListener('resize', function() {
            const containerRect = container.getBoundingClientRect();

            if (parseInt(container.style.left) > 0 || container.style.left === '') {
                container.style.left = '20px';
                container.style.right = 'auto';
            } else {
                container.style.left = 'auto';
                container.style.right = '20px';
            }

            const maxTop = window.innerHeight - container.offsetHeight;
            const currentTop = parseInt(container.style.top || containerRect.top);
            container.style.top = Math.max(0, Math.min(currentTop, maxTop)) + 'px';
        });

        // 点击浮框切换活动状态
        Object.values(boxes).forEach(box => {
            box.addEventListener('click', function() {
                setActiveBox(this);
            });
        });

        window.addEventListener('scroll', function() {
            const box = document.getElementById('stickyBox');
            if (window.scrollY > 150) { // 滚动超过200px时添加额外样式
                box.classList.add('fixed-style');
            } else {
                box.classList.remove('fixed-style');
            }
        });


    </script>
</body>
</html> 