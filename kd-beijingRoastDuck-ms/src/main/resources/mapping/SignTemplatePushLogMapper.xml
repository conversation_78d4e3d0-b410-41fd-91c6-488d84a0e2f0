<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.SignTemplatePushLogDao">

	<insert id="insertSelective" parameterType="kd.entity.SignTemplatePushLog">
		<include refid="mapping.SignTemplatePushLogMapper.insertSelective" />
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.SignTemplatePushLog">
		<include refid="mapping.SignTemplatePushLogMapper.updateByPrimaryKeySelective" />
	</update>

	<update id="updateByClaimCaseObjectId" parameterType="kd.entity.SignTemplatePushLog">
		<include refid="mapping.SignTemplatePushLogMapper.updateByClaimCaseObjectId" />
	</update>


</mapper>