###  #测试
debug: true
eureka:
  instance:
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
  client:
    serviceUrl:
      defaultZone: http://pater:pater_123@*************:8761/eureka/
#      defaultZone: *************************************/eureka/
server:
  port: 8202
spring:
  servlet:
    multipart:
      # 上传文件总的最大值
      max-request-size: 100GB
      # 单个文件的最大值
      max-file-size: 100GB
  main:
    allow-bean-definition-overriding: true #允许bean重复
  application:
    name: kd-beijingRoastDuck-ms
  cloud:
    config:
      #   用以切换 开发， 测试， 生产环境 以便连接不同的数据库
#      profile: eleTest # 对应配置中心文件的${profile}部
      #      profile: prd # 对应配置中心文件的${profile}部
      profile: dev # 对应配置中心文件的${profile}部
      label: kd-cuisine # 对应配置中心文件的${label}部
      discovery:
        enabled: true # 通过服务发现的方式去找配置中心
        service-id: kd-config-server # 配置中心的名字，直接配置名称可以在配置中心集群的时候实现负载均衡
      username: pater
      password: pater_123
  devtools:
    restart:
      enabled: false  # 关闭热部署

#eureka:
#  instance:
#    prefer-ip-address: true
#    instance-id: ${spring.cloud.client.ip-address}:${server.port}
#  client:
#    serviceUrl:
#      defaultZone: http://pater:pater_123@*************:8761/eureka/
#server:
#  port: 8202
#spring:
#  servlet:
#    multipart:
#      # 上传文件总的最大值
#      max-request-size: 100GB
#      # 单个文件的最大值
#      max-file-size: 100GB
#  main:
#    allow-bean-definition-overriding: true #允许bean重复
#  application:
#    name: kd-beijingRoastDuck-ms
#  cloud:
#    config:
#      #   用以切换 开发， 测试， 生产环境 以便连接不同的数据库
#      profile: dev # 对应配置中心文件的${profile}部
#      #      profile: prd # 对应配置中心文件的${profile}部
#      #      profile: dev # 对应配置中心文件的${profile}部
#      label: kd-cuisine # 对应配置中心文件的${label}部
#      discovery:
#        enabled: true # 通过服务发现的方式去找配置中心
#        service-id: kd-config-server # 配置中心的名字，直接配置名称可以在配置中心集群的时候实现负载均衡
#      username: pater
#      password: pater_123



####
####生产配置
#eureka:
#  instance:
#    prefer-ip-address: true
#    instance-id: ${spring.cloud.client.ip-address}:${server.port}
#  client:
#    serviceUrl:
#      defaultZone: **********************************************/eureka/ #生产
#server:
#  port: 8109
#spring:
#  servlet:
#    multipart:
#      # 上传文件总的最大值
#      max-request-size: 100GB
#      # 单个文件的最大值
#      max-file-size: 100GB
#  main:
#    allow-bean-definition-overriding: true #允许bean重复
#  application:
#    name: kd-beijingRoastDuck-ms
#  cloud:
#    config:
#      #   用以切换 开发， 测试， 生产环境 以便连接不同的数据库
#      profile: prd # 对应配置中心文件的${profile}部
#      label: kd-cuisine # 对应配置中心文件的${label}部
#      discovery:
#        enabled: true # 通过服务发现的方式去找配置中心
#        service-id: kd-config-server # 配置中心的名字，直接配置名称可以在配置中心集群的时候实现负载均衡
#      username: root

#      password: F5XjWamhKzll8eB6