package kd.scallion.api;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import kd.common.context.RedisCommonKeyEnum;
import kd.common.enums.RetEnum;
import kd.common.tool.EmailToolV3;
import kd.common.tool.JsonBizTool;
import kd.common.tool.JsonTool;
import kd.common.tool.RedisTool3;
import kd.entity.Area;
import kd.entity.ClaimCase;
import kd.entity.ErrorLog;
import kd.entity.PolicyPerson;
import kd.main.common.ClaimCaseLabelEnum;
import kd.main.common.ExRetEnum;
import kd.main.common.QueueName;
import kd.main.common.Tool;
import kd.main.elm.ClaimInformation;
import kd.main.elm.ReportStatusResVo;
import kd.main.support.ele.ClaimDataSup;
import kd.main.util.HttpToolV3;
import kd.scallion.service.AreaService;
import kd.scallion.service.ClaimCaseService;
import kd.scallion.service.ErrorLogService;
import kd.scallion.service.PolicyPersonService;
import kd.scallion.support.Email4AcceptCaseNoticesSup;
import kd.scallion.vo.ClaimCaseReq;
import kd.scallion.vo.ClaimParamReq;
import kd.scallion.vo.HmClaimCaseReq;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.security.SecureRandom;
import java.util.*;

@Controller
@RequestMapping("/api/claimCaseApi")
public class ClaimCaseApi {

    private final static Logger logger = LoggerFactory.getLogger(ClaimCaseApi.class);

    @Autowired
    private PolicyPersonService policyPersonService;

    @Autowired
    private ClaimCaseService claimCaseService;

    @Autowired
    private ErrorLogService errorLogService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @PostMapping(value = "pushTask")
    @ResponseBody
    public String pushTask(@RequestBody String json) {
        logger.info(">>>>>>>>>>>>>mongodb转发报案推送{}队列，json：{}", QueueName.KD_APP_REPORT_CASE, json);
        amqpTemplate.convertAndSend(QueueName.KD_APP_REPORT_CASE, json);
        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    @RequestMapping(value = "appInsertClaimCase")
    @ResponseBody
    public String appInsertClaimCase(@RequestBody String json) {
        logger.info(">>>>>>>>>方法/api/claimCaseApi/appInsertClaimCase开始处理app报案任务，json：{}", json);
        String claimCaseNo = "";
        try {
            ClaimCaseReq claimCaseReq = JSONObject.parseObject(json, ClaimCaseReq.class);
            claimCaseNo = claimCaseReq.getClaimCaseNo();
            // 重复报案校验
            if (RedisTool3.setnx(claimCaseReq.getInsCode() + "-INNER-" + claimCaseReq.getPartnerClaimNo(), claimCaseReq.getClaimCaseNo()) == 1L) {
                RedisTool3.expire(claimCaseReq.getInsCode() + "-INNER-" + claimCaseReq.getPartnerClaimNo(), 60 * 60 * 24);
            } else {
                logger.error(">>>>>>>>>>该案件已存在,重复报案,partnerClaimNo:{}, insCode:{}", claimCaseReq.getPartnerClaimNo(), claimCaseReq.getInsCode());
                throw new Exception("饿了么内部理赔单号{" + claimCaseReq.getPartnerClaimNo() + "}已存在，重复报案");
            }
            ClaimCase claimCase = claimCaseService.selectByPartnerClaimNoAndInsCode(claimCaseReq.getPartnerClaimNo(), claimCaseReq.getInsCode());
            if (claimCase != null) {
                logger.error(">>>>>>>>>>该案件已存在, 重复报案, partnerClaimNo:{}, insCode:{}", claimCaseReq.getPartnerClaimNo(), claimCaseReq.getInsCode());
                throw new Exception("饿了么内部理赔单号{" + claimCaseReq.getPartnerClaimNo() + "}已存在，重复报案");
            }

            //查保单号是否存在 存在出险人信息为保全信息
            PolicyPerson policyPerson = policyPersonService.findByCustomerPolicyNoBS(claimCaseReq.getPolicyNo(), claimCaseReq.getInsCode());
            if (policyPerson == null) {
                logger.error(">>>>>>>>>方法/api/claimCaseApi/appInsertClaimCase，保全{}信息不存在", claimCaseReq.getPolicyNo());
                ErrorLog errorLog = new ErrorLog();
                errorLog.setId(Tool.uuid());
                errorLog.setBusinessName("app报案入库");
                errorLog.setMethodName("/api/claimCaseApi/appInsertClaimCase");
                errorLog.setComeFrom(1);
                errorLog.setReqType(0);
                errorLog.setStatus(0);
                errorLog.setReqData(json);
                errorLog.setDescription("报案时保全信息不存在，保单号{"+ claimCaseReq.getPolicyNo()+"}");
                errorLog.setCreator("-1");
                errorLog.setCreateTime(new Date());
                errorLogService.insertSelective(errorLog);
            }
            claimCaseReq.setClaimCaseId(Tool.uuid());
            claimCaseService.insertReport(claimCaseReq);
            amqpTemplate.convertAndSend(QueueName.KD_SET_CLAIM_CAST_LABEL, claimCaseReq.getClaimCaseId());

           /* try {
                Thread.sleep(1000L);
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 发送短信
            Map<String, Object> smsMap = new TreeMap<>();
            smsMap.put("claimCaseId", claimCaseReq.getClaimCaseId());
            smsMap.put("type","01");
            amqpTemplate.convertAndSend(QueueName.KD_ROASTDUCK_RABBIT_SEND_MESSAGE, JsonTool.genByFastJson(smsMap));*/

            // 回传 饿了么 电子资料审核中(2)
            claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseReq.getClaimCaseId());
            String reqData = JsonTool.genByFastJson(claimCase);
            JSONObject reqJson = JSONObject.parseObject(reqData);
            reqJson.put("auditStatus", 2);
//            amqpTemplate.convertAndSend(QueueName.KD_SCALLION_STATUS_BACK, JsonTool.genByFastJson(reqJson));
            // 延迟10秒回退饿了么
            // 盒马和京东家政不回传饿了么
           if(!"HMRB".equals(claimCase.getInsCode()) && !"JZTB".equals(claimCase.getInsCode())){
               amqpTemplate.convertAndSend(QueueName.KD_SCALLION_STATUS_BACK_TTL_EXCHANGE, QueueName.KD_SCALLION_STATUS_BACK_TTL, JsonTool.genByFastJson(reqJson), message -> {
                   message.getMessageProperties().setExpiration(String.valueOf(10L * 1000L));
                   return message;
               });
           }

           /* // 人保案件，延迟10秒推送人保抄单接口（抄单执行完自动报案）
            if ("RB".equalsIgnoreCase(claimCase.getInsCode())) {
                Map<String, String> dataMap = new HashMap<>();
                dataMap.put("claimCaseId", claimCase.getId());
                ClaimDataSup claimDataSup = new ClaimDataSup();
                claimDataSup.setMode(1);
                claimDataSup.setReqJsonStr(JsonTool.genByFastJson(dataMap));
                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_DATA_PUSH_PICC_DELAY_TTL_EXCHANGE, QueueName.KD_CLAIM_DATA_PUSH_PICC_DELAY_TTL, claimDataSup, message -> {
                    message.getMessageProperties().setExpiration(String.valueOf(10L * 1000L));
                    return message;
                });
            }*/

            //TODO 临时增加，下发邮件
            amqpTemplate.convertAndSend(QueueName.KD_CUISINE_POLICY_EMAIL, new Email4AcceptCaseNoticesSup(claimCaseReq.getClaimCaseNo()));

            logger.info(">>>>>>>>>方法/api/claimCaseApi/appInsertClaimCase，入库成功，json：{}", json);
        } catch (Exception e) {
            logger.error(">>>>>>>>>方法/api/claimCaseApi/appInsertClaimCase，系统异常，json：" + json);
            e.printStackTrace();

            // 发送钉钉告警消息
            sendDingTalk(claimCaseNo + "-饿了么报案入库异常");
            // 案件入库异常增加邮箱发送
            String email = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "CLAIM_CASE_INSERT", "ERROR_EMAIL");
            if (StringUtils.isBlank(email)) {
                email = "<EMAIL>";
            }
            EmailToolV3.sendHtmlEmail4Eai_new("饿了么案件入库异常 kd-ele", json, email, "mk", null);

            ErrorLog errorLog = new ErrorLog();
            errorLog.setId(Tool.uuid());
            errorLog.setBusinessName("app报案入库");
            errorLog.setMethodName("/api/claimCaseApi/appInsertClaimCase");
            errorLog.setComeFrom(2);// 他方
            errorLog.setReqType(1);// 外部请求
            errorLog.setStatus(0); // 未处理
            errorLog.setReqData(json);
            errorLog.setDescription(e.getMessage());
            errorLog.setCreator("-1");
            errorLog.setCreateTime(new Date());
            errorLogService.insertSelective(errorLog);

            return JsonBizTool.genJson(RetEnum.FAIL);
        }
        return JsonBizTool.genJson(RetEnum.SUCCESS);
    }

    @RequestMapping(value = "replenishMaterial")
    @ResponseBody
    public String replenishMaterial(@RequestBody String json) {
        logger.info(">>>>>>>>>方法/api/claimCaseApi/replenishMaterial，开始处理app补材任务：{}", json);
        try {
            ClaimCaseReq vo = JSONObject.parseObject(json, ClaimCaseReq.class);

            if (StringUtils.isBlank(vo.getReportNo())) {
                logger.error(">>>>>>>>>方法/api/claimCaseApi/replenishMaterial，缺少参数，json：{}", json);
                return JsonBizTool.genJson(ExRetEnum.CASE_ID_NULL);
            }
            ClaimCase claimCase = claimCaseService.selectByCaseNo(vo.getReportNo());
            if (claimCase == null){
                logger.info(">>>>>>>>>方法/api/claimCaseApi/replenishMaterial，接收的报案号{}不存在", vo.getReportNo());
                return JsonBizTool.genJson(ExRetEnum.CASE_ID_NULL);
            }
            if ("HMRB".equals(claimCase.getInsCode())) {
                claimCaseService.replenishMaterial4HM(vo);
            } else {
                claimCaseService.replenishMaterial(vo);
            }

            logger.info(">>>>>>>>>方法/api/claimCaseApi/replenishMaterial，{}补材成功", claimCase.getClaimCaseNo());
        } catch (Exception e) {
            logger.error(">>>>>>>>>方法/api/claimCaseApi/replenishMaterial，系统异常，json：{}", json);
            e.printStackTrace();

            ErrorLog errorLog = new ErrorLog();
            errorLog.setId(Tool.uuid());
            errorLog.setBusinessName("app补充材料");
            errorLog.setMethodName("/api/claimCaseApi/replenishMaterial");
            errorLog.setComeFrom(2);// 他方
            errorLog.setReqType(1);// 外部请求
            errorLog.setStatus(0); // 未处理
            errorLog.setReqData(json);
            errorLog.setDescription(e.getMessage());
            errorLog.setCreator("-1");
            errorLog.setCreateTime(new Date());
            errorLogService.insertSelective(errorLog);

            return JsonBizTool.genJson(RetEnum.FAIL);
        }
        return JsonBizTool.genJson(RetEnum.SUCCESS);
    }

    @RequestMapping(value = "appCloseCase")
    @ResponseBody
    public String appCloseCase(@RequestBody String json) {
        logger.info(">>>>>>>>>>>>>开始请求appCloseCase接口，json：{}", json);

        try{
            JSONObject jsonObject = JSONObject.parseObject(json);
            if (StringUtils.isBlank(jsonObject.getString("reportNo"))) {
                logger.error(">>>>>>>>>>>>>请求appCloseCase接口>>>>>缺少参数，json：{}", json);
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
            }

            ClaimCase claimCase = claimCaseService.selectByCaseNo(jsonObject.getString("reportNo"));
            if (claimCase == null) {
                logger.error(">>>>>>>>>>>>>请求appCloseCase接口>>>>>报案号不存在，json：{}", json);
                return JsonBizTool.genJson(ExRetEnum.CASE_ID_NULL);
            }
            claimCaseService.closeReport(claimCase.getId());
            // 盒马和京东家政不回传饿了么
            if (!"HMRB".equals(claimCase.getInsCode()) && !"JZTB".equals(claimCase.getInsCode())){
                // 回传 饿了么 APP撤案（9）
                claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCase.getId());
                String reqData = JsonTool.genByFastJson(claimCase);
                JSONObject reqJson = JSONObject.parseObject(reqData);
                reqJson.put("auditStatus", 9);
//            amqpTemplate.convertAndSend(QueueName.KD_SCALLION_STATUS_BACK, JsonTool.genByFastJson(reqJson));
                // 延迟10秒回退饿了么
                amqpTemplate.convertAndSend(QueueName.KD_SCALLION_STATUS_BACK_TTL_EXCHANGE, QueueName.KD_SCALLION_STATUS_BACK_TTL, JsonTool.genByFastJson(reqJson), message -> {
                    message.getMessageProperties().setExpiration(String.valueOf(10L * 1000L));
                    return message;
                });
            }
           /* if ("RB".equals(claimCase.getInsCode())) {
                // 异步推送人保理赔案件取消
                Map<String, String> dataMap = new HashMap<>();
                dataMap.put("claimCaseId", claimCase.getId());
                ClaimDataSup claimDataSup = new ClaimDataSup();
                claimDataSup.setMode(9);
                claimDataSup.setReqJsonStr(JsonTool.genByFastJson(dataMap));
                amqpTemplate.convertAndSend(QueueName.KD_CLAIM_DATA_PUSH_PICC_DELAY_TTL_EXCHANGE, QueueName.KD_CLAIM_DATA_PUSH_PICC_DELAY_TTL, claimDataSup, message -> {
                    message.getMessageProperties().setExpiration(String.valueOf(10L * 1000L));
                    return message;
                });
            }*/

        } catch (Exception e) {
            ErrorLog errorLog = new ErrorLog();
            errorLog.setId(Tool.uuid());
            errorLog.setBusinessName("大葱本地关闭报案");
            errorLog.setMethodName("/api/claimCaseApi/appCloseCase");
            errorLog.setComeFrom(2);// 他方
            errorLog.setReqType(1);// 外部请求
            errorLog.setStatus(0); // 未处理
            errorLog.setReqData(json);
            errorLog.setDescription(e.getMessage());
            errorLog.setCreator("-1");
            errorLog.setCreateTime(new Date());
            errorLogService.insertSelective(errorLog);

            logger.error(">>>>>>>>>>>>>请求appCloseCase接口>>>>>系统异常，请联系管理员，json：{}，msg：{}", json, e.getMessage());
            e.printStackTrace();

            return JsonBizTool.genJson(RetEnum.FAIL);
        }
        return JsonBizTool.genJson(RetEnum.SUCCESS);
    }

    /**
     *  理赔状态回传  --通用批量（单次）
     */
    @PostMapping("statusBackListenerBatch")
    @ResponseBody
    public String statusBackListener_batch(@RequestBody String json) {
        logger.info(">>>>>>>>>>>>>开始请求statusBackListener_batch接口，json:{}", json);
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            List<String> claimCaseNoList = JSONObject.parseArray(jsonObject.getString("claimCaseNoList"), String.class);
            if (CollectionUtils.isNotEmpty(claimCaseNoList)) {
                for (String s : claimCaseNoList) {
                    ClaimCase claimCase = claimCaseService.selectByCaseNo(s);
                    if (claimCase == null) {
                        logger.error("案件号{}不存在", s);
                        continue;
                    }
                    String reqData = JsonTool.genByFastJson(claimCase);
                    JSONObject reqJson = JSONObject.parseObject(reqData);
                    if (jsonObject.getInteger("auditStatus") != null) {
                        reqJson.put("auditStatus", jsonObject.getInteger("auditStatus"));
                    }
                    amqpTemplate.convertAndSend(QueueName.KD_SCALLION_STATUS_BACK, JsonTool.genByFastJson(reqJson));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(">>>>>>>>>>>>>请求statusBackListener_batch接口>>>>>系统异常，请联系管理员，json：{}，msg：{}", json, e.getMessage());
            return JsonBizTool.genJson(ExRetEnum.ERROR_SYSTEMERROR);
        }
        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

    @RequestMapping(value={"setClaimCaseLabel" })
    @ResponseBody
    public String setClaimCaseLabel( @RequestBody String json) {
        logger.info(">>>>>>>>>>>>>开始请求setClaimCaseLabel接口，json：{}", json);
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            if(StringUtils.isBlank(jsonObject.getString("claimCaseId"))){
                logger.error(">>>>>>>>>>>>>请求setClaimCaseLabel接口>>>>>缺少参数，json：{}", json);
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
            }
            ClaimCase claimCase ;
            claimCase = claimCaseService.selectByPrimaryKeyBySql(jsonObject.getString("claimCaseId"));
            if(claimCase == null ){
                logger.error(">>>>>>>>>>>>>请求setClaimCaseLabel接口>>>>>报案号不存在，json：{}", json);
                return JsonBizTool.genJson(ExRetEnum.CASE_ID_NULL);
            }
            PolicyPerson policyPerson = policyPersonService.selectByPrimaryKey(claimCase.getPolicyPersonId());
            Area province = areaService.findByName(claimCase.getProvince(), "-1");
            Area city = areaService.findByName(claimCase.getCity(), province.getId().toString());
            Area district = areaService.findByName(claimCase.getDistrict(), city.getId().toString());
            String key = new StringBuilder().append(policyPerson.getSubsidiaryAgentName()).append("+").append(province.getCode()).append("+").append(city.getCode()).append("+").append(district.getCode()).toString();
            Map<String, String> map = RedisTool3.hgetAll(new StringBuilder().append(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString()).append("SUBSIDIARY_LABEL").toString());
            Map<String, String> map1 = RedisTool3.hgetAll(new StringBuilder().append(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString()).append("AREA_SPECIAL_LABEL").toString());
            Map<String, String> map2 = RedisTool3.hgetAll(new StringBuilder().append(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString()).append("INS_CODE_LABEL").toString());
            String value = map.get(key);
            String value1 = map1.get(province.getCode());
            String value2 = map2.get(claimCase.getInsCode());
            if(value != null ){
//                claimCase.setLabel(ClaimCaseLabelEnum.ACX017.getCode());
                if(claimCase.getLabel() != null){
                    claimCase.setLabel(claimCase.getLabel()+","+value);
                }else{
                    claimCase.setLabel(value);
                }
                claimCaseService.updateByPrimaryKeySelective(claimCase);
            }
            claimCase = claimCaseService.selectByPrimaryKeyBySql(jsonObject.getString("claimCaseId"));
            if(value1 != null ){
                // 判断InsCode是否存在
                if (StringUtils.isNotBlank(value2)){
                    if(claimCase.getLabel() != null){
                        claimCase.setLabel(claimCase.getLabel()+","+value1);
                    }else{
                        claimCase.setLabel(value1);
                    }
                    claimCaseService.updateByPrimaryKeySelective(claimCase);
                }
            }
            return JsonBizTool.genJson(RetEnum.SUCCESS);
        } catch (Exception e) {
            logger.error(">>>>>>>>>方法/api/claimCaseApi/setClaimCaseLabel，系统异常，json：{}", json);
            e.printStackTrace();
            ErrorLog errorLog ;
            errorLog = new ErrorLog();
            errorLog.setId(Tool.uuid());
            errorLog.setBusinessName("修改label入库");
            errorLog.setMethodName("/api/claimCaseApi/setClaimCaseLabel");
            errorLog.setComeFrom(Integer.valueOf(1));
            errorLog.setReqType(Integer.valueOf(0));
            errorLog.setStatus(Integer.valueOf(0));
            errorLog.setReqData(json);
            errorLog.setDescription(e.getMessage());
            errorLog.setCreator("-1");
            errorLog.setCreateTime(new Date());
            errorLogService.insertSelective(errorLog);
            return JsonBizTool.genJson(RetEnum.FAIL);
        }
    }

    @RequestMapping(value = "hmInsertClaimCase")
    @ResponseBody
    public String hmInsertClaimCase(@RequestBody String json) {
        logger.info(">>>>>>>>>方法/api/claimCaseApi/hmInsertClaimCase开始处理app报案任务，json：{}", json);
        String claimCaseNo = "";
        try {
            HmClaimCaseReq claimCaseReq = JSONObject.parseObject(json, HmClaimCaseReq.class);
            claimCaseNo = claimCaseReq.getClaimCaseNo();
            // 重复报案校验
            if (RedisTool3.setnx(claimCaseReq.getInsCode() + "-INNER-" + claimCaseReq.getPartnerClaimNo(), claimCaseReq.getClaimCaseNo()) == 1L) {
                RedisTool3.expire(claimCaseReq.getInsCode() + "-INNER-" + claimCaseReq.getPartnerClaimNo(), 60 * 60 * 24);
            } else {
                logger.error(">>>>>>>>>>该案件已存在,重复报案,partnerClaimNo:{}, insCode:{}", claimCaseReq.getPartnerClaimNo(), claimCaseReq.getInsCode());
                throw new Exception("盒马内部理赔单号{" + claimCaseReq.getPartnerClaimNo() + "}已存在，重复报案");
            }
            ClaimCase claimCase = claimCaseService.selectByPartnerClaimNoAndInsCode(claimCaseReq.getPartnerClaimNo(), claimCaseReq.getInsCode());
            if (claimCase != null) {
                logger.error(">>>>>>>>>>该案件已存在, 重复报案, partnerClaimNo:{}, insCode:{}", claimCaseReq.getPartnerClaimNo(), claimCaseReq.getInsCode());
                throw new Exception("盒马内部理赔单号{" + claimCaseReq.getPartnerClaimNo() + "}已存在，重复报案");
            }

            //查保单号是否存在 存在出险人信息为保全信息
            PolicyPerson policyPerson = policyPersonService.findByCustomerPolicyNoBS(claimCaseReq.getPolicyNo(), claimCaseReq.getInsCode());
            if (policyPerson == null) {
                logger.error(">>>>>>>>>方法/api/claimCaseApi/hmInsertClaimCase，保全{}信息不存在", claimCaseReq.getPolicyNo());
                ErrorLog errorLog = new ErrorLog();
                errorLog.setId(Tool.uuid());
                errorLog.setBusinessName("app报案入库");
                errorLog.setMethodName("/api/claimCaseApi/hmInsertClaimCase");
                errorLog.setComeFrom(1);
                errorLog.setReqType(0);
                errorLog.setStatus(0);
                errorLog.setReqData(json);
                errorLog.setDescription("报案时保全信息不存在，保单号{"+ claimCaseReq.getPolicyNo()+"}");
                errorLog.setCreator("-1");
                errorLog.setCreateTime(new Date());
                errorLogService.insertSelective(errorLog);
            }
            claimCaseReq.setClaimCaseId(Tool.uuid());
            claimCaseService.insertReportHm(claimCaseReq);
            amqpTemplate.convertAndSend(QueueName.KD_SET_CLAIM_CAST_LABEL, claimCaseReq.getClaimCaseId());

           /* try {
                Thread.sleep(1000L);
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 发送短信
            Map<String, Object> smsMap = new TreeMap<>();
            smsMap.put("claimCaseId", claimCaseReq.getClaimCaseId());
            smsMap.put("type","01");
            amqpTemplate.convertAndSend(QueueName.KD_ROASTDUCK_RABBIT_SEND_MESSAGE, JsonTool.genByFastJson(smsMap));*/

            // 回传 盒马 电子资料审核中(2)
            claimCase = claimCaseService.selectByPrimaryKeyBySql(claimCaseReq.getClaimCaseId());
            String reqData = JsonTool.genByFastJson(claimCase);
            JSONObject reqJson = JSONObject.parseObject(reqData);
            reqJson.put("auditStatus", 2);
//            amqpTemplate.convertAndSend(QueueName.KD_SCALLION_STATUS_BACK, JsonTool.genByFastJson(reqJson));
            // 延迟10秒回退饿了么
            amqpTemplate.convertAndSend(QueueName.KD_SCALLION_STATUS_BACK_TTL_EXCHANGE, QueueName.KD_SCALLION_STATUS_BACK_TTL, JsonTool.genByFastJson(reqJson), message -> {
                message.getMessageProperties().setExpiration(String.valueOf(10L * 1000L));
                return message;
            });

            //TODO 临时增加，下发邮件
            amqpTemplate.convertAndSend(QueueName.KD_CUISINE_POLICY_EMAIL, new Email4AcceptCaseNoticesSup(claimCaseReq.getClaimCaseNo()));

            logger.info(">>>>>>>>>方法/api/claimCaseApi/hmInsertClaimCase，入库成功，json：{}", json);
        }
        catch (Exception e) {
            logger.error(">>>>>>>>>方法/api/claimCaseApi/hmInsertClaimCase，系统异常，json：" + json);
            e.printStackTrace();

            // 发送钉钉告警消息
            sendDingTalk(claimCaseNo + "-盒马人保报案入库异常");
            // 案件入库异常增加邮箱发送
            String email = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "CLAIM_CASE_INSERT", "ERROR_EMAIL");
            if (StringUtils.isBlank(email)) {
                email = "<EMAIL>";
            }
            EmailToolV3.sendHtmlEmail4Eai_new("盒马人保案件入库异常 kd-hmrb", json, email, "mk", null);

            ErrorLog errorLog = new ErrorLog();
            errorLog.setId(Tool.uuid());
            errorLog.setBusinessName("app报案入库");
            errorLog.setMethodName("/api/claimCaseApi/hmInsertClaimCase");
            errorLog.setComeFrom(2);// 他方
            errorLog.setReqType(1);// 外部请求
            errorLog.setStatus(0); // 未处理
            errorLog.setReqData(json);
            errorLog.setDescription(e.getMessage());
            errorLog.setCreator("-1");
            errorLog.setCreateTime(new Date());
            errorLogService.insertSelective(errorLog);

            return JsonBizTool.genJson(RetEnum.FAIL);
        }
        return JsonBizTool.genJson(RetEnum.SUCCESS);
    }

    @RequestMapping(value = "findStutas")
    @ResponseBody
    public String findStutas (@RequestBody String json){
        logger.info(">>>>>>>>>>>>>开始请求findStutas接口，json：{}", json);
        HashMap<String, Object> resMap = new HashMap<>();
        try{
            ClaimParamReq claimParamReq = JSONObject.parseObject(json, ClaimParamReq.class);
            JSONObject claimCaseJson = JSONObject.parseObject(json);
            PageInfo<JSONObject> page = claimCaseService.caseSearchRB(claimParamReq);

            if (page != null) {
                List<JSONObject> pageList = page.getList();
                System.out.println(pageList.get(0).toString());
                JSONObject jsonObject = pageList.get(0);
//                Map<String, Object> dataMap = new HashMap<>();
                resMap.put("partnerClaimNo",claimCaseJson.getString("partnerClaimNo"));
                String auditResult = jsonObject.getString("auditResult");

                //如果状态文档为空，则根据状态得到相应的状态表

                JSONObject auditResultToJsonObject = JSONObject.parseObject(auditResult);
                HashMap<String, Object> auditResultMap = new HashMap<>();
                auditResultMap.put("auditStatus",auditResultToJsonObject.getString("auditStatus"));
                //状态文档不一定存在，所以需要判断，不为空则直接用源数据
                String auditDesc = auditResultToJsonObject.getString("auditDesc");
                if (auditDesc != null){
                    auditResultMap.put("auditDesc",auditDesc);
                }
                String claimConclusion = auditResultToJsonObject.getString("claimConclusion");
                if (claimConclusion != null){
                    auditResultMap.put("claimConclusion",claimConclusion);
                }
                String payAmount = auditResultToJsonObject.getString("payAmount");
                if (payAmount != null){
                    auditResultMap.put("payAmount",payAmount);
                }
                auditResultMap.put("auditDate",auditResultToJsonObject.getString("auditDate"));
                resMap.put("auditResult",auditResultMap);
                resMap.put("reportNo",jsonObject.getString("reportNo"));
            }else {
                return null;
            }


        }catch (Exception e){
            logger.error(">>>>>>>>>方法/api/claimCaseApi/findStutas，系统异常，json：" + json);
            e.printStackTrace();
            ErrorLog errorLog = new ErrorLog();
            errorLog.setId(Tool.uuid());
            errorLog.setBusinessName("理赔状态查询 ");
            errorLog.setMethodName("/api/claimCaseApi/findStutas");
            errorLog.setComeFrom(2);// 他方
            errorLog.setReqType(1);// 外部请求
            errorLog.setStatus(0); // 未处理
            errorLog.setReqData(json);
            errorLog.setDescription(e.getMessage());
            errorLog.setCreator("-1");
            errorLog.setCreateTime(new Date());
            errorLogService.insertSelective(errorLog);
            HashMap<String, Object> errorMap = new HashMap<>();
            errorMap.put("msg","查询异常");
            return JsonBizTool.genJson(errorMap);
        }
        return  JsonBizTool.genJson(resMap);
    }
    @RequestMapping(value = "closeCase")
    @ResponseBody
    public String closeCase(@RequestBody String json){
        logger.info(">>>>>>>>>>>>>开始请求大葱closeCase接口，json：{}", json);
        try {
            if (StringUtils.isBlank(json)){
                logger.info(">>>>>>>>>>>>>参数为空，json：{}", json);
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
            }
            ClaimCase claimCase = claimCaseService.selectByCaseNo(json);
            claimCaseService.closeReport(claimCase.getId());
        }catch (Exception e){
            logger.error(">>>>>>>>>方法/api/claimCaseApi/closeCase，系统异常，json：" + json);
            e.printStackTrace();
            ErrorLog errorLog = new ErrorLog();
            errorLog.setId(Tool.uuid());
            errorLog.setBusinessName("理赔状态查询 ");
            errorLog.setMethodName("/api/claimCaseApi/closeCase");
            errorLog.setComeFrom(2);// 他方
            errorLog.setReqType(1);// 外部请求
            errorLog.setStatus(0); // 未处理
            errorLog.setReqData(json);
            errorLog.setDescription(e.getMessage());
            errorLog.setCreator("-1");
            errorLog.setCreateTime(new Date());
            errorLogService.insertSelective(errorLog);

            return JsonBizTool.genJson(RetEnum.FAIL);
        }
        return "1";
    }

    /**
     * 发送钉钉案件入库告警消息
     * @param content
     */
    private void sendDingTalk(String content) {
        try {
            String uuid = Tool.uuid();
            String data = "# TYPE app_report_case counter\n" +
                    "app_report_case{description=\"" + content + "\",key=\"" + uuid + "\"}" + 100 + "\n";
            String url = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "CLAIM_CASE_INSERT", "DING_TALK_URL");
            url = url + uuid;
            HttpToolV3.callApiByPostStr(true, url, data);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
