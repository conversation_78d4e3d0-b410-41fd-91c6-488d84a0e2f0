package kd.stinkyTofu.conf;

import kd.main.common.QueueName;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * @Description: rabbitmq配置文件
 */
@Configuration
public class RabbitConfig {

    private static final Integer DEFAULT_CONCURRENT = 10;

    @Bean
    public Queue KD_POLICY_PUSH_INS_TEMP() {
        return new Queue(QueueName.KD_POLICY_PUSH_INS_TEMP, true);
    }

    @Bean
    public Queue KD_POLICY_PUSH_INS_TEMP_JOB() {
        return new Queue(QueueName.KD_POLICY_PUSH_INS_TEMP_JOB, true);
    }

    @Bean
    public Queue KD_LOCAL_POLICY_DEAL_TB() { return new Queue(QueueName.KD_LOCAL_POLICY_DEAL_TB, true); }

    @Bean
    public Queue KD_PUSH_COMMON_PROPOSAL() { return new Queue(QueueName.KD_PUSH_COMMON_PROPOSAL, true); }

//    @Bean
//    public Queue KD_PUSH_IMAGE_UPLOAD() { return new Queue(QueueName.KD_PUSH_IMAGE_UPLOAD, true); }

    @Bean
    public Queue E_SIGN_PDF_TO_PHOTO() { return new Queue(QueueName.E_SIGN_PDF_TO_PHOTO, true); }

    @Bean
    public Queue E_SIGN_AGREEMENT_WITHDRAWN (){
        return new Queue(QueueName.E_SIGN_AGREEMENT_WITHDRAWN, true);
    }

    /**
     * 错误日志入库
     * @return
     */
    @Bean
    public Queue KD_PUSH_ERROR_LOG_QUEUE() {
        return new Queue(QueueName.KD_PUSH_ERROR_LOG_QUEUE, true);
    }

    /**
     * 案件状态流转（新增）
     * @return
     */
    @Bean
    public Queue KD_CLAIM_CASE_STATE_TRANSITION_ADD() {
        return new Queue(QueueName.KD_CLAIM_CASE_STATE_TRANSITION_ADD, true);
    }

    /**
     * 案件状态流转（组装）
     * @return
     */
    @Bean
    public Queue KD_CLAIM_CASE_STATE_TRANSITION_PACK() {
        return new Queue(QueueName.KD_CLAIM_CASE_STATE_TRANSITION_PACK, true);
    }

    /**
     * e签宝操作日志添加
     * @return
     */
    @Bean
    public Queue KD_SIGN_TEMPLATE_OPERATE_LOG_ADD() {
        return new Queue(QueueName.KD_SIGN_TEMPLATE_OPERATE_LOG_ADD, true);
    }

    @Bean("customContainerFactory")
    public SimpleRabbitListenerContainerFactory containerFactory(SimpleRabbitListenerContainerFactoryConfigurer configurer,
                                                                 ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConcurrentConsumers(DEFAULT_CONCURRENT);
        factory.setMaxConcurrentConsumers(DEFAULT_CONCURRENT);
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    /**
     * 普通消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageDirect4ImgUpload() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_PUSH_IMAGE_UPLOAD_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 延时消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageTtlDirect4ImgUpload() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_PUSH_IMAGE_UPLOAD_TTL_EXCHANGE)
                .durable(true)
                .build();
    }


    @Bean
    public Queue messageQueue4ImgUpload() {
        return new Queue(QueueName.KD_PUSH_IMAGE_UPLOAD);
    }




    /**
     * TTL消息队列配置
     *
     * @return
     */
    @Bean
    Queue messageTtlQueue4ImgUpload() {
        return QueueBuilder
                .durable(QueueName.KD_PUSH_IMAGE_UPLOAD_TTL)
                // 配置到期后转发的交换
                .withArgument("x-dead-letter-exchange", QueueName.KD_PUSH_IMAGE_UPLOAD_EXCHANGE)
                // 配置到期后转发的路由键
                .withArgument("x-dead-letter-routing-key", QueueName.KD_PUSH_IMAGE_UPLOAD)
                .build();
    }


    /**
     * 普通队列和普通交换机的绑定-routekey
     *
     * @param messageDirect4ImgUpload 消息中心交换配置
     * @param messageQueue4ImgUpload  消息中心队列
     * @return
     */
    @Bean
    Binding messageBinding4ImgUpload(DirectExchange messageDirect4ImgUpload, Queue messageQueue4ImgUpload) {
        return BindingBuilder
                .bind(messageQueue4ImgUpload)
                .to(messageDirect4ImgUpload)
                .with(QueueName.KD_PUSH_IMAGE_UPLOAD);
    }

    /**
     * ttl队列和ttl交换机的绑定-routekey
     *
     * @param messageTtlQueue4ImgUpload
     * @param messageTtlDirect4ImgUpload
     * @return
     */
    @Bean
    public Binding messageTtlBinding4ImgUpload(Queue messageTtlQueue4ImgUpload, DirectExchange messageTtlDirect4ImgUpload) {
        return BindingBuilder
                .bind(messageTtlQueue4ImgUpload)
                .to(messageTtlDirect4ImgUpload)
                .with(QueueName.KD_PUSH_IMAGE_UPLOAD_TTL);
    }


    /**
     * 普通消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageDirect4CommonSubmitUW() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_PUSH_COMMON_SUBMIT_UW_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 延时消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageTtlDirect4CommonSubmitUW() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_PUSH_COMMON_SUBMIT_UW_TTL_EXCHANGE)
                .durable(true)
                .build();
    }


    @Bean
    public Queue messageQueue4CommonSubmitUW() {
        return new Queue(QueueName.KD_PUSH_COMMON_SUBMIT_UW);
    }




    /**
     * TTL消息队列配置
     *
     * @return
     */
    @Bean
    Queue messageTtlQueue4CommonSubmitUW() {
        return QueueBuilder
                .durable(QueueName.KD_PUSH_COMMON_SUBMIT_UW_TTL)
                // 配置到期后转发的交换
                .withArgument("x-dead-letter-exchange", QueueName.KD_PUSH_COMMON_SUBMIT_UW_EXCHANGE)
                // 配置到期后转发的路由键
                .withArgument("x-dead-letter-routing-key", QueueName.KD_PUSH_COMMON_SUBMIT_UW)
                .build();
    }


    /**
     * 普通队列和普通交换机的绑定-routekey
     *
     * @param messageDirect4CommonSubmitUW 消息中心交换配置
     * @param messageQueue4CommonSubmitUW  消息中心队列
     * @return
     */
    @Bean
    Binding messageBinding4CommonSubmitUW(DirectExchange messageDirect4CommonSubmitUW, Queue messageQueue4CommonSubmitUW) {
        return BindingBuilder
                .bind(messageQueue4CommonSubmitUW)
                .to(messageDirect4CommonSubmitUW)
                .with(QueueName.KD_PUSH_COMMON_SUBMIT_UW);
    }

    /**
     * ttl队列和ttl交换机的绑定-routekey
     *
     * @param messageTtlQueue4CommonSubmitUW
     * @param messageTtlDirect4CommonSubmitUW
     * @return
     */
    @Bean
    public Binding messageTtlBinding4CommonSubmitUW(Queue messageTtlQueue4CommonSubmitUW, DirectExchange messageTtlDirect4CommonSubmitUW) {
        return BindingBuilder
                .bind(messageTtlQueue4CommonSubmitUW)
                .to(messageTtlDirect4CommonSubmitUW)
                .with(QueueName.KD_PUSH_COMMON_SUBMIT_UW_TTL);
    }

}
